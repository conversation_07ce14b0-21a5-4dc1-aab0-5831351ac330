package com.youying.web.controller.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wechat.pay.java.service.payments.model.Transaction;
import com.wechat.pay.java.service.payments.model.Transaction.TradeStateEnum;
import com.youying.common.core.domain.entity.Portfolio;
import com.youying.common.core.domain.entity.UserOrder;
import com.youying.common.enums.Enums.BadgeTypeFlag;
import com.youying.common.enums.Enums.OrderQueryType;
import com.youying.common.enums.Enums.PayStatusFlag;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.common.pay.PayInfo;
import com.youying.common.pay.PaymentContext;
import com.youying.common.pay.PaymentStrategy;
import com.youying.common.pay.WechatPayment;
import com.youying.system.service.PortfolioService;
import com.youying.system.service.UserOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 定时任务
 *
 * <AUTHOR>
 * @since 2023-07-03
 */
@Slf4j
@Service
public class TaskController {
    @Autowired
    private UserOrderService userOrderService;
    @Autowired
    private PortfolioService portfolioService;

    /**
     * 发送提前预约通知
     */
    @Transactional
    @Scheduled(cron = "0 0/1 * * * ? ")
    public void updateMessage() {
        List<UserOrder> userOrderList = userOrderService.findUserOrderNotPay();
        if (CollectionUtils.isNotEmpty(userOrderList)) {
            List<UserOrder> updateUserOrderList = new ArrayList<>();
            PaymentContext context = new PaymentContext();
            PaymentStrategy weChatPayment = new WechatPayment();
            context.setPaymentStrategy(weChatPayment);
            for (UserOrder userOrder : userOrderList) {
                PayInfo payInfo = context.findPayInfo(userOrder.getOrderNo(), OrderQueryType.ORDER_NO.getCode());
                Transaction transaction = payInfo.getTransaction();
                // 未支付，直接取消
                if (!transaction.getTradeState().equals(TradeStateEnum.SUCCESS)) {
                    userOrder.setPayStatus(PayStatusFlag.CANCEL.getCode());
                    updateUserOrderList.add(userOrder);
                    continue;
                }
                long orderCount = userOrderService.count(new LambdaQueryWrapper<UserOrder>()
                        .eq(UserOrder::getPortfolioId, userOrder.getPortfolioId())
                        .eq(UserOrder::getBadgeType, BadgeTypeFlag.ELECTRONIC_TICKET.getCode())
                        .ne(UserOrder::getPayStatus, PayStatusFlag.CANCEL.getCode()));
                Portfolio portfolio = portfolioService.getById(userOrder.getPortfolioId());
                // 判断藏品是否全部领取
                if (portfolio.getIssuedQuantity() < orderCount + 1) {
                    userOrder.setPayStatus(PayStatusFlag.REFUND.getCode());
                    userOrder.setRefundPrice(userOrder.getPayPrice());
                    userOrder.setRefundTime(new Date());
                    userOrder.setRefundStatus(StatusFlag.OK.getCode());
                    updateUserOrderList.add(userOrder);
                    // 退还金额
                } else {
                    userOrderService.callback(transaction);
                }
            }
            if (CollectionUtils.isNotEmpty(updateUserOrderList)) {
                for (UserOrder userOrder : updateUserOrderList) {
                    userOrderService.updateUserOrder(userOrder);
                }
            }
        }
    }
}
