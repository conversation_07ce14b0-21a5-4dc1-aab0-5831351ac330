package com.youying.web.controller;

import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.UserPushCollection;
import com.youying.common.core.page.PageDomain;
import com.youying.common.core.page.TableList;
import com.youying.system.domain.userpushcollection.UserPushCollectionResponse;
import com.youying.system.service.UserPushCollectionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户推送藏品表
 *
 * <AUTHOR>
 * @since 2023-07-02
 */
@RestController
@RequestMapping("/userPushCollection")
public class UserPushCollectionController extends BaseController {

    @Autowired
    private UserPushCollectionService userPushCollectionService;

    /**
     * 查询用户推送藏品表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<UserPushCollectionResponse>> listByPage(@RequestBody PageDomain pageDomain) {
        startPage(pageDomain);
        return R.ok(getTableList(userPushCollectionService.listByPage()));
    }

    /**
     * 查询用户推送藏品表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<UserPushCollection> details(Long id) {
        return R.ok(userPushCollectionService.getById(id));
    }

}

