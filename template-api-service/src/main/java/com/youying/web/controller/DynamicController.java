package com.youying.web.controller;

import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.page.TableList;
import com.youying.system.domain.dynamic.DynamicRequest;
import com.youying.system.domain.dynamic.DynamicResponse;
import com.youying.system.service.DynamicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 剧目剧场动态表
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@RestController
@RequestMapping("/dynamic")
public class DynamicController extends BaseController {

    @Autowired
    private DynamicService dynamicService;

    /**
     * 查询剧目剧场动态表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<DynamicResponse>> listByPage(@RequestBody DynamicRequest request) {
        startPage(request);
        return R.ok(getTableList(dynamicService.listByPage(request)));
    }

    /**
     * 查询剧目剧场动态表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<DynamicResponse> details(Long id, Long userId) {
        return R.ok(dynamicService.details(id, userId));
    }
}

