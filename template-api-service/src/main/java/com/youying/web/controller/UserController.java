package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.constant.CacheConstants;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.RankMedalInfo;
import com.youying.common.core.domain.entity.User;
import com.youying.common.core.redis.RedisCache;
import com.youying.common.enums.BusinessType;
import com.youying.common.utils.SecurityUtils;
import com.youying.common.utils.StringUtils;
import com.youying.system.domain.user.ResetPwdRequest;
import com.youying.system.domain.user.UpdatePhoneRequest;
import com.youying.system.domain.user.UpdatePwdRequest;
import com.youying.system.domain.user.UserResponse;
import com.youying.system.service.RankMedalInfoService;
import com.youying.system.service.UserService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 用户表
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@RestController
@RequestMapping("/user")
public class UserController extends BaseController {
    @Autowired
    private UserService userService;
    @Autowired
    private RankMedalInfoService rankMedalInfoService;
    @Autowired
    private RedisCache redisCache;

    /**
     * 获取用户信息详情
     *
     * @return
     */
    @GetMapping(value = "/getUserInfo")
    public R<UserResponse> getUserInfo() {
        UserResponse res = new UserResponse();
        User user = userService.getById(SecurityUtils.getUserId());
        RankMedalInfo rankMedalInfo = rankMedalInfoService.getById(user.getRankMedalInfoId());
        if (rankMedalInfo != null) {
            res.setRankMedalColor(rankMedalInfo.getColor());
            res.setRankMedalName(rankMedalInfo.getRankMedalName());
            res.setRankMedalLevel(rankMedalInfo.getName());
        }
        BeanUtils.copyProperties(user, res);
        String ipaddr = SecurityUtils.getLoginUser().getLoginLocation();
        res.setIpaddr(ipaddr);
        return R.ok(res);
    }

    /**
     * 修改用户密码（旧密码）
     *
     * @return
     */
    @PutMapping(value = "/updatePwd")
    @Log(title = "修改用户密码", businessType = BusinessType.UPDATE)
    public R<?> updatePwd(@Valid @RequestBody UpdatePwdRequest request) {
        User userInfo = SecurityUtils.getUser();
        if (!SecurityUtils.matchesPassword(request.getOldPassword(), userInfo.getPassword())) {
            return R.fail("密码输入错误");
        }
        userInfo.setPassword(SecurityUtils.encryptPassword(request.getNewPassword()));
        userService.updateById(userInfo);
        return R.ok();
    }

    /**
     * 修改用户密码（手机号码）
     *
     * @return
     */
    @PutMapping(value = "/updatePwdByPhone")
    @Log(title = "修改用户密码", businessType = BusinessType.UPDATE)
    public R<?> updatePwdByPhone(@Valid @RequestBody ResetPwdRequest request) {
        User userInfo = SecurityUtils.getUser();
        String cacheKey = CacheConstants.UPDATE_PWD_PHONE_KEY + userInfo.getPhone();
        String keyCode = redisCache.getCacheObject(cacheKey);
        if (StringUtils.isBlank(keyCode)) {
            return R.fail("验证码已失效");
        }

        if (!keyCode.equals(request.getCode())) {
            return R.fail("验证码错误");
        }

        userInfo.setPassword(SecurityUtils.encryptPassword(request.getNewPassword()));
        userService.updateById(userInfo);

        redisCache.deleteObject(cacheKey);

        return R.ok();
    }

    /**
     * 修改用户手机号码
     *
     * @return
     */
    @PutMapping(value = "/updatePhone")
    @Log(title = "修改用户手机号码", businessType = BusinessType.UPDATE)
    public R<?> updatePhone(@Valid @RequestBody UpdatePhoneRequest request) {
        User userInfo = SecurityUtils.getUser();
        String cacheKey = CacheConstants.UPDATE_NEW_PHONE_KEY + request.getPhone();
        String keyCode = redisCache.getCacheObject(cacheKey);
        if (StringUtils.isBlank(keyCode)) {
            return R.fail("验证码已失效");
        }

        if (keyCode.equals(request.getCode())) {
            return R.fail("验证码错误");
        }

        // 判断手机号码是否存在
        User user = userService.findUserByPhone(request.getPhone());
        if (user != null && !userInfo.getId().equals(user.getId())) {
            return R.fail("手机号码已存在");
        }

        userInfo.setPhone(request.getPhone());
        userService.updateById(userInfo);

        redisCache.deleteObject(cacheKey);
        return R.ok();
    }

    /**
     * 修改用户信息
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改用户", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody User user) {
        User userInfo = SecurityUtils.getUser();
        userInfo.setName(user.getName());
        userInfo.setSex(user.getSex());
        userInfo.setAvatar(user.getAvatar());
        userInfo.setRankMedalInfoId(user.getRankMedalInfoId());
        userInfo.setPersonalizedSignature(user.getPersonalizedSignature());
        userInfo.setFistLogin(user.getFistLogin());
        userService.updateById(userInfo);
        return R.ok();
    }

    /**
     * 注销用户
     *
     * @return
     */
    @DeleteMapping(value = "/logout")
    @Log(title = "注销", businessType = BusinessType.DELETE)
    public R<?> logout(String code) {
        // String cacheKey = CacheConstants.LOGOUT_PHONE_KEY + SecurityUtils.getUser().getPhone();
        // String keyCode = redisCache.getCacheObject(cacheKey);
        // if (StringUtils.isBlank(keyCode)) {
        //     throw new ServiceException("验证码已失效");
        // }
        // if (keyCode.equals(code)) {
        //     throw new ServiceException("验证码错误");
        // }
        userService.removeById(SecurityUtils.getUserId());
        return R.ok();
    }

}

