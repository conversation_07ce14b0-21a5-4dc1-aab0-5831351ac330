package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.AutoReply;
import com.youying.common.core.page.PageDomain;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.system.service.AutoReplyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * 剧目剧场自动回复表
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@RestController
@RequestMapping("/autoReply")
public class AutoReplyController extends BaseController {

    @Autowired
    private AutoReplyService autoReplyService;

    /**
     * 查询剧目剧场自动回复表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<AutoReply>> listByPage(@RequestBody PageDomain pageDomain) {
        startPage(pageDomain);
        return R.ok(getTableList(autoReplyService.list()));
    }


    /**
     * 查询剧目剧场自动回复表列表
     *
     * @return
     */
    @GetMapping(value = "/list")
    public R<List<AutoReply>> list() {
        return R.ok(autoReplyService.list());
    }

    /**
     * 查询剧目剧场自动回复表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<AutoReply> details(Long id) {
        return R.ok(autoReplyService.getById(id));
    }

    /**
     * 添加剧目剧场自动回复表
     *
     * @return
     */
    @PostMapping(value = "/add")
    @Log(title = "添加剧目剧场自动回复表数据", businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody AutoReply autoReply) {
        return R.ok(autoReplyService.save(autoReply));
    }

    /**
     * 修改剧目剧场自动回复表
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改剧目剧场自动回复表数据", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody AutoReply autoReply) {
        return R.ok(autoReplyService.updateById(autoReply));
    }

    /**
     * 删除剧目剧场自动回复表
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除剧目剧场自动回复表数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(autoReplyService.removeByIds(Arrays.asList(ids)));
        }
        return R.ok();
    }

}

