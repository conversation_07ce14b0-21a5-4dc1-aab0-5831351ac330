package com.youying.web.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.AdvertisingPicture;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.system.service.AdvertisingPictureService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 广告轮播图表
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@RestController
@RequestMapping("/advertisingPicture")
public class AdvertisingPictureController extends BaseController {

    @Autowired
    private AdvertisingPictureService advertisingPictureService;

    /**
     * 查询广告轮播图表列表
     *
     * @return
     */
    @GetMapping(value = "/list")
    public R<List<AdvertisingPicture>> list() {
        return R.ok(advertisingPictureService.list(new LambdaQueryWrapper<AdvertisingPicture>()
                .eq(AdvertisingPicture::getStatus, StatusFlag.OK.getCode())
                .orderByAsc(AdvertisingPicture::getSort)));
    }

}

