package com.youying.web.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.Issue;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.system.domain.issue.AddIssueRequest;
import com.youying.system.domain.issue.IssueRequest;
import com.youying.system.domain.issue.IssueResponse;
import com.youying.system.service.IssueService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 剧目剧场问答表
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@RestController
@RequestMapping("/issue")
public class IssueController extends BaseController {

    @Autowired
    private IssueService issueService;

    /**
     * 查询剧目剧场问答列表(父级)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<IssueResponse>> listByPage(@RequestBody IssueRequest request) {
        startPage(request);
        return R.ok(getTableList(issueService.listByPage(request)));
    }

    /**
     * 查询剧目剧场问答列表(子级)
     *
     * @return
     */
    @PostMapping(value = "/listByParentId")
    public R<TableList<IssueResponse>> listByParentId(@RequestBody IssueRequest request) {
        startPage(request);
        return R.ok(getTableList(issueService.listByParentId(request)));
    }

    /**
     * 查询剧目下问答条数
     *
     * @return
     */
    @GetMapping(value = "/findIssueCountByRepertoire")
    public R<?> findIssueCountByRepertoire(Long repertoireId) {
        return R.ok(issueService.count(new LambdaQueryWrapper<Issue>()
                .eq(Issue::getRepertoireId, repertoireId)
                .eq(Issue::getParentId, 0L)));
    }

    /**
     * 查询剧场下问答条数
     *
     * @return
     */
    @GetMapping(value = "/findIssueCountByTheater")
    public R<?> findIssueCountByTheater(Long theaterId) {
        return R.ok(issueService.count(new LambdaQueryWrapper<Issue>()
                .eq(Issue::getTheaterId, theaterId)
                .eq(Issue::getParentId, 0L)));
    }

    /**
     * 查询剧目剧场问答表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<IssueResponse> details(Long id) {
        long count = issueService.count(new LambdaQueryWrapper<Issue>()
                .eq(Issue::getParentId, id)
                .isNotNull(Issue::getContent));
        IssueResponse issueResponse = new IssueResponse();
        Issue issue = issueService.getById(id);
        BeanUtils.copyProperties(issue, issueResponse);
        issueResponse.setReplyCount(count);
        return R.ok(issueResponse);
    }

    /**
     * 添加剧目剧场问答表
     *
     * @return
     */
    @PostMapping(value = "/add")
    @Log(title = "添加剧目剧场问答表数据", businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody Issue issue) {
        return R.ok(issueService.add(issue));
    }

    /**
     * 回复剧目剧场问答表
     *
     * @return
     */
    @PostMapping(value = "/reply")
    @Log(title = "添加剧目剧场问答表数据", businessType = BusinessType.INSERT)
    public R<?> reply(@RequestBody AddIssueRequest request) {
        return R.ok(issueService.reply(request));
    }
}

