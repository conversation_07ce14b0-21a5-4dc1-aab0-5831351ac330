package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.CommentInfo;
import com.youying.common.enums.BusinessType;
import com.youying.common.utils.SecurityUtils;
import com.youying.system.service.CommentInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 评论点赞、踩详情表
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@RestController
@RequestMapping("/commentInfo")
public class CommentInfoController extends BaseController {

    @Autowired
    private CommentInfoService commentInfoService;

    /**
     * 添加评论点赞、踩、删除
     *
     * @return
     */
    @PostMapping(value = "/save")
    @Log(title = "添加评论点赞、踩详情表数据", businessType = BusinessType.UPDATE)
    public R<?> save(@RequestBody CommentInfo commentInfo) {
        commentInfo.setUserId(SecurityUtils.getUserId());
        return R.ok(commentInfoService.addOrUpdate(commentInfo));
    }

}

