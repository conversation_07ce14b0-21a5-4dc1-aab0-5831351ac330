package com.youying.web.controller;

import com.youying.common.annotation.Anonymous;
import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.DigitalAvatar;
import com.youying.common.core.page.PageDomain;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.system.domain.digitalavatar.DigitalAvatarResponse;
import com.youying.system.service.DigitalAvatarService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;

/**
 * 数字头像表
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@RestController
@RequestMapping("/digitalAvatar")
public class DigitalAvatarController extends BaseController {

    @Autowired
    private DigitalAvatarService digitalAvatarService;

    /**
     * 查询数字头像表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    @Anonymous
    public R<TableList<DigitalAvatarResponse>> listByPage(@RequestBody PageDomain pageDomain) {
        startPage(pageDomain);
        return R.ok(getTableList(digitalAvatarService.listByPage(pageDomain)));
    }

    /**
     * 查询数字头像表详情
     *
     * @return
     */
    @GetMapping(value = "/getDigitalAvatarInfo")
    @Anonymous
    public R<DigitalAvatarResponse> getDigitalAvatarInfo(Long portfolioId) {
        return R.ok(digitalAvatarService.getDigitalAvatarInfo(portfolioId));
    }

    /**
     * 查询数字头像表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<DigitalAvatar> details(Long id) {
        return R.ok(digitalAvatarService.getById(id));
    }

    /**
     * 添加数字头像表
     *
     * @return
     */
    @PostMapping(value = "/add")
    @Log(title = "添加数字头像表数据", businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody DigitalAvatar digitalAvatar) {
        return R.ok(digitalAvatarService.save(digitalAvatar));
    }

    /**
     * 修改数字头像表
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改数字头像表数据", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody DigitalAvatar digitalAvatar) {
        return R.ok(digitalAvatarService.updateById(digitalAvatar));
    }

    /**
     * 删除数字头像表
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除数字头像表数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(digitalAvatarService.removeByIds(Arrays.asList(ids)));
        }
        return R.ok();
    }

}

