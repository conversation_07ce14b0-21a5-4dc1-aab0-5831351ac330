package com.youying.web.controller.system;

import java.math.BigDecimal;
import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.youying.common.annotation.Anonymous;
import com.youying.common.constant.Constants;
import com.youying.common.core.domain.AjaxResult;
import com.youying.common.core.domain.entity.User;
import com.youying.common.core.domain.entity.UserSetting;
import com.youying.common.enums.Enums.DeleteFlag;
import com.youying.common.enums.Enums.SexFlag;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.common.utils.SecurityUtils;
import com.youying.common.utils.SnowFlake;
import com.youying.common.utils.StringUtils;
import com.youying.common.utils.uuid.IdUtils;
import com.youying.common.wechat.WechatLoginRequest;
import com.youying.common.wechat.WechatService;
import com.youying.framework.web.service.SysLoginService;
import com.youying.system.service.UserService;
import com.youying.system.service.UserSettingService;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@RestController
public class SysLoginController {
    @Autowired
    private SysLoginService loginService;
    @Autowired
    private WechatService wechatService;
    @Autowired
    private UserService userService;
    @Autowired
    private UserSettingService userSettingService;

    /**
     * 登录方法-微信登录
     *
     * @param request 登录信息
     * @return 结果
     */
    @PostMapping("/loginByWechat")
    @Anonymous
    public AjaxResult loginByWechat(@RequestBody WechatLoginRequest request) {
        String phone = wechatService.getWechatPhone(request.getPhoneCode());
        String openId = wechatService.getWechat(request.getCode());
        User user = userService.findUserByPhone(phone);
        if (user == null) {
            Date nowTime = new Date();
            // 添加用户
            user = new User();
            user.setOpenId(openId);
            user.setNo(SnowFlake.getSnowFlakeId());
            user.setName(IdUtils.getRandomNumber());
            user.setPhone(phone);
            user.setPassword(SecurityUtils.encryptPassword(Constants.USER_DEFAULT_PWD));
            user.setRankMedalInfoId(0L);
            user.setSex(SexFlag.UNKNOWN.getCode());
            user.setAmount(BigDecimal.ZERO);
            user.setSumLook(0);
            user.setSpeakStatus(StatusFlag.OK.getCode());
            user.setStatus(StatusFlag.OK.getCode());
            user.setDeleted(DeleteFlag.OK.getCode());
            user.setCreateBy(phone);
            user.setUpdateBy(phone);
            user.setCreateTime(nowTime);
            user.setUpdateTime(nowTime);
            userService.save(user);

            UserSetting userSetting = new UserSetting();
            userSetting.setUserId(user.getId());
            userSetting.setCommentNotify(1);
            userSetting.setWellNotify(1);
            userSetting.setIssueNotify(1);
            userSetting.setGroupMessageNotify(1);
            userSetting.setMessageNotify(1);
            userSetting.setWelcomeNotify(1);
            userSetting.setLikeNotify(1);
            userSettingService.save(userSetting);
        }
        if (StringUtils.isBlank(user.getOpenId())) {
            user.setOpenId(openId);
            userService.updateById(user);
        }
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String token = loginService.loginByWechat(user.getPhone());
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    /**
     * 登录方法-微信自动登录
     *
     * @param code 微信小程序wx.login获取的code
     * @return 结果
     */
    @GetMapping("/autoLogin")
    @Anonymous
    public AjaxResult autoLogin(String code) {
        if (StringUtils.isBlank(code)) {
            return AjaxResult.error("code参数不能为空");
        }

        try {
            // 1. 通过code获取openid
            String openId = wechatService.getWechat(code);
            if (StringUtils.isBlank(openId)) {
                return AjaxResult.error("获取openid失败，请检查code是否有效");
            }

            // 2. 根据openid查找用户
            User user = userService.findUserByOpenId(openId);
            if (user == null) {
                return AjaxResult.error("用户不存在，请先完成注册");
            }

            // 3. 检查用户状态
            if (StatusFlag.PROHIBITION.getCode().equals(user.getStatus())) {
                return AjaxResult.error("账号已被停用");
            }
            if (DeleteFlag.DELETE.getCode().equals(user.getDeleted())) {
                return AjaxResult.error("账号已被删除");
            }

            // 4. 生成令牌并返回
            AjaxResult ajax = AjaxResult.success();
            String token = loginService.loginByWechat(user.getPhone());
            ajax.put(Constants.TOKEN, token);
            ajax.put("message", "自动登录成功");
            return ajax;

        } catch (Exception e) {
            return AjaxResult.error("自动登录失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo() {
        User user = userService.getById(SecurityUtils.getUserId());
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("ipAddr", SecurityUtils.getLoginLocation());
        return ajax;
    }

    /**
     * 获取手机号码
     *
     * @param request 获取手机号码
     * @return 结果
     */
    @PostMapping("/getWechatPhone")
    @Anonymous
    public AjaxResult getWechatPhone(@RequestBody WechatLoginRequest request) {
        String phone = wechatService.getWechatPhone(request.getCode());
        AjaxResult ajax = AjaxResult.success();
        ajax.put("phone", phone);
        return ajax;
    }
}
