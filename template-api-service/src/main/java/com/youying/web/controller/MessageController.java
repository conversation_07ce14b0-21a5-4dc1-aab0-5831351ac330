package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.Message;
import com.youying.common.core.page.PageDomain;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.system.service.MessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;

/**
 * 群发消息表
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@RestController
@RequestMapping("/message")
public class MessageController extends BaseController {

    @Autowired
    private MessageService messageService;

    /**
     * 查询群发消息表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<Message>> listByPage(@RequestBody PageDomain pageDomain) {
        startPage(pageDomain);
        return R.ok(getTableList(messageService.listByPage()));
    }

    /**
     * 查询群发消息表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<Message> details(Long id) {
        return R.ok(messageService.getById(id));
    }

    /**
     * 添加群发消息表
     *
     * @return
     */
    @PostMapping(value = "/add")
    @Log(title = "添加群发消息表数据", businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody Message message) {
        return R.ok(messageService.save(message));
    }

    /**
     * 修改群发消息表
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改群发消息表数据", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody Message message) {
        return R.ok(messageService.updateById(message));
    }

    /**
     * 删除群发消息表
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除群发消息表数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(messageService.removeByIds(Arrays.asList(ids)));
        }
        return R.ok();
    }

}

