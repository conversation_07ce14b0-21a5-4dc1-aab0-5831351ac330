package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.page.PageDomain;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.system.domain.userorder.UserOrderResponse;
import com.youying.system.service.UserOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户订单表
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@RestController
@RequestMapping("/userOrder")
public class UserOrderController extends BaseController {

    @Autowired
    private UserOrderService userOrderService;

    /**
     * 查询用户订单表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<UserOrderResponse>> listByPage(@RequestBody PageDomain pageDomain) {
        startPage(pageDomain);
        return R.ok(getTableList(userOrderService.listByPage(pageDomain)));
    }

    /**
     * 查询用户订单表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<UserOrderResponse> details(Long id) {
        return R.ok(userOrderService.details(id));
    }

    /**
     * 查询用户订单未支付条数
     *
     * @return
     */
    @GetMapping(value = "/findUserOrderCount")
    public R<?> findUserOrderCount() {
        return R.ok(userOrderService.findUserOrderCount());
    }

    /**
     * 取消支付
     *
     * @return
     */
    @PostMapping(value = "/cancelOrder/{id}")
    @Log(title = "取消支付", businessType = BusinessType.UPDATE)
    public R<?> cancelOrder(@PathVariable("id") Long id) {
        return R.ok(userOrderService.cancelOrder(id));
    }
}

