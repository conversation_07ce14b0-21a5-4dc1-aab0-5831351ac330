package com.youying.web.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.UserNotify;
import com.youying.common.core.page.PageDomain;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.common.enums.Enums.LookFlag;
import com.youying.common.utils.SecurityUtils;
import com.youying.system.domain.usernotify.UserNotifyResponse;
import com.youying.system.service.UserNotifyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;

/**
 * 用户推送信息表
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@RestController
@RequestMapping("/userNotify")
public class UserNotifyController extends BaseController {

    @Autowired
    private UserNotifyService userNotifyService;

    /**
     * 查询用户推送信息表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<UserNotify>> listByPage(@RequestBody PageDomain pageDomain) {
        startPage(pageDomain);
        return R.ok(getTableList(userNotifyService.listByPage()));
    }

    /**
     * 查询用户推送信息表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<UserNotifyResponse> details(Long id) {
        return R.ok(userNotifyService.details(id));
    }

    /**
     * 删除用户推送信息表
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除用户推送信息表数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(userNotifyService.removeByIds(Arrays.asList(ids)));
        }
        return R.ok();
    }

    /**
     * 查询用户推送未看条数
     *
     * @return
     */
    @GetMapping(value = "/findUserNotLookNotifyCount")
    public R<?> findUserNotLookNotifyCount() {
        return R.ok(userNotifyService.count(new LambdaQueryWrapper<UserNotify>()
                .eq(UserNotify::getUserId, SecurityUtils.getUserId())
                .eq(UserNotify::getLookFlag, LookFlag.DEFAULT.getCode())));
    }
}

