package com.youying.web.controller.common;

import com.google.code.kaptcha.Producer;
import com.youying.common.annotation.Log;
import com.youying.common.config.FriendBetterConfig;
import com.youying.common.constant.CacheConstants;
import com.youying.common.constant.Constants;
import com.youying.common.core.domain.AjaxResult;
import com.youying.common.core.domain.R;
import com.youying.common.core.redis.RedisCache;
import com.youying.common.enums.BusinessType;
import com.youying.common.utils.SecurityUtils;
import com.youying.common.utils.StringUtils;
import com.youying.common.utils.sign.Base64;
import com.youying.common.utils.uuid.IdUtils;
import com.youying.system.service.ISysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.FastByteArrayOutputStream;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * 验证码操作处理
 *
 * <AUTHOR>
 */
@RestController
public class CaptchaController {
    @Resource(name = "captchaProducer")
    private Producer captchaProducer;

    @Resource(name = "captchaProducerMath")
    private Producer captchaProducerMath;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private ISysConfigService configService;

    /**
     * 生成验证码
     */
    @GetMapping("/captchaImage")
    public AjaxResult getCode(HttpServletResponse response) throws IOException {
        AjaxResult ajax = AjaxResult.success();
        boolean captchaEnabled = configService.selectCaptchaEnabled();
        ajax.put("captchaEnabled", captchaEnabled);
        if (!captchaEnabled) {
            return ajax;
        }

        // 保存验证码信息
        String uuid = IdUtils.simpleUUID();
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + uuid;

        String capStr = null, code = null;
        BufferedImage image = null;

        // 生成验证码
        String captchaType = FriendBetterConfig.getCaptchaType();
        if ("math".equals(captchaType)) {
            String capText = captchaProducerMath.createText();
            capStr = capText.substring(0, capText.lastIndexOf("@"));
            code = capText.substring(capText.lastIndexOf("@") + 1);
            image = captchaProducerMath.createImage(capStr);
        } else if ("char".equals(captchaType)) {
            capStr = code = captchaProducer.createText();
            image = captchaProducer.createImage(capStr);
        }

        redisCache.setCacheObject(verifyKey, code, Constants.CAPTCHA_EXPIRATION, TimeUnit.MINUTES);
        // 转换流信息写出
        FastByteArrayOutputStream os = new FastByteArrayOutputStream();
        try {
            ImageIO.write(image, "jpg", os);
        } catch (IOException e) {
            return AjaxResult.error(e.getMessage());
        }

        ajax.put("uuid", uuid);
        ajax.put("img", Base64.encode(os.toByteArray()));
        return ajax;
    }

    /**
     * 发送重置密码验证码
     */
    @GetMapping("/sendUpdatePwdCode")
    @Log(title = "发送重置密码验证码", businessType = BusinessType.INSERT)
    public R<?> sendUpdatePwdCode() {
        String cacheKey = CacheConstants.UPDATE_PWD_PHONE_KEY + SecurityUtils.getUser().getPhone();
        String code = redisCache.getCacheObject(cacheKey);
        if (StringUtils.isNotBlank(code)) {
            return R.fail("请勿重复发送验证码，5分钟后重试");
        }
        String randomNumber = IdUtils.getRandomNumber();
        redisCache.setCacheObject(cacheKey, randomNumber, CacheConstants.UPDATE_PWD_PHONE_TIME, TimeUnit.MINUTES);
        // TODO 发送验证码
        return R.ok();
    }

    /**
     * 验证重置密码验证码
     */
    @GetMapping("/verifyPwdCode")
    @Log(title = "验证重置密码验证码", businessType = BusinessType.INSERT)
    public R<?> verifyPwdCode(String code) {
        String cacheKey = CacheConstants.UPDATE_PWD_PHONE_KEY + SecurityUtils.getUser().getPhone();
        String codeKey = redisCache.getCacheObject(cacheKey);
        if (!code.equals(codeKey)) {
            return R.fail();
        }
        return R.ok();
    }

    /**
     * 发送修改手机号验证码(旧手机)
     */
    @GetMapping("/sendUpdatePhoneCode")
    @Log(title = "发送修改手机号验证码", businessType = BusinessType.INSERT)
    public R<?> sendUpdatePhoneCode() {
        String cacheKey = CacheConstants.UPDATE_PHONE_KEY + SecurityUtils.getUser().getPhone();
        String code = redisCache.getCacheObject(cacheKey);
        if (StringUtils.isNotBlank(code)) {
            return R.fail("请勿重复发送验证码，5分钟后重试");
        }
        String randomNumber = IdUtils.getRandomNumber();
        redisCache.setCacheObject(cacheKey, randomNumber, CacheConstants.UPDATE_PHONE_TIME, TimeUnit.MINUTES);
        // TODO 发送验证码
        return R.ok();
    }

    /**
     * 验证重修改手机号验证码(旧手机)
     */
    @GetMapping("/verifyPhoneCode")
    @Log(title = "验证重置密码验证码", businessType = BusinessType.INSERT)
    public R<?> verifyPhoneCode(String code) {
        String cacheKey = CacheConstants.UPDATE_PHONE_KEY + SecurityUtils.getUser().getPhone();
        String codeKey = redisCache.getCacheObject(cacheKey);
        if (!code.equals(codeKey)) {
            return R.fail();
        }
        return R.ok();
    }

    /**
     * 发送修改手机号验证码(新号码)
     */
    @GetMapping("/sendUpdateNewPhoneCode")
    @Log(title = "发送修改手机号验证码(新号码)", businessType = BusinessType.INSERT)
    public R<?> sendUpdateNewPhoneCode(String phone) {
        String cacheKey = CacheConstants.UPDATE_NEW_PHONE_KEY + phone;
        String code = redisCache.getCacheObject(cacheKey);
        if (StringUtils.isNotBlank(code)) {
            return R.fail("请勿重复发送验证码，5分钟后重试");
        }
        String randomNumber = IdUtils.getRandomNumber();
        redisCache.setCacheObject(cacheKey, randomNumber, CacheConstants.UPDATE_PHONE_TIME, TimeUnit.MINUTES);
        // TODO 发送验证码
        return R.ok();
    }

    /**
     * 发送注销手机号码
     */
    @GetMapping("/sendLogoutCode")
    @Log(title = "发送注销手机号码", businessType = BusinessType.INSERT)
    public R<?> sendLogoutCode() {
        String cacheKey = CacheConstants.LOGOUT_PHONE_KEY + SecurityUtils.getUser().getPhone();
        String code = redisCache.getCacheObject(cacheKey);
        if (StringUtils.isNotBlank(code)) {
            return R.fail("请勿重复发送验证码，5分钟后重试");
        }
        String randomNumber = IdUtils.getRandomNumber();
        redisCache.setCacheObject(cacheKey, randomNumber, CacheConstants.UPDATE_PHONE_TIME, TimeUnit.MINUTES);
        // TODO 发送验证码
        return R.ok();
    }

}
