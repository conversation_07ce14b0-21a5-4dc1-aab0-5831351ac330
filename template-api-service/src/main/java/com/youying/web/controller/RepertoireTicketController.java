package com.youying.web.controller;

import com.youying.common.annotation.Anonymous;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.RepertoireTicket;
import com.youying.common.core.page.PageDomain;
import com.youying.common.core.page.TableList;
import com.youying.system.domain.repertoireticket.RepertoireTicketResponse;
import com.youying.system.service.RepertoireTicketService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 剧目电子票表
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@RestController
@RequestMapping("/repertoireTicket")
public class RepertoireTicketController extends Base<PERSON><PERSON>roller {

    @Autowired
    private RepertoireTicketService repertoireTicketService;

    /**
     * 查询剧目电子票表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    @Anonymous
    public R<TableList<RepertoireTicketResponse>> listByPage(@RequestBody PageDomain pageDomain) {
        startPage(pageDomain);
        return R.ok(getTableList(repertoireTicketService.listByPage(pageDomain)));
    }

    /**
     * 查询剧目电子票表详情
     *
     * @return
     */
    @GetMapping(value = "/getRepertoireTicketInfo")
    @Anonymous
    public R<RepertoireTicketResponse> getRepertoireTicketInfo(Long portfolioId) {
        return R.ok(repertoireTicketService.getRepertoireTicketInfo(portfolioId));
    }

    /**
     * 查询剧目电子票表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    @Anonymous
    public R<RepertoireTicket> details(Long id) {
        return R.ok(repertoireTicketService.getById(id));
    }

    /**
     * 查询剧目电子票表详情
     *
     * @return
     */
    @GetMapping(value = "/findRepertoireTicketInfo")
    @Anonymous
    public R<RepertoireTicketResponse> findRepertoireTicketInfo(Long id, Long relationId) {
        return R.ok(repertoireTicketService.findRepertoireTicketInfo(id, relationId));
    }
}

