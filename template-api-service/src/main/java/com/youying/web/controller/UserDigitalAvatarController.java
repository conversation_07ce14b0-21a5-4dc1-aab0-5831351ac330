package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.UserDigitalAvatar;
import com.youying.common.enums.BusinessType;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.system.service.UserDigitalAvatarService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户电子头像表
 *
 * <AUTHOR>
 * @since 2023-08-29
 */
@RestController
@RequestMapping("/userDigitalAvatar")
public class UserDigitalAvatarController extends BaseController {

    @Autowired
    private UserDigitalAvatarService userDigitalAvatarService;

    /**
     * 添加用户电子头像表
     *
     * @return
     */
    @PostMapping(value = "/add")
    @Log(title = "添加用户电子头像表数据", businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody UserDigitalAvatar userDigitalAvatar) {
        userDigitalAvatar.setStatus(StatusFlag.PROHIBITION.getCode());
        return R.ok(userDigitalAvatarService.save(userDigitalAvatar));
    }

}

