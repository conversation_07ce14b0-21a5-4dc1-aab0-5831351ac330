package com.youying.web.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.UserMessage;
import com.youying.common.core.domain.entity.UserMessageInfo;
import com.youying.common.core.page.PageDomain;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.common.enums.Enums.LookFlag;
import com.youying.common.utils.SecurityUtils;
import com.youying.system.service.UserMessageInfoService;
import com.youying.system.service.UserMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * 用户消息表
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@RestController
@RequestMapping("/userMessageInfo")
public class UserMessageInfoController extends BaseController {

    @Autowired
    private UserMessageInfoService userMessageInfoService;
    @Autowired
    private UserMessageService userMessageService;

    /**
     * 查询用户消息表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<UserMessageInfo>> listByPage(@RequestBody PageDomain pageDomain) {
        startPage(pageDomain);
        return R.ok(getTableList(userMessageInfoService.list()));
    }

    /**
     * 查询用户消息表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<UserMessageInfo> details(Long id) {
        return R.ok(userMessageInfoService.getById(id));
    }

    /**
     * 添加用户消息表
     *
     * @return
     */
    @PostMapping(value = "/add")
    @Log(title = "添加用户消息表数据", businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody UserMessageInfo userMessageInfo) {
        UserMessage userMessage = userMessageService.getById(userMessageInfo.getUserMessageId());
        userMessage.setUpdateTime(new Date());
        userMessageService.updateById(userMessage);
        return R.ok(userMessageInfoService.save(userMessageInfo));
    }

    /**
     * 查询用户未查看消息条数
     *
     * @return
     */
    @GetMapping(value = "/findUserMessageNotifyCount")
    public R<?> findUserMessageNotifyCount() {
        return R.ok(userMessageInfoService.count(new LambdaQueryWrapper<UserMessageInfo>()
                .eq(UserMessageInfo::getUserId, SecurityUtils.getUserId())
                .gt(UserMessageInfo::getUserMerchantId, 0)
                .eq(UserMessageInfo::getLookFlag, LookFlag.DEFAULT.getCode())));
    }
}

