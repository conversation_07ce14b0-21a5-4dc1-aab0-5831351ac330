package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.UserMessage;
import com.youying.common.core.page.PageDomain;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.system.domain.usermessage.UserMessageResponse;
import com.youying.system.service.UserMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;

/**
 * 用户会话表
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@RestController
@RequestMapping("/userMessage")
public class UserMessageController extends BaseController {

    @Autowired
    private UserMessageService userMessageService;

    /**
     * 查询用户会话表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<UserMessageResponse>> listByPage(@RequestBody PageDomain pageDomain) {
        startPage(pageDomain);
        return R.ok(getTableList(userMessageService.listByPage()));
    }

    /**
     * 查询用户会话表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<UserMessageResponse> details(Long id) {
        return R.ok(userMessageService.details(id));
    }

    /**
     * 添加用户会话表
     *
     * @return
     */
    @PostMapping(value = "/add")
    @Log(title = "添加用户会话表数据", businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody UserMessage userMessage) {
        return R.ok(userMessageService.add(userMessage));
    }

    /**
     * 删除用户会话表
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除用户会话表数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(userMessageService.delete(Arrays.asList(ids)));
        }
        return R.ok();
    }

}

