package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.Merchant;
import com.youying.common.core.page.PageDomain;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.system.service.MerchantService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * 商家表
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@RestController
@RequestMapping("/merchant")
public class MerchantController extends BaseController {

    @Autowired
    private MerchantService merchantService;

    /**
     * 查询商家表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<Merchant>> listByPage(@RequestBody PageDomain pageDomain) {
        startPage(pageDomain);
        return R.ok(getTableList(merchantService.list()));
    }


    /**
     * 查询商家表列表
     *
     * @return
     */
    @GetMapping(value = "/list")
    public R<List<Merchant>> list() {
        return R.ok(merchantService.list());
    }

    /**
     * 查询商家表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<Merchant> details(Long id) {
        return R.ok(merchantService.getById(id));
    }

    /**
     * 添加商家表
     *
     * @return
     */
    @PostMapping(value = "/add")
    @Log(title = "添加商家表数据", businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody Merchant merchant) {
        return R.ok(merchantService.save(merchant));
    }

    /**
     * 修改商家表
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改商家表数据", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody Merchant merchant) {
        return R.ok(merchantService.updateById(merchant));
    }

    /**
     * 删除商家表
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除商家表数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(merchantService.removeByIds(Arrays.asList(ids)));
        }
        return R.ok();
    }

}

