package com.youying.web.controller;

import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.page.TableList;
import com.youying.system.domain.souvenirbadge.SouvenirBadgeRequest;
import com.youying.system.domain.souvenirbadge.SouvenirBadgeResponse;
import com.youying.system.service.SouvenirBadgeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 剧场纪念徽章表
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@RestController
@RequestMapping("/souvenirBadge")
public class SouvenirBadgeController extends BaseController {

    @Autowired
    private SouvenirBadgeService souvenirBadgeService;

    /**
     * 查询剧场纪念徽章表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/open/listByPage")
    public R<TableList<SouvenirBadgeResponse>> listByPage(@RequestBody SouvenirBadgeRequest request) {
        startPage(request);
        return R.ok(getTableList(souvenirBadgeService.listByPage(request)));
    }

    /**
     * 查询剧场纪念徽章表详情
     *
     * @param id
     * @param relationId 未获取传0
     * @return
     */
    @GetMapping(value = "/open/details")
    public R<SouvenirBadgeResponse> details(Long id, Long relationId) {
        return R.ok(souvenirBadgeService.details(id, relationId));
    }
}

