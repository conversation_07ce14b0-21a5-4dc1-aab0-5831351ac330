package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.RepertoireLabel;
import com.youying.common.core.page.PageDomain;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.system.service.RepertoireLabelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * 剧目标签表
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@RestController
@RequestMapping("/repertoireLabel")
public class RepertoireLabelController extends BaseController {

    @Autowired
    private RepertoireLabelService repertoireLabelService;

    /**
     * 查询剧目标签表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<RepertoireLabel>> listByPage(@RequestBody PageDomain pageDomain) {
        startPage(pageDomain);
        return R.ok(getTableList(repertoireLabelService.list()));
    }


    /**
     * 查询剧目标签表列表
     *
     * @return
     */
    @GetMapping(value = "/list")
    public R<List<RepertoireLabel>> list() {
        return R.ok(repertoireLabelService.list());
    }

    /**
     * 查询剧目标签表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<RepertoireLabel> details(Long id) {
        return R.ok(repertoireLabelService.getById(id));
    }

    /**
     * 添加剧目标签表
     *
     * @return
     */
    @PostMapping(value = "/add")
    @Log(title = "添加剧目标签表数据", businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody RepertoireLabel repertoireLabel) {
        return R.ok(repertoireLabelService.save(repertoireLabel));
    }

    /**
     * 修改剧目标签表
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改剧目标签表数据", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody RepertoireLabel repertoireLabel) {
        return R.ok(repertoireLabelService.updateById(repertoireLabel));
    }

    /**
     * 删除剧目标签表
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除剧目标签表数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(repertoireLabelService.removeByIds(Arrays.asList(ids)));
        }
        return R.ok();
    }

}

