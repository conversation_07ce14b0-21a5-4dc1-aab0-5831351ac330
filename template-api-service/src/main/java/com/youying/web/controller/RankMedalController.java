package com.youying.web.controller;

import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.RankMedal;
import com.youying.common.core.page.PageDomain;
import com.youying.common.core.page.TableList;
import com.youying.system.service.RankMedalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 等级勋章表
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@RestController
@RequestMapping("/rankMedal")
public class RankMedalController extends BaseController {

    @Autowired
    private RankMedalService rankMedalService;

    /**
     * 查询等级勋章表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<RankMedal>> listByPage(@RequestBody PageDomain pageDomain) {
        startPage(pageDomain);
        return R.ok(getTableList(rankMedalService.list()));
    }

    /**
     * 查询等级勋章表列表
     *
     * @return
     */
    @GetMapping(value = "/list")
    public R<List<RankMedal>> list() {
        return R.ok(rankMedalService.list());
    }

    /**
     * 查询等级勋章表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<RankMedal> details(Long id) {
        return R.ok(rankMedalService.getById(id));
    }

}

