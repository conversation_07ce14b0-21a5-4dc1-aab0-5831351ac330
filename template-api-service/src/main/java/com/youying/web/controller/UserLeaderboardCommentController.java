package com.youying.web.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.UserLeaderboardComment;
import com.youying.common.enums.BusinessType;
import com.youying.common.utils.SecurityUtils;
import com.youying.system.service.UserLeaderboardCommentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 用户榜单评论
 *
 * <AUTHOR>
 * @since 2024-01-09
 */
@RestController
@RequestMapping("/userLeaderboardComment")
public class UserLeaderboardCommentController extends BaseController {

    @Autowired
    private UserLeaderboardCommentService userLeaderboardCommentService;

    /**
     * 查询用户榜单评论列
     *
     * @return
     */
    @GetMapping(value = "/findUserLeaderboardComment")
    public R<List<UserLeaderboardComment>> findUserLeaderboardComment() {
        return R.ok(userLeaderboardCommentService.list(new LambdaQueryWrapper<UserLeaderboardComment>()
                .eq(UserLeaderboardComment::getUserId, SecurityUtils.getUserId())));
    }

    /**
     * 查询用户榜单评论详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<UserLeaderboardComment> details(Long id) {
        return R.ok(userLeaderboardCommentService.getById(id));
    }

    /**
     * 添加用户榜单评论
     *
     * @return
     */
    @PostMapping(value = "/add")
    @Log(title = "添加用户榜单评论数据", businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody UserLeaderboardComment userLeaderboardComment) {
        userLeaderboardComment.setUserId(SecurityUtils.getUserId());
        userLeaderboardComment.setCommentUserId(SecurityUtils.getUserId());
        return R.ok(userLeaderboardCommentService.save(userLeaderboardComment));
    }

    /**
     * 修改用户榜单评论
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改用户榜单评论数据", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody UserLeaderboardComment userLeaderboardComment) {
        return R.ok(userLeaderboardCommentService.updateById(userLeaderboardComment));
    }

    /**
     * 删除用户榜单评论
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{id}")
    @Log(title = "删除用户榜单评论数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("id") Long id) {
        return R.ok(userLeaderboardCommentService.remove(new LambdaQueryWrapper<UserLeaderboardComment>()
                .eq(UserLeaderboardComment::getId, id)
                .eq(UserLeaderboardComment::getCommentUserId, SecurityUtils.getUserId())));
    }

}

