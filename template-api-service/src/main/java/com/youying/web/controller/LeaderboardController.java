package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.Leaderboard;
import com.youying.common.enums.BusinessType;
import com.youying.common.enums.Enums.DataFlag;
import com.youying.system.domain.leaderboard.AddLeaderboardRequest;
import com.youying.system.domain.leaderboard.LeaderboardResponse;
import com.youying.system.service.LeaderboardService;
import com.youying.system.service.UserLeaderboardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 榜单表
 *
 * <AUTHOR>
 * @since 2024-01-09
 */
@RestController
@RequestMapping("/leaderboard")
public class LeaderboardController extends BaseController {
    @Autowired
    private LeaderboardService leaderboardService;
    @Autowired
    private UserLeaderboardService userLeaderboardService;

    /**
     * 查询用户榜单列表
     *
     * @return
     */
    @GetMapping(value = "/findUserLeaderboardList")
    public R<List<LeaderboardResponse>> findUserLeaderboardList() {
        return R.ok(leaderboardService.findUserLeaderboardList());
    }

    /**
     * 查询用户榜单最后修改时间
     *
     * @return
     */
    @GetMapping(value = "/findUserLeaderboardLastTime")
    public R<?> findUserLeaderboardLastTime() {
        return R.ok(leaderboardService.findUserLeaderboardLastTime());
    }

    /**
     * 查询榜单表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<LeaderboardResponse> details(Long id) {
        return R.ok(leaderboardService.details(id));
    }

    /**
     * 查询用户榜单数量
     *
     * @return
     */
    @GetMapping(value = "/findUserLeaderboardCount")
    public R<?> findUserLeaderboardCount() {
        return R.ok(leaderboardService.findLeaderboardCount());
    }

    /**
     * 添加榜单表
     *
     * @return
     */
    @PostMapping(value = "/add")
    @Log(title = "添加榜单表数据", businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody AddLeaderboardRequest request) {
        return R.ok(leaderboardService.add(request));
    }

    /**
     * 修改榜单表
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改榜单表数据", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody AddLeaderboardRequest request) {
        return R.ok(leaderboardService.update(request));
    }

    /**
     * 删除榜单表
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{id}")
    @Log(title = "删除榜单表数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("id") Long id) {
        Leaderboard leaderboard = leaderboardService.getById(id);
        if (leaderboard != null && DataFlag.ADMIN.getCode().equals(leaderboard.getType())) {
            return R.fail("系统数据，无法删除");
        }
        leaderboardService.removeById(id);
        userLeaderboardService.deleteByLeaderboard(id);
        return R.ok();
    }

}

