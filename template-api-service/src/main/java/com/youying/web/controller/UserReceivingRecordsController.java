package com.youying.web.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.youying.common.annotation.Anonymous;
import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.UserAdornCollection;
import com.youying.common.core.domain.entity.UserReceivingRecords;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.common.enums.Enums.BadgeTypeFlag;
import com.youying.common.enums.Enums.LookFlag;
import com.youying.common.utils.SecurityUtils;
import com.youying.system.domain.ticketgroup.TicketGroupRequest;
import com.youying.system.domain.ticketgroup.TicketGroupResponse;
import com.youying.system.domain.userreceivingrecords.AddUserReceivingRecordsRequest;
import com.youying.system.domain.userreceivingrecords.UserReceivingRecordsRequest;
import com.youying.system.domain.userreceivingrecords.UserReceivingRecordsResponse;
import com.youying.system.service.UserReceivingRecordsService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 用户数字藏品领取记录
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@RestController
@RequestMapping("/userReceivingRecords")
public class UserReceivingRecordsController extends BaseController {
    @Autowired
    private UserReceivingRecordsService userReceivingRecordsService;

    /**
     * 查询用户数字藏品领取记录列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<UserReceivingRecordsResponse>> listByPage(@RequestBody UserReceivingRecordsRequest request) {
        startPage(request);
        return R.ok(getTableList(userReceivingRecordsService.listByPage(request)));
    }

    /**
     * 查询用户纸质票总金额
     *
     * @return
     */
    @PostMapping(value = "/findSumPrice")
    public R<?> findSumPrice(@RequestBody UserReceivingRecordsRequest request) {
        return R.ok(userReceivingRecordsService.findSumPrice(request));
    }

    /**
     * 查询用户数字头像
     *
     * @return
     */
    @PostMapping(value = "/findUserDigitalAvatar")
    public R<TableList<UserReceivingRecordsResponse>> findUserDigitalAvatar(@RequestBody UserReceivingRecordsRequest request) {
        startPage(request);
        return R.ok(getTableList(userReceivingRecordsService.findUserDigitalAvatar(request)));
    }

    /**
     * 查询用户纪念勋章
     *
     * @return
     */
    @PostMapping(value = "/findUserSouvenirBadge")
    public R<TableList<UserReceivingRecordsResponse>> findUserSouvenirBadge(@RequestBody UserReceivingRecordsRequest request) {
        startPage(request);
        return R.ok(getTableList(userReceivingRecordsService.findUserSouvenirBadge(request)));
    }

    /**
     * 查询用户数字藏品领取记录详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    @Anonymous
    public R<UserReceivingRecordsResponse> details(Long id) {
        return R.ok(userReceivingRecordsService.details(id));
    }

    /**
     * 查询用户数字藏品领取记录
     *
     * @return
     */
    @GetMapping(value = "/findReceivingRecordsByPortfolioNo")
    public R<List<UserReceivingRecordsResponse>> findReceivingRecordsByPortfolioNo(String portfolioNo) {
        return R.ok(userReceivingRecordsService.findReceivingRecordsByPortfolioNo(portfolioNo));
    }

    /**
     * 数字头像（分组）
     *
     * @return
     */
    @PostMapping(value = "/listByDigitalAvatar")
    public R<TableList<UserReceivingRecordsResponse>> listByDigitalAvatar(@RequestBody UserReceivingRecordsRequest request) {
        startPage(request);
        return R.ok(getTableList(userReceivingRecordsService.listByDigitalAvatar(request)));
    }

    /**
     * 等级勋章（分组）
     *
     * @return
     */
    @PostMapping(value = "/listByRankMedal")
    public R<TableList<UserReceivingRecordsResponse>> listByRankMedal(@RequestBody UserReceivingRecordsRequest request) {
        startPage(request);
        return R.ok(getTableList(userReceivingRecordsService.listByRankMedal(request)));
    }

    /**
     * 电子票详情
     *
     * @return
     */
    @GetMapping(value = "/detailsByRepertoireTicket")
    public R<UserReceivingRecordsResponse> detailsByRepertoireTicket(Long id) {
        return R.ok(userReceivingRecordsService.detailsByRepertoireTicket(id));
    }

    /**
     * 电子头像详情
     *
     * @param id
     * @param upgradeStatus
     * @return
     */
    @GetMapping(value = "/detailsByDigitalAvatar")
    public R<UserReceivingRecordsResponse> detailsByDigitalAvatar(Long id, Integer upgradeStatus) {
        return R.ok(userReceivingRecordsService.detailsByDigitalAvatar(id, upgradeStatus));
    }

    /**
     * 纪念徽章详情
     *
     * @param id
     * @param upgradeStatus
     * @return
     */
    @GetMapping(value = "/detailsBySouvenirBadge")
    public R<UserReceivingRecordsResponse> detailsBySouvenirBadge(Long id, Integer upgradeStatus) {
        return R.ok(userReceivingRecordsService.detailsBySouvenirBadge(id, upgradeStatus));
    }

    /**
     * 等级勋章详情
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/detailsByRankMedal")
    public R<UserReceivingRecordsResponse> detailsByRankMedal(Long id) {
        return R.ok(userReceivingRecordsService.detailsByRankMedal(id));
    }

    /**
     * 根据用户ID+类型查询领取数量
     *
     * @param badgeType 1：电子票、2：数字头像、3：纪念徽章、4：等级勋章
     * @return
     */
    @GetMapping(value = "/findGetNumberByUser")
    public R<?> findGetNumberByUser(Long userId, Integer badgeType) {
        return R.ok(userReceivingRecordsService.findGetNumberByUser(userId, badgeType));
    }

    /**
     * 查询用户领取数字头像数量
     *
     * @return
     */
    @GetMapping(value = "/findDigitalAvatarCountByUser")
    public R<?> findDigitalAvatarCountByUser() {
        return R.ok(userReceivingRecordsService.findDigitalAvatarCountByUser());
    }

    /**
     * 添加用户数字藏品领取记录
     *
     * @return
     */
    @PostMapping(value = "/add")
    @Log(title = "添加用户数字藏品领取记录数据", businessType = BusinessType.INSERT)
    public R<List<UserReceivingRecords>> add(@RequestBody AddUserReceivingRecordsRequest request) {
        if (CollectionUtils.isEmpty(request.getRepertoireTicketList())) {
            return R.fail("数据错误~");
        }
        return R.ok(userReceivingRecordsService.add(request));
    }

    /**
     * 添加数字头像、纪念勋章
     *
     * @return
     */
    @PostMapping(value = "/save")
    @Log(title = "添加数字头像、纪念勋章", businessType = BusinessType.INSERT)
    public R<?> save(@RequestBody UserReceivingRecords userReceivingRecords) {
        return R.ok(userReceivingRecordsService.add(userReceivingRecords));
    }

    /**
     * 修改
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改用户领取记录", businessType = BusinessType.INSERT)
    public R<?> update(@RequestBody UserReceivingRecords userReceivingRecords) {
        return R.ok(userReceivingRecordsService.update(userReceivingRecords));
    }

    /**
     * 查询用户等级勋章未查看条数
     *
     * @return
     */
    @GetMapping(value = "/findNotLookRankCount")
    public R<?> findNotLookRankCount() {
        return R.ok(userReceivingRecordsService.count(new LambdaQueryWrapper<UserReceivingRecords>()
                .eq(UserReceivingRecords::getUserId, SecurityUtils.getUserId())
                .eq(UserReceivingRecords::getBadgeType, BadgeTypeFlag.RANK_MEDAL.getCode())
                .eq(UserReceivingRecords::getLookFlag, LookFlag.DEFAULT.getCode())));
    }

    /**
     * 查询用户藏品未查看条数
     *
     * @param badgeType 藏品类型
     * @return
     */
    @GetMapping(value = "/findNotLookCount")
    public R<?> findNotLookCount(Integer badgeType) {
        return R.ok(userReceivingRecordsService.count(new LambdaQueryWrapper<UserReceivingRecords>()
                .eq(UserReceivingRecords::getUserId, SecurityUtils.getUserId())
                .eq(UserReceivingRecords::getBadgeType, badgeType)
                .eq(UserReceivingRecords::getLookFlag, LookFlag.DEFAULT.getCode())));
    }

    /**
     * 查询用户未评论条数
     *
     * @return
     */
    @GetMapping(value = "/findCommentCount")
    public R<?> findCommentCount() {
        return R.ok(userReceivingRecordsService.count(new LambdaQueryWrapper<UserReceivingRecords>()
                .eq(UserReceivingRecords::getUserId, SecurityUtils.getUserId())
                .eq(UserReceivingRecords::getBadgeType, BadgeTypeFlag.ELECTRONIC_TICKET.getCode())
                .eq(UserReceivingRecords::getLookFlag, LookFlag.DEFAULT.getCode())
                .eq(UserReceivingRecords::getCommentId, 0)));
    }

    /**
     * 修改藏品佩戴状态
     *
     * @return
     */
    @PutMapping(value = "/updateAdornStatus")
    @Log(title = "修改藏品佩戴状态", businessType = BusinessType.UPDATE)
    public R<?> updateAdornStatus(@RequestBody UserAdornCollection userAdornCollection) {
        return R.ok(userReceivingRecordsService.updateAdornStatus(userAdornCollection));
    }

    /**
     * 查询用户电子票
     *
     * @return
     */
    @PostMapping(value = "/findElectronicTicketByUserId")
    public R<List<TicketGroupResponse>> findElectronicTicketByUserId(@RequestBody TicketGroupRequest request) {
        return R.ok(userReceivingRecordsService.findElectronicTicketByUserId(request));
    }
}

