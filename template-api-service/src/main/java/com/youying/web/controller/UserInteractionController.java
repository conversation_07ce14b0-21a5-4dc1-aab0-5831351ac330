package com.youying.web.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.UserInteraction;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.common.enums.Enums.LookFlag;
import com.youying.common.utils.SecurityUtils;
import com.youying.system.domain.userinteraction.UserInteractionRequest;
import com.youying.system.domain.userinteraction.UserInteractionResponse;
import com.youying.system.service.UserInteractionService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * 用户互动通知表
 *
 * <AUTHOR>
 * @since 2023-06-18
 */
@RestController
@RequestMapping("/userInteraction")
public class UserInteractionController extends BaseController {

    @Autowired
    private UserInteractionService userInteractionService;

    /**
     * 查询用户互动通知表列表(分页)-评论
     *
     * @return
     */
    @PostMapping(value = "/listByPageByComment")
    public R<TableList<UserInteractionResponse>> listByPageByComment(@RequestBody UserInteractionRequest request) {
        startPage(request);
        return R.ok(getTableList(userInteractionService.listByPageByComment(request)));
    }

    /**
     * 查询用户互动通知表列表(分页)-点赞
     *
     * @return
     */
    @PostMapping(value = "/listByPageByKudos")
    public R<TableList<UserInteractionResponse>> listByPageByKudos(@RequestBody UserInteractionRequest request) {
        List<UserInteraction> userInteractionsList = userInteractionService.list(new LambdaQueryWrapper<UserInteraction>()
                .select(UserInteraction::getId, UserInteraction::getLookFlag)
                .eq(UserInteraction::getReplyUserId, SecurityUtils.getUserId())
                .eq(UserInteraction::getType, 2)
                .eq(UserInteraction::getLookFlag, LookFlag.DEFAULT.getCode()));
        if (CollectionUtils.isNotEmpty(userInteractionsList)) {
            userInteractionsList.forEach(item -> item.setLookFlag(LookFlag.PASS.getCode()));
            userInteractionService.updateBatchById(userInteractionsList);
        }
        startPage(request);
        return R.ok(getTableList(userInteractionService.listByPageByKudos(request)));
    }

    /**
     * 查询用户互动通知表列表(分页)-提问
     *
     * @return
     */
    @PostMapping(value = "/listByPageByIssue")
    public R<TableList<UserInteractionResponse>> listByPageByIssue(@RequestBody UserInteractionRequest request) {
        List<UserInteraction> userInteractionsList = userInteractionService.list(new LambdaQueryWrapper<UserInteraction>()
                .select(UserInteraction::getId, UserInteraction::getLookFlag)
                .eq(UserInteraction::getReplyUserId, SecurityUtils.getUserId())
                .eq(UserInteraction::getType, 3)
                .eq(UserInteraction::getLookFlag, LookFlag.DEFAULT.getCode()));
        if (CollectionUtils.isNotEmpty(userInteractionsList)) {
            userInteractionsList.forEach(item -> item.setLookFlag(LookFlag.PASS.getCode()));
            userInteractionService.updateBatchById(userInteractionsList);
        }
        startPage(request);
        return R.ok(getTableList(userInteractionService.listByPageByIssue(request)));
    }

    /**
     * 删除用户互动通知表
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除用户互动通知表数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(userInteractionService.removeByIds(Arrays.asList(ids)));
        }
        return R.ok();
    }

    /**
     * 查询用户未评论条数
     *
     * @param type （1、评论，2、评论点赞，3、提问，4、动态点赞）
     * @return
     */
    @GetMapping(value = "/findUserReceivingRecordsCount")
    public R<?> findUserReceivingRecordsCount(Integer type) {
        return R.ok(userInteractionService.count(new LambdaQueryWrapper<UserInteraction>()
                .eq(UserInteraction::getReplyUserId, SecurityUtils.getUserId())
                .eq(UserInteraction::getType, type)
                .eq(UserInteraction::getLookFlag, LookFlag.DEFAULT.getCode())));
    }

}