package com.youying.web.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.UserTreasure;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.common.enums.Enums.LookFlag;
import com.youying.common.utils.SecurityUtils;
import com.youying.system.domain.usertreasure.UserTreasureRequest;
import com.youying.system.domain.usertreasure.UserTreasureResponse;
import com.youying.system.service.UserTreasureService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 用户剧目剧场收藏表
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@RestController
@RequestMapping("/userTreasure")
public class UserTreasureController extends BaseController {

    @Autowired
    private UserTreasureService userTreasureService;

    /**
     * 剧目剧关注列表
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<UserTreasureResponse>> listByPage(@RequestBody UserTreasureRequest request) {
        List<UserTreasure> list = userTreasureService.list(new LambdaQueryWrapper<UserTreasure>()
                .select(UserTreasure::getId, UserTreasure::getLookFlag)
                .eq(UserTreasure::getUserId, SecurityUtils.getUserId())
                .eq(UserTreasure::getLookFlag, LookFlag.DEFAULT.getCode()));
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(item -> item.setLookFlag(LookFlag.PASS.getCode()));
            userTreasureService.updateBatchById(list);
        }
        startPage(request);
        return R.ok(getTableList(userTreasureService.listByPage(request)));
    }

    /**
     * 剧目剧关注添加或取消
     *
     * @return
     */
    @PostMapping(value = "/save")
    @Log(title = "剧目剧关注添加或取消", businessType = BusinessType.UPDATE)
    public R<?> save(@RequestBody UserTreasure userTreasure) {
        userTreasure.setUserId(SecurityUtils.getUserId());
        return R.ok(userTreasureService.addOrDelete(userTreasure));
    }

    /**
     * 查询用户动态未看条数
     *
     * @return
     */
    @GetMapping(value = "/findDynamicCount")
    public R<?> findDynamicCount() {
        return R.ok(userTreasureService.count(new LambdaQueryWrapper<UserTreasure>()
                .eq(UserTreasure::getUserId, SecurityUtils.getUserId())
                .eq(UserTreasure::getLookFlag, LookFlag.DEFAULT.getCode())));
    }
}

