package com.youying.web.controller;

import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.WechatSetting;
import com.youying.system.service.WechatSettingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 小程序设置表
 *
 * <AUTHOR>
 * @since 2023-06-09
 */
@RestController
@RequestMapping("/wechatSetting")
public class WechatSettingController extends BaseController {

    @Autowired
    private WechatSettingService wechatSettingService;

    /**
     * 查询小程序设置表详情
     *
     * @return
     */
    @GetMapping(value = "/open/getWechatSetting")
    public R<WechatSetting> details() {
        return R.ok(wechatSettingService.getById(1));
    }

}

