package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.DynamicKudos;
import com.youying.common.enums.BusinessType;
import com.youying.system.service.DynamicKudosService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 剧场动态点赞表
 *
 * <AUTHOR>
 * @since 2023-06-18
 */
@RestController
@RequestMapping("/dynamicKudos")
public class DynamicKudosController extends BaseController {

    @Autowired
    private DynamicKudosService dynamicKudosService;

    /**
     * 添加剧场动态点赞、踩、删除
     *
     * @return
     */
    @PostMapping(value = "/save")
    @Log(title = "添加剧场动态点赞表数据", businessType = BusinessType.INSERT)
    public R<?> addOrUpdate(@RequestBody DynamicKudos dynamicKudos) {
        return R.ok(dynamicKudosService.addOrUpdate(dynamicKudos));
    }

}

