package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.RankMedalInfo;
import com.youying.common.core.page.PageDomain;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.system.service.RankMedalInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * 等级勋章详情表
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@RestController
@RequestMapping("/rankMedalInfo")
public class RankMedalInfoController extends BaseController {

    @Autowired
    private RankMedalInfoService rankMedalInfoService;

    /**
     * 查询等级勋章详情表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<RankMedalInfo>> listByPage(@RequestBody PageDomain pageDomain) {
        startPage(pageDomain);
        return R.ok(getTableList(rankMedalInfoService.list()));
    }


    /**
     * 查询等级勋章详情表列表
     *
     * @return
     */
    @GetMapping(value = "/list")
    public R<List<RankMedalInfo>> list() {
        return R.ok(rankMedalInfoService.list());
    }

    /**
     * 查询等级勋章详情表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<RankMedalInfo> details(Long id) {
        return R.ok(rankMedalInfoService.getById(id));
    }

    /**
     * 添加等级勋章详情表
     *
     * @return
     */
    @PostMapping(value = "/add")
    @Log(title = "添加等级勋章详情表数据", businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody RankMedalInfo rankMedalInfo) {
        return R.ok(rankMedalInfoService.save(rankMedalInfo));
    }

    /**
     * 修改等级勋章详情表
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改等级勋章详情表数据", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody RankMedalInfo rankMedalInfo) {
        return R.ok(rankMedalInfoService.updateById(rankMedalInfo));
    }

    /**
     * 删除等级勋章详情表
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除等级勋章详情表数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(rankMedalInfoService.removeByIds(Arrays.asList(ids)));
        }
        return R.ok();
    }

}

