package com.youying.web.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.UserReceivingRecordsText;
import com.youying.common.enums.BusinessType;
import com.youying.system.service.UserReceivingRecordsTextService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * 电子票演员信息
 *
 * <AUTHOR>
 * @since 2024-01-08
 */
@RestController
@RequestMapping("/userReceivingRecordsText")
public class UserReceivingRecordsTextController extends BaseController {

    @Autowired
    private UserReceivingRecordsTextService userReceivingRecordsTextService;

    /**
     * 根据扫描记录查询电子票演员信息
     *
     * @return
     */
    @GetMapping(value = "/findUserReceivingRecordsText")
    public R<List<UserReceivingRecordsText>> findUserReceivingRecordsText(Long userReceivingRecordsId) {
        return R.ok(userReceivingRecordsTextService.list(new LambdaQueryWrapper<UserReceivingRecordsText>()
                .eq(UserReceivingRecordsText::getUserReceivingRecordsId, userReceivingRecordsId)));
    }

    /**
     * 查询电子票演员信息详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<UserReceivingRecordsText> details(Long id) {
        return R.ok(userReceivingRecordsTextService.getById(id));
    }

    /**
     * 添加电子票演员信息
     *
     * @return
     */
    @PostMapping(value = "/add")
    @Log(title = "添加电子票演员信息数据", businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody List<UserReceivingRecordsText> userReceivingRecordsText) {
        return R.ok(userReceivingRecordsTextService.saveBatch(userReceivingRecordsText));
    }

    /**
     * 修改电子票演员信息
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改电子票演员信息数据", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody List<UserReceivingRecordsText> userReceivingRecordsText) {
        return R.ok(userReceivingRecordsTextService.saveOrUpdateBatch(userReceivingRecordsText));
    }

    /**
     * 删除电子票演员信息
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除电子票演员信息数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(userReceivingRecordsTextService.removeByIds(Arrays.asList(ids)));
        }
        return R.ok();
    }

}

