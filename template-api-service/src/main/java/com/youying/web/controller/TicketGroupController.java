package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.common.PullResponse;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.TicketGroup;
import com.youying.common.enums.BusinessType;
import com.youying.common.enums.Enums.DataFlag;
import com.youying.common.utils.SecurityUtils;
import com.youying.system.domain.ticketgroup.AddTicketGroupRequest;
import com.youying.system.domain.ticketgroup.TicketGroupResponse;
import com.youying.system.service.TicketGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 电子票分组
 *
 * <AUTHOR>
 * @since 2024-01-08
 */
@RestController
@RequestMapping("/ticketGroup")
public class TicketGroupController extends BaseController {

    @Autowired
    private TicketGroupService ticketGroupService;

    /**
     * 查询电子票分组列
     *
     * @return
     */
    @PostMapping(value = "/findTicketGroupList")
    public R<List<TicketGroupResponse>> listByPage() {
        return R.ok(ticketGroupService.findTicketGroupList());
    }

    /**
     * 查询电子票分组详情
     *
     * @returnn
     */
    @GetMapping(value = "/details")
    public R<List<TicketGroupResponse>> details(Long id) {
        return R.ok(ticketGroupService.details(id));
    }

    /**
     * 查询用户电子票分组数量
     *
     * @return
     */
    @GetMapping(value = "/findTicketGroupCount")
    public R<?> findTicketGroupCount() {
        return R.ok(ticketGroupService.findTicketGroupCount());
    }

    /**
     * 添加电子票分组
     *
     * @return
     */
    @PostMapping(value = "/add")
    @Log(title = "添加电子票分组数据", businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody AddTicketGroupRequest request) {
        return R.ok(ticketGroupService.add(request));
    }

    /**
     * 修改电子票分组
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改电子票分组数据", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody AddTicketGroupRequest request) {
        return R.ok(ticketGroupService.update(request));
    }

    /**
     * 删除电子票分组
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{id}")
    @Log(title = "删除电子票分组数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("id") Long id) {
        TicketGroup ticketGroup = ticketGroupService.getById(id);
        if (ticketGroup != null && DataFlag.ADMIN.getCode().equals(ticketGroup.getType())) {
            return R.fail("系统分组，无法删除");
        }
        if (!SecurityUtils.getUserId().equals(ticketGroup.getUserId())) {
            return R.fail("数据错误");
        }
        return R.ok(ticketGroupService.delete(id));
    }

    /**
     * 查询电子票分组下拉
     *
     * @returnn
     */
    @GetMapping(value = "/pull")
    public R<List<PullResponse>> pull() {
        return R.ok(ticketGroupService.pull());
    }

}

