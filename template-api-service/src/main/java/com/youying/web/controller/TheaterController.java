package com.youying.web.controller;

import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.page.TableList;
import com.youying.system.domain.repertoire.RepertoireDisplayResponse;
import com.youying.system.domain.theater.TheaterRequest;
import com.youying.system.domain.theater.TheaterResponse;
import com.youying.system.service.TheaterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 剧场表
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@RestController
@RequestMapping("/theater")
public class TheaterController extends BaseController {

    @Autowired
    private TheaterService theaterService;

    /**
     * 查询剧场表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/open/listByPage")
    public R<TableList<TheaterResponse>> listByPage(@RequestBody TheaterRequest request) {
        startPageByUnsafeOrder(request);
        return R.ok(getTableList(theaterService.listByPage(request)));
    }

    /**
     * 查询首页推荐剧场
     *
     * @return
     */
    @PostMapping(value = "/open/listByIndex")
    public R<TableList<TheaterResponse>> listByIndex(@RequestBody TheaterRequest request) {
        startPage(request);
        return R.ok(getTableList(theaterService.listByIndex(request.getUserId())));
    }

    /**
     * 查询剧场详情 (l)
     *
     * @param id     剧场ID
     * @param userId 未登录传 0
     * @return
     */
    @GetMapping(value = "/open/details")
    public R<TheaterResponse> details(Long id, Long userId) {
        return R.ok(theaterService.details(id, userId));
    }

    /**
     * 查询剧场橱窗列表
     *
     * @param theaterId
     * @return
     */
    @GetMapping(value = "/open/findTheaterDisplay")
    public R<List<RepertoireDisplayResponse>> findRepertoireDisplay(Long theaterId) {
        return R.ok(theaterService.findTheaterDisplay(theaterId));
    }
}

