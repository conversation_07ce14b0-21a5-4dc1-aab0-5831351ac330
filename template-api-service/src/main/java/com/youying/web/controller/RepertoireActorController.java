package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.RepertoireActor;
import com.youying.common.core.page.PageDomain;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.system.service.RepertoireActorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * 剧目演员表
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@RestController
@RequestMapping("/repertoireActor")
public class RepertoireActorController extends BaseController {

    @Autowired
    private RepertoireActorService repertoireActorService;

    /**
     * 查询剧目演员表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<RepertoireActor>> listByPage(@RequestBody PageDomain pageDomain) {
        startPage(pageDomain);
        return R.ok(getTableList(repertoireActorService.list()));
    }


    /**
     * 查询剧目演员表列表
     *
     * @return
     */
    @GetMapping(value = "/list")
    public R<List<RepertoireActor>> list() {
        return R.ok(repertoireActorService.list());
    }

    /**
     * 查询剧目演员表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<RepertoireActor> details(Long id) {
        return R.ok(repertoireActorService.getById(id));
    }

    /**
     * 添加剧目演员表
     *
     * @return
     */
    @PostMapping(value = "/add")
    @Log(title = "添加剧目演员表数据", businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody RepertoireActor repertoireActor) {
        return R.ok(repertoireActorService.save(repertoireActor));
    }

    /**
     * 修改剧目演员表
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改剧目演员表数据", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody RepertoireActor repertoireActor) {
        return R.ok(repertoireActorService.updateById(repertoireActor));
    }

    /**
     * 删除剧目演员表
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除剧目演员表数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(repertoireActorService.removeByIds(Arrays.asList(ids)));
        }
        return R.ok();
    }

}

