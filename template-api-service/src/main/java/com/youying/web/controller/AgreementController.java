package com.youying.web.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.Agreement;
import com.youying.system.service.AgreementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;

/**
 * 协议设置表
 *
 * <AUTHOR>
 * @since 2023-06-09
 */
@Validated
@RestController
@RequestMapping("/agreement")
public class AgreementController extends BaseController {

    @Autowired
    private AgreementService agreementService;

    /**
     * 根据类型查询协议
     *
     * @return
     */
    @GetMapping(value = "/open/findAgreementByType")
    public R<Agreement> details(@NotNull(message = "类型不能为空") Integer type) {
        return R.ok(agreementService.getOne(new LambdaQueryWrapper<Agreement>()
                .eq(Agreement::getType, type)));
    }

}

