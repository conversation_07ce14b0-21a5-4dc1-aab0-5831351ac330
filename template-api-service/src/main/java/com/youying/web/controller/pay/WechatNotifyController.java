package com.youying.web.controller.pay;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wechat.pay.java.service.payments.model.Transaction;
import com.wechat.pay.java.service.payments.model.Transaction.TradeStateEnum;
import com.youying.common.annotation.Anonymous;
import com.youying.common.annotation.Log;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.Portfolio;
import com.youying.common.core.domain.entity.PortfolioInfo;
import com.youying.common.core.domain.entity.UserOrder;
import com.youying.common.core.domain.entity.UserReceivingRecords;
import com.youying.common.enums.BusinessType;
import com.youying.common.enums.Enums.BadgeTypeFlag;
import com.youying.common.enums.Enums.FreeFlag;
import com.youying.common.enums.Enums.OrderQueryType;
import com.youying.common.enums.Enums.PayStatusFlag;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.common.pay.PayInfo;
import com.youying.common.pay.PaymentContext;
import com.youying.common.pay.PaymentStrategy;
import com.youying.common.pay.WechatPay;
import com.youying.common.pay.WechatPayment;
import com.youying.common.utils.DateUtils;
import com.youying.system.service.PortfolioInfoService;
import com.youying.system.service.PortfolioService;
import com.youying.system.service.UserOrderService;
import com.youying.system.service.UserReceivingRecordsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SignatureException;
import java.util.Date;

/**
 * 支付回调
 *
 * <AUTHOR>
 * @since 2023-08-15
 */
@RestController
@RequestMapping("/wechat")
@Slf4j
public class WechatNotifyController {
    @Autowired
    private UserOrderService userOrderService;
    @Autowired
    private UserReceivingRecordsService userReceivingRecordsService;
    @Autowired
    private PortfolioService portfolioService;
    @Autowired
    private PortfolioInfoService portfolioInfoService;

    @PostMapping("/pay/wechatPayCallback")
    @Anonymous
    @Log(title = "支付回调", businessType = BusinessType.UPDATE)
    public void wechatCallback(HttpServletRequest request) throws IOException {
        log.info("支付回调 =======================》 {}", request);
        PaymentContext context = new PaymentContext();
        PaymentStrategy weChatPayment = new WechatPayment();
        context.setPaymentStrategy(weChatPayment);
        Transaction transaction = context.callback(request);
        userOrderService.callback(transaction);
    }

    /**
     * 支付
     *
     * @param userReceivingRecordsId
     */
    @PostMapping("/pay/{userReceivingRecordsId}")
    @Log(title = "支付", businessType = BusinessType.INSERT)
    public R<WechatPay> pay(@PathVariable("userReceivingRecordsId") Long userReceivingRecordsId) throws NoSuchAlgorithmException, SignatureException, InvalidKeyException {
        UserReceivingRecords userReceivingRecords = userReceivingRecordsService.getById(userReceivingRecordsId);
        PortfolioInfo portfolioInfo = portfolioInfoService.getById(userReceivingRecords.getPortfolioInfoId());
        Portfolio portfolio = portfolioService.getById(userReceivingRecords.getPortfolioId());
        if (StatusFlag.OK.getCode().equals(portfolio.getSoldOut())) {
            return R.fail("商品已售罄");
        }
        if (DateUtils.compareTo(portfolioInfo.getStartTime(), new Date(), DateUtils.YYYY_MM_DD_HH_MM_SS) < 0) {
            return R.fail("暂未发售，请耐心等待");
        }
        if (DateUtils.compareTo(portfolioInfo.getEndTime(), new Date(), DateUtils.YYYY_MM_DD_HH_MM_SS) > 0) {
            return R.fail("已结束");
        }

        if (FreeFlag.CHARGING.getCode().equals(portfolioInfo.getFree())) {
            if (portfolioInfo.getPrice().compareTo(new BigDecimal(BigInteger.ZERO)) == 0) {
                return R.fail("支付金额必须大于0.00元");
            }
        }
        if (portfolioInfo.getIssuedQuantity() == 0) {
            return R.fail("无升级商品");
        }
        if (StatusFlag.OK.getCode().equals(userReceivingRecords.getUpgradeStatus())) {
            return R.fail("请勿重复支付");
        }
        UserOrder userOrder = userOrderService.getById(userReceivingRecords.getOrderId());
        if (userOrder != null && PayStatusFlag.WITH.getCode().equals(userOrder.getPayStatus())) {
            if (userOrder.getPayPrice().compareTo(new BigDecimal(BigInteger.ZERO)) == 0) {
                userOrderService.updateUserReceivingRecords(userReceivingRecordsId);
                return R.ok();
            }
            PaymentContext context = new PaymentContext();
            PaymentStrategy weChatPayment = new WechatPayment();
            context.setPaymentStrategy(weChatPayment);
            PayInfo orderInfo = context.findPayInfo(userOrder.getOrderNo(), OrderQueryType.ORDER_NO.getCode());
            Transaction transaction = orderInfo.getTransaction();
            TradeStateEnum tradeStateEnum = userOrderService.callback(transaction);
            if (tradeStateEnum.equals(TradeStateEnum.SUCCESS)) {
                return R.fail("请勿重复支付");
            }
        }
        long orderCount = userOrderService.count(new LambdaQueryWrapper<UserOrder>()
                .eq(UserOrder::getPortfolioId, userReceivingRecords.getPortfolioId())
                .eq(UserOrder::getBadgeType, BadgeTypeFlag.ELECTRONIC_TICKET.getCode())
                .ne(UserOrder::getPayStatus, PayStatusFlag.CANCEL.getCode()));
        // 判断藏品是否全部领取
        if (portfolioInfo.getIssuedQuantity() < orderCount + 1) {
            return R.fail("商品已全部发放，请关注下一批。");
        }

        return R.ok(userOrderService.pay(userReceivingRecordsId));
    }

    /**
     * 查询支付信息
     *
     * @return
     */
    @Anonymous
    @GetMapping("findOrderInfo")
    public R<PayInfo> findOrderInfo(String orderNo) {
        PaymentContext context = new PaymentContext();
        PaymentStrategy weChatPayment = new WechatPayment();
        context.setPaymentStrategy(weChatPayment);
        PayInfo orderInfo = context.findPayInfo(orderNo, OrderQueryType.ORDER_NO.getCode());
        return R.ok(orderInfo);
    }
}
