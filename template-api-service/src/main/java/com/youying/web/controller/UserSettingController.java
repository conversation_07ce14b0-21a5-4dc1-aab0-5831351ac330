package com.youying.web.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.UserSetting;
import com.youying.common.enums.BusinessType;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.common.utils.SecurityUtils;
import com.youying.system.service.UserSettingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户设置表
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@RestController
@RequestMapping("/userSetting")
public class UserSettingController extends BaseController {

    @Autowired
    private UserSettingService userSettingService;

    /**
     * 查询用户设置表详情
     *
     * @return
     */
    @GetMapping(value = "/getUserSetting")
    public R<UserSetting> getUserSetting() {
        UserSetting setting = userSettingService.getOne(new LambdaQueryWrapper<UserSetting>()
                .eq(UserSetting::getUserId, SecurityUtils.getUserId()));
        if (setting == null) {
            setting = new UserSetting();
            setting.setUserId(SecurityUtils.getUserId());
            setting.setCommentNotify(StatusFlag.OK.getCode());
            setting.setWellNotify(StatusFlag.OK.getCode());
            setting.setIssueNotify(StatusFlag.OK.getCode());
            setting.setGroupMessageNotify(StatusFlag.OK.getCode());
            setting.setMessageNotify(StatusFlag.OK.getCode());
            setting.setWelcomeNotify(StatusFlag.OK.getCode());
            setting.setLikeNotify(StatusFlag.OK.getCode());
            setting.setElectronicTicket(StatusFlag.OK.getCode());
            userSettingService.save(setting);
        }
        return R.ok(setting);
    }

    /**
     * 修改用户设置表
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改用户设置表数据", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody UserSetting userSetting) {
        return R.ok(userSettingService.updateById(userSetting));
    }
}

