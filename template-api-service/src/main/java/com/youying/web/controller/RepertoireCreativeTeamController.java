package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.RepertoireCreativeTeam;
import com.youying.common.core.page.PageDomain;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.system.service.RepertoireCreativeTeamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * 剧目主创团队表
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@RestController
@RequestMapping("/repertoireCreativeTeam")
public class RepertoireCreativeTeamController extends BaseController {

    @Autowired
    private RepertoireCreativeTeamService repertoireCreativeTeamService;

    /**
     * 查询剧目主创团队表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<RepertoireCreativeTeam>> listByPage(@RequestBody PageDomain pageDomain) {
        startPage(pageDomain);
        return R.ok(getTableList(repertoireCreativeTeamService.list()));
    }


    /**
     * 查询剧目主创团队表列表
     *
     * @return
     */
    @GetMapping(value = "/list")
    public R<List<RepertoireCreativeTeam>> list() {
        return R.ok(repertoireCreativeTeamService.list());
    }

    /**
     * 查询剧目主创团队表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<RepertoireCreativeTeam> details(Long id) {
        return R.ok(repertoireCreativeTeamService.getById(id));
    }

    /**
     * 添加剧目主创团队表
     *
     * @return
     */
    @PostMapping(value = "/add")
    @Log(title = "添加剧目主创团队表数据", businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody RepertoireCreativeTeam repertoireCreativeTeam) {
        return R.ok(repertoireCreativeTeamService.save(repertoireCreativeTeam));
    }

    /**
     * 修改剧目主创团队表
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改剧目主创团队表数据", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody RepertoireCreativeTeam repertoireCreativeTeam) {
        return R.ok(repertoireCreativeTeamService.updateById(repertoireCreativeTeam));
    }

    /**
     * 删除剧目主创团队表
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除剧目主创团队表数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(repertoireCreativeTeamService.removeByIds(Arrays.asList(ids)));
        }
        return R.ok();
    }

}

