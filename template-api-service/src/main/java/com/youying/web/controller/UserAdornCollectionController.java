package com.youying.web.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.UserAdornCollection;
import com.youying.common.utils.SecurityUtils;
import com.youying.system.service.UserAdornCollectionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 用户藏品佩戴表
 *
 * <AUTHOR>
 * @since 2023-08-25
 */
@RestController
@RequestMapping("/userAdornCollection")
public class UserAdornCollectionController extends BaseController {

    @Autowired
    private UserAdornCollectionService userAdornCollectionService;

    /**
     * 查询用户佩戴数字头像
     *
     * @return
     */
    @GetMapping(value = "/findUserAdornDigitalAvatar")
    public R<List<UserAdornCollection>> findUserAdornDigitalAvatar(Integer badgeType) {
        return R.ok(userAdornCollectionService.list(new LambdaQueryWrapper<UserAdornCollection>()
                .eq(UserAdornCollection::getUserId, SecurityUtils.getUserId())
                .eq(UserAdornCollection::getBadgeType, badgeType)));
    }
}

