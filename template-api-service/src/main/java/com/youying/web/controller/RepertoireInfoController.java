package com.youying.web.controller;

import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.page.TableList;
import com.youying.system.domain.repertoireinfo.RepertoireInfoRequest;
import com.youying.system.domain.repertoireinfo.RepertoireInfoResponse;
import com.youying.system.service.RepertoireInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 剧目场次信息表
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@RestController
@RequestMapping("/repertoireInfo")
public class RepertoireInfoController extends BaseController {

    @Autowired
    private RepertoireInfoService repertoireInfoService;

    /**
     * 根据剧场查询剧目信息
     *
     * @return
     */
    @PostMapping(value = "/open/findRepertoireByTheaterId")
    public R<TableList<RepertoireInfoResponse>> findRepertoireByTheaterId(@RequestBody RepertoireInfoRequest request) {
        startPage(request);
        return R.ok(getTableList(repertoireInfoService.findRepertoireByTheaterId(request)));
    }
}

