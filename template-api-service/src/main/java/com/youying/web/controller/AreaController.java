package com.youying.web.controller;

import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.Area;
import com.youying.system.domain.area.AreaTree;
import com.youying.system.service.AddressService;
import com.youying.system.service.AreaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 地区表
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@RestController
@RequestMapping("/area")
public class AreaController extends BaseController {
    @Autowired
    private AreaService areaService;
    @Autowired
    private AddressService addressService;

    /**
     * 查询地区表树形
     *
     * @return
     */
    @GetMapping(value = "/open/findAreaTree")
    public R<List<AreaTree>> findAreaTree() {
        return R.ok(addressService.findAreaTree());
    }

    /**
     * 查询地区市一级
     *
     * @return
     */
    @GetMapping(value = "/open/findAreaByCity")
    public R<List<AreaTree>> findAreaByCity(String keyword) {
        return R.ok(addressService.findAreaByCity(keyword));
    }

    /**
     * 查询地区表列表
     *
     * @return
     */
    @GetMapping(value = "/list")
    public R<List<Area>> list() {
        return R.ok(areaService.list());
    }
}

