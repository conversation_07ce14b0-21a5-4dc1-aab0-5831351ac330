package com.youying.web.controller;

import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.Repertoire;
import com.youying.common.core.page.TableList;
import com.youying.system.domain.repertoire.RepertoireDisplayResponse;
import com.youying.system.domain.repertoire.RepertoireRequest;
import com.youying.system.domain.repertoire.RepertoireResponse;
import com.youying.system.service.RepertoireService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 剧目表
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@RestController
@RequestMapping("/repertoire")
public class RepertoireController extends BaseController {

    @Autowired
    private RepertoireService repertoireService;

    /**
     * 查询剧目表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/open/listByPage")
    public R<TableList<Repertoire>> listByPage(@RequestBody RepertoireRequest request) {
        startPage(request);
        return R.ok(getTableList(repertoireService.listByPage(request)));
    }

    /**
     * 查询首页推荐剧目表列表(分页)
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/open/listByIndex")
    public R<TableList<RepertoireResponse>> listByIndex(@RequestBody RepertoireRequest request) {
        startPage(request);
        return R.ok(getTableList(repertoireService.listByIndex(request.getUserId(), request.getCity())));
    }

    /**
     * 剧目详情
     *
     * @param id
     * @param userId 用户ID 未登录传 0
     * @return
     */
    @GetMapping(value = "/open/details")
    public R<RepertoireResponse> details(Long id, Long userId) {
        return R.ok(repertoireService.details(id, userId));
    }

    /**
     * 查询剧目橱窗列表
     *
     * @param repertoireId
     * @return
     */
    @GetMapping(value = "/open/findRepertoireDisplay")
    public R<List<RepertoireDisplayResponse>> findRepertoireDisplay(Long repertoireId) {
        return R.ok(repertoireService.findRepertoireDisplay(repertoireId));
    }
}

