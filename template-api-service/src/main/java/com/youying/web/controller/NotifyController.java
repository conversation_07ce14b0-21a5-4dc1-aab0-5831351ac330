package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.Notify;
import com.youying.common.core.page.PageDomain;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.system.service.NotifyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * 通知表
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@RestController
@RequestMapping("/notify")
public class NotifyController extends BaseController {

    @Autowired
    private NotifyService notifyService;

    /**
     * 查询通知表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<Notify>> listByPage(@RequestBody PageDomain pageDomain) {
        startPage(pageDomain);
        return R.ok(getTableList(notifyService.list()));
    }


    /**
     * 查询通知表列表
     *
     * @return
     */
    @GetMapping(value = "/list")
    public R<List<Notify>> list() {
        return R.ok(notifyService.list());
    }

    /**
     * 查询通知表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<Notify> details(Long id) {
        return R.ok(notifyService.getById(id));
    }

    /**
     * 添加通知表
     *
     * @return
     */
    @PostMapping(value = "/add")
    @Log(title = "添加通知表数据", businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody Notify notify) {
        return R.ok(notifyService.save(notify));
    }

    /**
     * 修改通知表
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改通知表数据", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody Notify notify) {
        return R.ok(notifyService.updateById(notify));
    }

    /**
     * 删除通知表
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除通知表数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(notifyService.removeByIds(Arrays.asList(ids)));
        }
        return R.ok();
    }

}

