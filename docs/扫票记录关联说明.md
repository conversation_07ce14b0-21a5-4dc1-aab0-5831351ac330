# 评论与扫票记录关联说明

## 概述

根据您的说明，评论系统与扫票记录有重要的关联关系。当用户上传票据进行扫票时，如果在数据库中找不到对应的剧目，系统会新建一个剧目。由于新建的剧目没有官方的封面图片，因此需要使用扫票时上传的票据图片作为替代。

## 数据库关联关系

### 核心表关系
```
t_comment (评论表)
    ↓ (通过 comment_id 关联)
t_user_receiving_records (用户扫票记录表)
    ↓ (包含图片信息)
image (藏品图片) / file_url (原始票据图片)
```

### 关联字段说明

**t_user_receiving_records 表关键字段：**
- `comment_id`: 关联的评论ID
- `image`: 扫票后生成的藏品图片URL
- `file_url`: 用户上传的原始票据图片URL
- `repertoire_id`: 关联的剧目ID
- `theater_id`: 关联的剧场ID

## 图片获取逻辑

### 优先级策略
我们在SQL查询中使用了`COALESCE`函数来实现智能的图片选择策略：

```sql
COALESCE(r.cover_picture, urr.image, urr.file_url) AS commentImage
```

### 具体逻辑
1. **第一优先级：剧目封面图** (`r.cover_picture`)
   - 如果剧目有官方封面图，优先使用
   - 适用于系统中已存在的正式剧目

2. **第二优先级：扫票藏品图** (`urr.image`)
   - 如果没有剧目封面图，使用扫票后生成的藏品图片
   - 这通常是经过处理和优化的图片

3. **第三优先级：原始票据图** (`urr.file_url`)
   - 如果都没有，使用用户上传的原始票据图片
   - 确保每个评论都有对应的图片显示

## 业务场景

### 场景1：正常剧目评论
- 用户对系统中已存在的剧目进行评论
- 显示剧目的官方封面图
- 扫票记录可能存在也可能不存在

### 场景2：扫票创建新剧目后评论
- 用户上传票据，系统识别后发现数据库中没有对应剧目
- 系统自动创建新剧目（没有封面图）
- 用户对新创建的剧目进行评论
- 显示扫票时的图片（优先藏品图，其次原始票据图）

### 场景3：扫票后对已存在剧目评论
- 用户上传票据，系统识别后匹配到已存在的剧目
- 用户对该剧目进行评论
- 如果剧目有封面图，显示封面图；否则显示扫票图片

## SQL实现细节

### 分页列表查询
```sql
SELECT
    -- 其他字段...
    COALESCE(r.cover_picture, urr.image, urr.file_url) AS commentImage,
    r.cover_picture AS repertoireCoverPicture,
    -- 其他字段...
FROM
    t_comment AS c
    LEFT JOIN t_user AS u ON u.id = c.user_id
    LEFT JOIN t_repertoire AS r ON r.id = c.repertoire_id
    LEFT JOIN t_theater AS t ON t.id = c.theater_id
    LEFT JOIN t_repertoire_info_detail AS rid ON rid.id = c.repertoire_info_detail_id
    LEFT JOIN t_user_receiving_records AS urr ON urr.comment_id = c.id  -- 关键关联
    LEFT JOIN t_comment AS c1 ON c1.parent_id = c.id AND c1.deleted = 1
```

### 评论详情查询
```sql
SELECT
    -- 其他字段...
    COALESCE(r.cover_picture, urr.image, urr.file_url) AS commentImage,
    -- 其他字段...
FROM
    t_comment AS c
    -- 其他JOIN...
    LEFT JOIN t_user_receiving_records AS urr ON urr.comment_id = c.id  -- 关键关联
```

## 数据一致性保证

### 关联维护
- 当用户通过扫票创建评论时，系统需要在`t_user_receiving_records`表中正确设置`comment_id`
- 确保扫票记录与评论的一对一或一对多关系

### 图片URL有效性
- 定期检查图片URL的有效性
- 对于失效的图片，可以考虑使用默认图片或重新生成

## 扩展考虑

### 性能优化
- 考虑为`t_user_receiving_records.comment_id`添加索引
- 对于大量数据的情况，可以考虑图片缓存策略

### 功能扩展
- 支持多张图片的情况（如用户上传多张票据图片）
- 支持图片压缩和CDN加速
- 支持图片水印和版权保护

## 测试建议

### 测试用例
1. **正常剧目评论测试**
   - 验证显示剧目封面图
   - 验证扫票记录不影响图片显示

2. **新建剧目评论测试**
   - 验证没有封面图时显示扫票图片
   - 验证图片优先级逻辑

3. **边界情况测试**
   - 所有图片都为空的情况
   - 图片URL无效的情况
   - 多个扫票记录关联同一评论的情况

### 数据验证
- 验证`comment_id`关联的正确性
- 验证图片URL的格式和有效性
- 验证不同场景下的图片显示逻辑

## 总结

通过关联`t_user_receiving_records`表，我们成功实现了"剧目图片或者上传的票的图片"的需求。这种设计既保证了正常剧目的官方图片显示，又支持了用户扫票创建新剧目时的图片展示，提供了完整的用户体验。

关键优势：
- 智能图片选择策略
- 完整的业务场景覆盖
- 良好的数据一致性
- 灵活的扩展能力
