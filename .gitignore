# 忽略所有编译生成的文件
*.class
*.exe
*.dll
*.so
*.a
*.o
*.out
*.pyc
*.pyo
*.pyd
*.jar
*.war
*.ear

# 忽略日志文件
*.log
*.log.*
*.tmp

# 忽略 IDE 配置文件
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# 忽略操作系统生成的文件
.DS_Store
Thumbs.db

# 忽略依赖目录
node_modules/
bower_components/
vendor/
dist/
build/
bin/
out/
target/

# 忽略环境变量文件
.env
.env.local
.env.development
.env.test
.env.production

# 忽略缓存目录
.cache/
.temp/
.tmp/

# 忽略测试相关文件
coverage/
*.lcov

# 忽略文档生成文件
docs/_build/
docs/_static/
docs/_templates/

# 忽略压缩文件
*.zip
*.tar.gz
*.tgz
*.rar
