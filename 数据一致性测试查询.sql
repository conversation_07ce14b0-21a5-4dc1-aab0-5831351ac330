-- 数据一致性测试查询

-- 1. 测试SUBSTRING函数的正确性
SELECT 
    'SUBSTRING测试' as test_type,
    SUBSTRING('这是一个很长的测试内容，超过20个字符', 0, 20) as substring_0_result,
    SUBSTRING('这是一个很长的测试内容，超过20个字符', 1, 20) as substring_1_result,
    LENGTH(SUBSTRING('这是一个很长的测试内容，超过20个字符', 0, 20)) as length_0,
    LENGTH(SUBSTRING('这是一个很长的测试内容，超过20个字符', 1, 20)) as length_1;

-- 2. 检查特定评论的followCount统计
-- 请将 {comment_id} 替换为实际的评论ID
SELECT 
    '关注统计测试' as test_type,
    comment_id,
    COUNT(1) as follow_count,
    GROUP_CONCAT(user_id) as follower_users,
    GROUP_CONCAT(CONCAT('user:', user_id, ',type:', type)) as details
FROM t_comment_info 
WHERE comment_id = {comment_id} AND `type` = 3
GROUP BY comment_id;

-- 3. 检查评论的所有互动统计
SELECT 
    '完整互动统计' as test_type,
    comment_id,
    `type`,
    COUNT(1) as count,
    CASE 
        WHEN `type` = 0 THEN '踩'
        WHEN `type` = 1 THEN '赞'
        WHEN `type` = 2 THEN '转发'
        WHEN `type` = 3 THEN '关注'
        ELSE '未知'
    END as type_name
FROM t_comment_info 
WHERE comment_id = {comment_id}
GROUP BY comment_id, `type`
ORDER BY `type`;

-- 4. 检查是否有重复数据
SELECT 
    '重复数据检查' as test_type,
    comment_id,
    user_id,
    `type`,
    COUNT(1) as duplicate_count
FROM t_comment_info 
WHERE comment_id = {comment_id}
GROUP BY comment_id, user_id, `type`
HAVING COUNT(1) > 1;

-- 5. 模拟getCommentPageList的查询逻辑
SELECT 
    'PageList模拟' as test_type,
    c.id,
    c.content,
    SUBSTRING(c.content, 1, 20) as content_substring,
    ( SELECT COUNT(1) FROM t_comment_info WHERE comment_id = c.id AND `type` = 1 ) AS likeCount,
    ( SELECT COUNT(1) FROM t_comment_info WHERE comment_id = c.id AND `type` = 2 ) AS shareCount,
    ( SELECT COUNT(1) FROM t_comment_info WHERE comment_id = c.id AND `type` = 3 ) AS followCount
FROM t_comment c
WHERE c.id = {comment_id} AND c.deleted = 1;

-- 6. 模拟getCommentDetail的查询逻辑
SELECT 
    'Detail模拟' as test_type,
    c.id,
    c.content,
    ( SELECT COUNT(1) FROM t_comment_info WHERE comment_id = c.id AND `type` = 1 ) AS likeCount,
    ( SELECT COUNT(1) FROM t_comment_info WHERE comment_id = c.id AND `type` = 2 ) AS shareCount,
    ( SELECT COUNT(1) FROM t_comment_info WHERE comment_id = c.id AND `type` = 3 ) AS followCount,
    ( SELECT GROUP_CONCAT(`type`) FROM t_comment_info WHERE comment_id = c.id AND user_id = {user_id} ) AS kudosStatus
FROM t_comment c
WHERE c.id = {comment_id} AND c.deleted = 1;

-- 7. 检查数据库连接和事务状态
SELECT 
    '数据库状态检查' as test_type,
    @@autocommit as autocommit_status,
    @@transaction_isolation as isolation_level,
    CONNECTION_ID() as connection_id,
    NOW() as current_time;

-- 使用说明：
-- 1. 将 {comment_id} 替换为实际出现问题的评论ID
-- 2. 将 {user_id} 替换为实际的用户ID
-- 3. 逐个执行这些查询，对比结果
-- 4. 特别关注followCount的统计结果是否一致
