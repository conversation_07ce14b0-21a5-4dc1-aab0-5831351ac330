<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>friendbetter</artifactId>
        <groupId>com.youying</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>template-server-service</artifactId>

    <description>
        system系统模块
    </description>

    <dependencies>
        <!-- SpringBoot集成mybatis-plus框架 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>

        <!-- 通用工具-->
        <dependency>
            <groupId>com.youying</groupId>
            <artifactId>template-common-service</artifactId>
        </dependency>

        <dependency>
            <groupId>com.bestpay</groupId>
            <artifactId>digital</artifactId>    <!-- jar名称 -->
            <version>1.0.0</version>          <!-- 如果jar包未指定自定义即可-->
            <type>jar</type>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/collection-open-1.0.3-SNAPSHOT.jar</systemPath>
        </dependency>
    </dependencies>

</project>