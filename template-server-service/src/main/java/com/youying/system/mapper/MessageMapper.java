package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.Message;
import com.youying.system.domain.usermessage.UserMessageResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 群发消息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface MessageMapper extends BaseMapper<Message> {

    /**
     * 查询群发消息表列表(分页)
     *
     * @param userId
     * @return
     */
    List<UserMessageResponse> listByPage(@Param("userId") Long userId);
}
