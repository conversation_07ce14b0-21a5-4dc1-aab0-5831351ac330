package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.DigitalAvatarBlockchain;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 电子头像区块链表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-19
 */
public interface DigitalAvatarBlockchainMapper extends BaseMapper<DigitalAvatarBlockchain> {
    /**
     * 随机获取一张数字头像
     *
     * @param digitalAvatarId
     * @return
     */
    DigitalAvatarBlockchain findRandomAvatarBlockchain(@Param("digitalAvatarId") Long digitalAvatarId);
}
