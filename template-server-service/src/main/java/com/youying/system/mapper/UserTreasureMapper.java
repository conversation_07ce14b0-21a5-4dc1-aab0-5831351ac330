package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.UserTreasure;
import com.youying.system.domain.usertreasure.UserTreasureRequest;
import com.youying.system.domain.usertreasure.UserTreasureResponse;

import java.util.List;

/**
 * <p>
 * 用户剧目剧场收藏表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface UserTreasureMapper extends BaseMapper<UserTreasure> {

    /**
     * 用户关注列表
     *
     * @param request
     * @return
     */
    List<UserTreasureResponse> listByPage(UserTreasureRequest request);

}
