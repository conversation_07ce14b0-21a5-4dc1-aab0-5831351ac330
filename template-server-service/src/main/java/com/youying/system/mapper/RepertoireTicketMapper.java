package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.RepertoireTicket;
import com.youying.common.core.page.PageDomain;
import com.youying.system.domain.repertoireticket.RepertoireTicketResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 剧目电子票表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface RepertoireTicketMapper extends BaseMapper<RepertoireTicket> {

    /**
     * 查询电子票
     *
     * @param repertoireId
     * @param theaterId
     * @return
     */
    List<RepertoireTicket> findTicketByRepertoireId(@Param("repertoireId") Long repertoireId,
                                                    @Param("theaterId") Long theaterId);

    /**
     * 查询电子票
     *
     * @param id
     * @param relationId
     * @return
     */
    RepertoireTicketResponse findRepertoireTicketInfo(@Param("id") Long id,
                                                      @Param("relationId") Long relationId);

    /**
     * 查询剧目电子票表列表
     *
     * @param pageDomain
     * @return
     */
    List<RepertoireTicketResponse> listByPage(PageDomain pageDomain);

    /**
     * 查询电子票详情
     *
     * @param portfolioId
     * @param userId
     * @return
     */
    RepertoireTicketResponse getRepertoireTicketInfo(@Param("portfolioId") Long portfolioId,
                                                     @Param("userId") Long userId);
}
