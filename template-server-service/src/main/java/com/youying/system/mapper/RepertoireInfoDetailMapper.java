package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.RepertoireInfoDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 剧目剧场场次表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface RepertoireInfoDetailMapper extends BaseMapper<RepertoireInfoDetail> {

    /**
     * 根据剧目剧场与场次时间查询场次详情信息
     *
     * @param theaterName
     * @param repertoireName
     * @param dateStr
     * @param timeStr
     * @param address
     * @return
     */
    RepertoireInfoDetail findRepertoireInfoDetail(@Param("theaterName") String theaterName,
                                                  @Param("repertoireName") String repertoireName,
                                                  @Param("dateStr") String dateStr,
                                                  @Param("timeStr") String timeStr,
                                                  @Param("address") String address);

    /**
     * 根据剧目剧场与场次时间查询场次详情信息
     *
     * @param theaterId
     * @param repertoireId
     * @param dateStr
     * @return
     */
    RepertoireInfoDetail findRepertoireInfoDetails(@Param("theaterId") Long theaterId,
                                                   @Param("repertoireId") Long repertoireId,
                                                   @Param("dateStr") String dateStr);

    RepertoireInfoDetail findRepertoireInfo(@Param("theaterId") Long theaterId, @Param("repertoireId") Long repertoireId, @Param("timeList") List<String> timeList);

    /**
     * 根据时间查询剧目
     *
     * @param time
     * @return
     */
    List<Long> findRepertoireByTime(@Param("time") List<String> time);
}
