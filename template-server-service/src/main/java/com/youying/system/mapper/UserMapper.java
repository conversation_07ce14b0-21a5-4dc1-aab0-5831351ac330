package com.youying.system.mapper;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.User;

/**
 * <p>
 * 用户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface UserMapper extends BaseMapper<User> {
    /**
     * 根据用户手机号码
     *
     * @param phone
     * @return
     */
    User findUserByPhone(@Param("phone") String phone);

    /**
     * 根据用户openId查询用户信息
     *
     * @param openId
     * @return
     */
    User findUserByOpenId(@Param("openId") String openId);
}
