package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.CommentInfo;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * 评论点赞、踩详情表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface CommentInfoMapper extends BaseMapper<CommentInfo> {
    /**
     * 删除点赞
     *
     * @param commentId
     * @param userId
     */
    @Delete("DELETE FROM t_comment_info WHERE comment_id = #{commentId} and user_id = #{userId}")
    void deleteUserComment(@Param("commentId") Long commentId,
                           @Param("userId") Long userId);

    /**
     * 修改点赞情况
     *
     * @param commentId
     * @param type
     * @param userId
     * @return
     */
    @Select("UPDATE t_comment_info SET `type` = #{type} WHERE comment_id = #{commentId} and user_id = #{userId}")
    Long update(@Param("commentId") Long commentId,
                @Param("type") Integer type,
                @Param("userId") Long userId);
}
