package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.SouvenirBadgeRequire;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 纪念徽章领取规则表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface SouvenirBadgeRequireMapper extends BaseMapper<SouvenirBadgeRequire> {
    /**
     * 查询用户是否领取或可领取(白名单)
     *
     * @param id
     * @param userId
     * @return
     */
    Long findWhiteList(@Param("id") Long id, @Param("userId") Long userId);

    /**
     * 查询可领取纪念徽章
     *
     * @param id
     * @param merchantId
     * @param repertoireInfoDetailId
     * @param theaterId
     * @param repertoireId
     * @param userId
     * @return
     */
    Long findUserConsumptionCount(@Param("id") Long id,
                                  @Param("merchantId") Long merchantId,
                                  @Param("repertoireInfoDetailId") Long repertoireInfoDetailId,
                                  @Param("theaterId") Long theaterId,
                                  @Param("repertoireId") Long repertoireId,
                                  @Param("userId") Long userId);
}
