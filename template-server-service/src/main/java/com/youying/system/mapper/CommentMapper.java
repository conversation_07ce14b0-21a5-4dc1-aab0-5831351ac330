package com.youying.system.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.Comment;
import com.youying.system.domain.comment.CommentRequest;
import com.youying.system.domain.comment.CommentResponse;

/**
 * <p>
 * 剧目剧场评论 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface CommentMapper extends BaseMapper<Comment> {

    /**
     * 剧目剧场评论
     *
     * @param request
     * @return
     */
    List<CommentResponse> listByPage(CommentRequest request);

    /**
     * 查询剧场、剧目对应评论（填加评论时的两段评论）
     *
     * @param id
     * @return
     */
    CommentResponse findMappingComment(@Param("id") Long id);

    /**
     * 查询剧目所有得分之和
     *
     * @param repertoireId
     * @return
     */
    @Select("SELECT IFNULL(SUM( grade ), 0) FROM t_comment WHERE repertoire_id = #{repertoireId}")
    Double findRepertoireSumGrade(@Param("repertoireId") Long repertoireId);

    /**
     * 查询剧目所有评分用户之和
     *
     * @param repertoireId
     * @return
     */
    @Select("SELECT IFNULL(COUNT( 1 ), 0) FROM t_comment WHERE repertoire_id = #{repertoireId}")
    Double findRepertoireSumUser(@Param("repertoireId") Long repertoireId);

    /**
     * 查询用户添加评论次数
     *
     * @param repertoireId
     * @param theaterId
     * @param repertoireInfoDetailId
     * @return
     */
    Long findUserAdd(@Param("repertoireId") Long repertoireId,
            @Param("theaterId") Long theaterId,
            @Param("repertoireInfoDetailId") Long repertoireInfoDetailId);

    /**
     * 查询评论详情
     *
     * @param id
     * @param userId
     * @return
     */
    CommentResponse details(@Param("id") Long id,
            @Param("userId") Long userId);

    /**
     * 查询用户评论列表(分页)
     *
     * @param request
     * @return
     */
    List<CommentResponse> listByUser(CommentRequest request);

    /**
     * 查询用户回复列表(分页)
     *
     * @param request
     * @return
     */
    List<CommentResponse> listReplyByUser(CommentRequest request);

    /**
     * 查询用户评论条数
     *
     * @param request
     * @return
     */
    Long listByUserCount(CommentRequest request);

    /**
     * 获取评论数据分页列表
     *
     * @param request
     * @return
     */
    List<CommentResponse> getCommentPageList(CommentRequest request);

    /**
     * 评论详情接口
     *
     * @param commentId 评论ID
     * @param userId    用户ID
     * @return
     */
    CommentResponse getCommentDetail(@Param("commentId") Long commentId, @Param("userId") Long userId);
}
