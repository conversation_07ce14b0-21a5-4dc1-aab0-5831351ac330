package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.Address;
import com.youying.system.domain.area.AreaTree;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
public interface AddressMapper extends BaseMapper<Address> {

    /**
     * 查询所有地区
     *
     * @return
     */
    @Select("SELECT id , fullname AS `name` , parent_id FROM t_area ORDER BY sort , id")
    List<AreaTree> findAreaAll();

    /**
     * @return
     */
    @Select("SELECT id , fullname AS `name` FROM t_area WHERE fullname LIKE CONCAT('%',#{keyword},'%') AND level_type = 2 ORDER BY sort , id")
    List<AreaTree> findAreaByCity(@Param("keyword") String keyword);
}
