package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.UserInteraction;
import com.youying.system.domain.userinteraction.UserInteractionRequest;
import com.youying.system.domain.userinteraction.UserInteractionResponse;

import java.util.List;

/**
 * <p>
 * 用户互动通知表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-18
 */
public interface UserInteractionMapper extends BaseMapper<UserInteraction> {

    /**
     * 查询用户互动通知表列表(分页)-评论
     *
     * @param request
     * @return
     */
    List<UserInteractionResponse> listByPageByComment(UserInteractionRequest request);

    /**
     * 查询用户互动通知表列表(分页)-点赞
     *
     * @param request
     * @return
     */
    List<UserInteractionResponse> listByPageByKudos(UserInteractionRequest request);

    /**
     * 查询用户互动通知表列表(分页)-提问
     *
     * @param request
     * @return
     */
    List<UserInteractionResponse> listByPageByIssue(UserInteractionRequest request);
}
