package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.common.PullResponse;
import com.youying.common.core.domain.entity.TicketGroup;
import com.youying.system.domain.ticketgroup.TicketGroupResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 剧目分组表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-08
 */
public interface TicketGroupMapper extends BaseMapper<TicketGroup> {

    /**
     * 查询用户创建剧目分组数量
     *
     * @param dataType
     * @param userId
     * @return
     */
    Integer findTicketGroupCount(@Param("dataType") Integer dataType,
                                 @Param("userId") Long userId);

    /**
     * 查询用户电子票分组下拉
     *
     * @param userId
     * @return
     */
    List<PullResponse> pull(@Param("userId") Long userId);

    /**
     * 查询用户电子票分组
     *
     * @param userId
     * @return
     */
    List<TicketGroupResponse> findTicketGroupList(@Param("userId") Long userId);
}
