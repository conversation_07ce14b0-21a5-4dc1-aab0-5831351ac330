package com.youying.system.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.Repertoire;
import com.youying.system.domain.repertoire.RepertoireDisplayResponse;
import com.youying.system.domain.repertoire.RepertoireRequest;
import com.youying.system.domain.repertoire.RepertoireResponse;

/**
 * <p>
 * 剧目表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface RepertoireMapper extends BaseMapper<Repertoire> {

    /**
     * 查询首页推荐剧目表列表(分页)
     *
     * @return
     */
    List<RepertoireResponse> listByIndex(@Param("userId") Long userId,
            @Param("city") Long city);

    /**
     * 查询剧目表列表(分页)
     *
     * @param request
     * @return
     */
    List<RepertoireResponse> listByPage(RepertoireRequest request);

    /**
     * 剧目详情
     *
     * @param id
     * @param userId
     * @return
     */
    RepertoireResponse details(@Param("id") Long id,
            @Param("userId") Long userId);

    /**
     * 查询剧目橱窗列表
     *
     * @param repertoireId
     * @return
     */
    List<RepertoireDisplayResponse> findRepertoireDisplay(@Param("repertoireId") Long repertoireId);

}
