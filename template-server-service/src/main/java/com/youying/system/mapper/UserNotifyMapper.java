package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.UserNotify;
import com.youying.system.domain.usernotify.UserNotifyResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户推送信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface UserNotifyMapper extends BaseMapper<UserNotify> {

    /**
     * 查询用户系统通知
     *
     * @param userId
     * @return
     */
    List<UserNotifyResponse> listByPage(@Param("userId") Long userId,
                                        @Param("port") Integer port);

    /**
     * 查询用户推送信息表详情
     *
     * @param id
     * @return
     */
    UserNotifyResponse details(@Param("id") Long id,
                               @Param("userId") Long userId,
                               @Param("port") Integer port);
}
