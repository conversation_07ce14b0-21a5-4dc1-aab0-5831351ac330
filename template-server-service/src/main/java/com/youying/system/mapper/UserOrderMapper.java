package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.UserOrder;
import com.youying.common.core.page.PageDomain;
import com.youying.system.domain.userorder.UserOrderResponse;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 用户订单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
public interface UserOrderMapper extends BaseMapper<UserOrder> {

    /**
     * 查询用户订单表
     *
     * @param userId
     * @param pageDomain
     * @return
     */
    List<UserOrderResponse> listByPage(@Param("userId") Long userId,
                                       @Param("pageDomain") PageDomain pageDomain);

    /**
     * 订单详情
     *
     * @param userId
     * @param id
     * @return
     */
    UserOrderResponse details(@Param("userId") Long userId,
                              @Param("id") Long id);

    /**
     * 修改订单
     *
     * @param userOrder
     */
    void updateUserOrder(@Param("userOrder") UserOrder userOrder);

    /**
     * 根据订单编号查询订单
     *
     * @param orderNo
     * @return
     */
    @Select("SELECT * FROM t_user_order WHERE `order_no` = #{orderNo}")
    UserOrder findUserOrderByNo(@Param("orderNo") String orderNo);

    /**
     * 查询用户未支付订单（30分钟）
     *
     * @return
     */
    List<UserOrder> findUserOrderNotPay();
}
