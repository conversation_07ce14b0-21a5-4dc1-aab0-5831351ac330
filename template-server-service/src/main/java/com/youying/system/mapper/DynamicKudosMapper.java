package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.DynamicKudos;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * <p>
 * 剧场动态点赞表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-18
 */
public interface DynamicKudosMapper extends BaseMapper<DynamicKudos> {

    /**
     * 删除动态点赞
     *
     * @param dynamicId
     * @param userId
     */
    @Delete("DELETE FROM t_dynamic_kudos WHERE dynamic_id = #{dynamicId} and user_id = #{userId}")
    void deleteUserDynamicKudos(@Param("dynamicId") Long dynamicId,
                                @Param("userId") Long userId);

    /**
     * 修改动态点赞状态
     *
     * @param dynamicId
     * @param type
     * @param userId
     * @return
     */
    @Update("UPDATE t_dynamic_kudos SET `type` = #{type} WHERE dynamic_id = #{dynamicId} and user_id = #{userId}")
    Long update(@Param("dynamicId") Long dynamicId,
                @Param("type") Integer type,
                @Param("userId") Long userId);

}
