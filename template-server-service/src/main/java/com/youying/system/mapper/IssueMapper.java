package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.Issue;
import com.youying.system.domain.issue.IssueRequest;
import com.youying.system.domain.issue.IssueResponse;

import java.util.List;

/**
 * <p>
 * 剧目剧场问答表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface IssueMapper extends BaseMapper<Issue> {

    /**
     * 查询剧目剧场问答列表(父级)
     *
     * @param request
     * @return
     */
    List<IssueResponse> listByPage(IssueRequest request);

    /**
     * 查询剧目剧场问答列表(子级)
     *
     * @param request
     * @return
     */
    List<IssueResponse> listByParentId(IssueRequest request);
}
