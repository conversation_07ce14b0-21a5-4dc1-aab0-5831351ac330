package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.Leaderboard;
import com.youying.system.domain.leaderboard.LeaderboardResponse;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 榜单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-09
 */
public interface LeaderboardMapper extends BaseMapper<Leaderboard> {

    /**
     * 查询用户榜单数量
     *
     * @param type
     * @param userId
     * @return
     */
    Integer findLeaderboardCount(@Param("type") Integer type,
                                 @Param("userId") Long userId);

    /**
     * 查询用户榜单列表
     *
     * @param userId
     * @return
     */
    List<LeaderboardResponse> findUserLeaderboardList(@Param("userId") Long userId);

    /**
     * 查询用户榜单最后修改时间
     *
     * @param userId
     * @return
     */
    Date findUserLeaderboardLastTime(Long userId);

    /**
     * 查询用户榜单详情
     *
     * @param leaderboardId
     * @param userId
     * @return
     */
    LeaderboardResponse details(@Param("leaderboardId") Long leaderboardId,
                                @Param("userId") Long userId);
}
