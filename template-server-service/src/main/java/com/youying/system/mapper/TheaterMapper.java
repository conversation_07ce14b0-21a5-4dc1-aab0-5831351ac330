package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.Theater;
import com.youying.system.domain.repertoire.RepertoireDisplayResponse;
import com.youying.system.domain.theater.TheaterRequest;
import com.youying.system.domain.theater.TheaterResponse;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 剧场表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface TheaterMapper extends BaseMapper<Theater> {

    /**
     * 查询首页剧场
     *
     * @param userId
     * @return
     */
    List<TheaterResponse> listByIndex(@Param("userId") Long userId);

    /**
     * 剧场列表
     *
     * @param request
     * @return
     */
    List<TheaterResponse> listByPage(TheaterRequest request);

    /**
     * 查询剧场详情
     *
     * @param id
     * @return
     */
    TheaterResponse details(@Param("id") Long id,
                            @Param("userId") Long userId);

    /**
     * 模糊查询是否存在该剧场
     *
     * @param str
     * @return
     */
    @Select("SELECT id , `name` FROM t_theater WHERE #{str} LIKE CONCAT('%',`name`,'%')")
    Theater findTheaterName(@Param("str") String str);

    /**
     * 查询剧场橱窗列表
     *
     * @param theaterId
     * @return
     */
    List<RepertoireDisplayResponse> findTheaterDisplay(@Param("theaterId") Long theaterId);
}
