package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.DigitalAvatarGet;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-30
 */
public interface DigitalAvatarGetMapper extends BaseMapper<DigitalAvatarGet> {

    /**
     * 查询纸质票电子头像图层
     *
     * @param no
     * @return
     */
    @Select("SELECT image FROM t_digital_avatar_get WHERE `no` = #{no}")
    List<String> findDigitalAvatarGetByNo(@Param("no") String no);
}
