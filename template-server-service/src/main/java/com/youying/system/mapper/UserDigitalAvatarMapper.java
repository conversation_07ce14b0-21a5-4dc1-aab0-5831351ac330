package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.UserDigitalAvatar;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * 用户电子头像表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-29
 */
public interface UserDigitalAvatarMapper extends BaseMapper<UserDigitalAvatar> {

    /**
     * 查询一条未占用升级数字头像
     *
     * @param portfolioId
     * @return
     */
    @Select("SELECT * FROM t_user_digital_avatar WHERE portfolio_id = #{portfolioId} AND digital_avatar_id = #{relationId} AND status = '0' LIMIT 1")
    UserDigitalAvatar getUserDigitalAvatar(@Param("portfolioId") Long portfolioId,
                                           @Param("relationId") Long relationId);

    /**
     * 修改用户数字头像
     *
     * @param userDigitalAvatar
     */
    void updateUserDigitalAvatar(@Param("userDigitalAvatar") UserDigitalAvatar userDigitalAvatar);
}
