package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.UserPushCollection;
import com.youying.system.domain.userpushcollection.UserPushCollectionResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户推送藏品表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-02
 */
public interface UserPushCollectionMapper extends BaseMapper<UserPushCollection> {

    /**
     * 查询用户推送数字藏品
     *
     * @param userId
     * @return
     */
    List<UserPushCollectionResponse> listByPage(@Param("userId") Long userId);
}
