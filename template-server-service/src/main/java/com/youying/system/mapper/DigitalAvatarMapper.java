package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.DigitalAvatar;
import com.youying.common.core.page.PageDomain;
import com.youying.system.domain.digitalavatar.DigitalAvatarResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 数字头像表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface DigitalAvatarMapper extends BaseMapper<DigitalAvatar> {

    /**
     * 查询可以电子票
     *
     * @param repertoireId
     * @param theaterId
     * @return
     */
    List<DigitalAvatar> findDigitalAvatarById(@Param("repertoireId") Long repertoireId,
                                              @Param("theaterId") Long theaterId);

    /**
     * 数字头像列表
     *
     * @param pageDomain
     * @return
     */
    List<DigitalAvatarResponse> listByPage(PageDomain pageDomain);

    /**
     * 查询数字头像表详情
     *
     * @param portfolioId
     * @param userId
     * @return
     */
    DigitalAvatarResponse getDigitalAvatarInfo(@Param("portfolioId") Long portfolioId,
                                               @Param("userId") Long userId);
}
