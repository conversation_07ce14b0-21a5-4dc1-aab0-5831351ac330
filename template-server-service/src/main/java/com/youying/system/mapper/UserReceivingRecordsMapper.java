package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.UserReceivingRecords;
import com.youying.system.domain.ticketgroup.TicketGroupRequest;
import com.youying.system.domain.ticketgroup.TicketGroupResponse;
import com.youying.system.domain.userreceivingrecords.UserGetResponse;
import com.youying.system.domain.userreceivingrecords.UserReceivingRecordsRequest;
import com.youying.system.domain.userreceivingrecords.UserReceivingRecordsResponse;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 用户数字藏品领取记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface UserReceivingRecordsMapper extends BaseMapper<UserReceivingRecords> {

    /**
     * 查询用户领取数字藏品
     *
     * @param request
     * @return
     */
    List<UserReceivingRecordsResponse> listByPage(UserReceivingRecordsRequest request);

    /**
     * 查询列表总金额
     *
     * @param request
     * @return
     */
    BigDecimal findSumPrice(UserReceivingRecordsRequest request);

    /**
     * 随机获取5个已经观影用户回答 （最多为5个）
     *
     * @param repertoireId
     * @param theaterId
     * @param userId
     * @return
     */
    List<Long> findRandomUser(@Param("repertoireId") Long repertoireId,
                              @Param("theaterId") Long theaterId,
                              @Param("userId") Long userId);

    /**
     * 数字头像
     *
     * @param request
     * @return
     */
    List<UserReceivingRecordsResponse> listByDigitalAvatar(UserReceivingRecordsRequest request);

    /**
     * 我的勋章（分组）
     *
     * @param request
     * @return
     */
    List<UserReceivingRecordsResponse> listByRankMedal(UserReceivingRecordsRequest request);

    /**
     * 电子票详情
     *
     * @param id
     * @param userId
     * @param badgeType
     * @return
     */
    UserReceivingRecordsResponse detailsByRepertoireTicket(@Param("id") Long id,
                                                           @Param("userId") Long userId,
                                                           @Param("badgeType") Integer badgeType);

    /**
     * 电子头像详情
     *
     * @param id
     * @param userId
     * @param badgeType
     * @param upgradeStatus
     * @return
     */
    UserReceivingRecordsResponse detailsByDigitalAvatar(@Param("id") Long id,
                                                        @Param("userId") Long userId,
                                                        @Param("badgeType") Integer badgeType,
                                                        @Param("upgradeStatus") Integer upgradeStatus);

    /**
     * 纪念徽章详情
     *
     * @param id
     * @param userId
     * @param badgeType
     * @param upgradeStatus
     * @return
     */
    UserReceivingRecordsResponse detailsBySouvenirBadge(@Param("id") Long id,
                                                        @Param("userId") Long userId,
                                                        @Param("badgeType") Integer badgeType,
                                                        @Param("upgradeStatus") Integer upgradeStatus);

    /**
     * 等级勋章详情
     *
     * @param id
     * @param userId
     * @param badgeType
     * @return
     */
    UserReceivingRecordsResponse detailsByRankMedal(@Param("id") Long id,
                                                    @Param("userId") Long userId,
                                                    @Param("badgeType") Integer badgeType);

    /**
     * 修改
     *
     * @param userReceivingRecords
     */
    @Update("update t_user_receiving_records set send_id = #{userReceivingRecords.sendId} where id = #{userReceivingRecords.id}")
    void updateUserReceivingRecords(@Param("userReceivingRecords") UserReceivingRecords userReceivingRecords);

    /**
     * 查询上链藏品
     *
     * @return
     */
    List<UserReceivingRecords> findUserReceivingRecords();

    /**
     * 藏品详情
     *
     * @param id
     * @return
     */
    UserReceivingRecordsResponse details(@Param("id") Long id);

    /**
     * 判断是否被领取
     *
     * @param portfolioId
     * @param seat
     * @return
     */
    Long findIsPickedUp(@Param("portfolioId") Long portfolioId,
                        @Param("seat") String seat,
                        @Param("timeList") List<String> timeList);

    /**
     * 查询用户数字藏品领取记录
     *
     * @param portfolioNo
     * @return
     */
    List<UserReceivingRecordsResponse> findReceivingRecordsByPortfolioNo(@Param("portfolioNo") String portfolioNo);

    /**
     * 修改
     *
     * @param userReceivingRecords
     */
    void updateUserReceivingRecord(@Param("userReceivingRecords") UserReceivingRecords userReceivingRecords);

    /**
     * 查询领取组合列表
     *
     * @param orderNo
     * @return
     */
    @Select("SELECT * FROM t_user_receiving_records WHERE order_no = #{orderNo}")
    List<UserReceivingRecords> findUserReceivingRecordsByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 查询用户数字头像
     *
     * @param request
     * @return
     */
    List<UserReceivingRecordsResponse> findUserDigitalAvatar(UserReceivingRecordsRequest request);

    /**
     * 查询用户电子头像数量
     *
     * @param userId
     * @return
     */
    Long findDigitalAvatarCountByUser(@Param("userId") Long userId);

    /**
     * 查询用户纪念勋章
     *
     * @param request
     * @return
     */
    List<UserReceivingRecordsResponse> findUserSouvenirBadge(UserReceivingRecordsRequest request);

    /**
     * 查询用户观影剧目、剧场次数
     *
     * @param userId
     * @param theaterId
     * @param code
     * @return
     */
    UserGetResponse findUserLookCount(@Param("userId") Long userId, @Param("theaterId") Long theaterId, @Param("code") Integer code);

    /**
     * 查询用户是否观看过指定场次
     *
     * @param userId
     * @param theaterId
     * @param lookNumber
     * @param rankMedalId
     * @param rankMedalInfoId
     * @param repertoireInfoId
     * @param startTime
     * @param endTime
     * @return
     */
    Long findUserLookAssignRepertoire(@Param("userId") Long userId,
                                      @Param("theaterId") Long theaterId,
                                      @Param("lookNumber") Integer lookNumber,
                                      @Param("rankMedalId") Long rankMedalId,
                                      @Param("rankMedalInfoId") Long rankMedalInfoId,
                                      @Param("repertoireInfoId") Long repertoireInfoId,
                                      @Param("startTime") Date startTime,
                                      @Param("endTime") Date endTime);

    /**
     * 查询用户电子票
     *
     * @param request
     * @return
     */
    List<TicketGroupResponse> findElectronicTicketByUserId(TicketGroupRequest request);
}
