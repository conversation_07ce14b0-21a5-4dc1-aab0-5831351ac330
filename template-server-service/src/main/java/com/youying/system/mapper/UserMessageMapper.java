package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.UserMessage;
import com.youying.system.domain.usermessage.UserMessageResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户会话表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface UserMessageMapper extends BaseMapper<UserMessage> {

    /**
     * 用户会话列表
     *
     * @return
     */
    List<UserMessageResponse> listByPage(@Param("userId") Long userId);

    /**
     * 查询用户会话表详情
     *
     * @param id
     * @return
     */
    UserMessageResponse details(@Param("id") Long id,
                                @Param("userId") Long userId);
}
