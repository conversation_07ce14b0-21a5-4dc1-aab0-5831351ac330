package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.Portfolio;
import com.youying.system.domain.portfolio.PortfolioResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 藏品组合表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
public interface PortfolioMapper extends BaseMapper<Portfolio> {

    /**
     * 根据图片文字查询藏品信息
     *
     * @param imageText
     * @return
     */
    PortfolioResponse getPortfolioByImageText(@Param("imageText") String imageText);

    /**
     * 查询藏品信息
     *
     * @return
     */
    List<PortfolioResponse> findPortfolioList(@Param("repertoireIdsList") List<Long> repertoireIdsList);
}
