package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.UserTicketGroup;
import com.youying.system.domain.ticketgroup.TicketGroupResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户电子票分组表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-08
 */
public interface UserTicketGroupMapper extends BaseMapper<UserTicketGroup> {

    /**
     * 查询用户分类数据
     *
     * @param ticketGroupId
     * @param userId
     * @return
     */
    List<TicketGroupResponse> findTicketGroupByUserId(@Param("ticketGroupId") Long ticketGroupId,
                                                      @Param("userId") Long userId);
}
