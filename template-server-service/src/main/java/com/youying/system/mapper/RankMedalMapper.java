package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.RankMedal;
import com.youying.common.core.domain.entity.RankMedalInfo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 等级勋章表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface RankMedalMapper extends BaseMapper<RankMedal> {

    /**
     * 查询可领取等级勋章
     *
     * @param userId
     * @param theaterId
     * @param repertoireId
     * @param amount
     * @param userConsumptionCount
     * @return
     */
    List<RankMedalInfo> findRankMedalCanHave(@Param("userId") Long userId,
                                             @Param("theaterId") Long theaterId,
                                             @Param("repertoireId") Long repertoireId,
                                             @Param("amount") BigDecimal amount,
                                             @Param("userConsumptionCount") Long userConsumptionCount);

}
