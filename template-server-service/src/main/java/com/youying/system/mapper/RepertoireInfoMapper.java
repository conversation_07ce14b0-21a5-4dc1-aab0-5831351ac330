package com.youying.system.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.RepertoireInfo;
import com.youying.system.domain.repertoireinfo.RepertoireInfoRequest;
import com.youying.system.domain.repertoireinfo.RepertoireInfoResponse;

/**
 * <p>
 * 剧目场次信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface RepertoireInfoMapper extends BaseMapper<RepertoireInfo> {

    /**
     * 根据剧场查询关联剧目
     *
     * @param request
     * @return
     */
    List<RepertoireInfoResponse> findRepertoireByTheaterId(RepertoireInfoRequest request);

    /**
     * 查询剧目剧场信息
     *
     * @param repertoireId
     * @return
     */
    List<RepertoireInfoResponse> findRepertoireTheaterInfo(@Param("repertoireId") Long repertoireId);
}
