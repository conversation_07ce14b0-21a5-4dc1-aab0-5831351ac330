package com.youying.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.RepertoireTicket;
import com.youying.common.core.page.PageDomain;
import com.youying.common.utils.SecurityUtils;
import com.youying.system.domain.repertoireticket.RepertoireTicketResponse;
import com.youying.system.mapper.RepertoireTicketMapper;
import com.youying.system.service.RepertoireTicketService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 剧目电子票表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@Service
public class RepertoireTicketServiceImpl extends ServiceImpl<RepertoireTicketMapper, RepertoireTicket> implements RepertoireTicketService {

    /**
     * 根据剧目ID查询电子票
     *
     * @param repertoireId
     * @return
     */
    @Override
    public List<RepertoireTicket> findTicketByRepertoireId(Long repertoireId, Long theaterId) {
        return baseMapper.findTicketByRepertoireId(repertoireId, theaterId);
    }

    /**
     * 查询电子票
     *
     * @param id
     * @param relationId
     * @return
     */
    @Override
    public RepertoireTicketResponse findRepertoireTicketInfo(Long id, Long relationId) {
        return baseMapper.findRepertoireTicketInfo(id, relationId);
    }

    /**
     * 查询剧目电子票表列表
     *
     * @param pageDomain
     * @return
     */
    @Override
    public List<RepertoireTicketResponse> listByPage(PageDomain pageDomain) {
        return baseMapper.listByPage(pageDomain);
    }

    /**
     * 查询电子票详情
     *
     * @param portfolioId
     * @return
     */
    @Override
    public RepertoireTicketResponse getRepertoireTicketInfo(Long portfolioId) {
        return baseMapper.getRepertoireTicketInfo(portfolioId, SecurityUtils.getUserId());
    }
}
