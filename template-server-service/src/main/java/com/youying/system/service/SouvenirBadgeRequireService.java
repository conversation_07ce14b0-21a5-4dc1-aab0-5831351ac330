package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.SouvenirBadgeRequire;

/**
 * <p>
 * 纪念徽章领取规则表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface SouvenirBadgeRequireService extends IService<SouvenirBadgeRequire> {
    /**
     * 查询可领取纪念徽章
     *
     * @param theaterId
     * @param userId
     * @return
     */
    void findUserConsumptionCount(Long theaterId, Long userId);
}
