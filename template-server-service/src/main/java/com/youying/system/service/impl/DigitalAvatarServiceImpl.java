package com.youying.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.DigitalAvatar;
import com.youying.common.core.domain.entity.DigitalAvatarImage;
import com.youying.common.core.page.PageDomain;
import com.youying.common.enums.Enums.AuditFlag;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.common.utils.SecurityUtils;
import com.youying.system.domain.digitalavatar.DigitalAvatarResponse;
import com.youying.system.mapper.DigitalAvatarMapper;
import com.youying.system.service.DigitalAvatarImageService;
import com.youying.system.service.DigitalAvatarService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 数字头像表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@Service
public class DigitalAvatarServiceImpl extends ServiceImpl<DigitalAvatarMapper, DigitalAvatar> implements DigitalAvatarService {
    @Autowired
    private DigitalAvatarImageService digitalAvatarImageService;

    public static List<List<String>> getCombinations(Map<Character, List<String>> lists, Integer sizeNumber, List<List<String>> combinations) {
        combinations.clear();
        backtrack(new ArrayList<>(), '0', sizeNumber, lists, combinations);
        return combinations;
    }

    private static void backtrack(List<String> currentCombination, char currentList, Integer sizeNumber, Map<Character, List<String>> lists, List<List<String>> combinations) {
        if (currentCombination.size() == sizeNumber) {
            combinations.add(new ArrayList<>(currentCombination));
            return;
        }

        List<String> currentListValues = lists.get(currentList);
        for (String value : currentListValues) {
            if (!currentCombination.contains(value)) {
                currentCombination.add(value);
                backtrack(currentCombination, (char) (currentList + 1), sizeNumber, lists, combinations);
                currentCombination.remove(currentCombination.size() - 1);
            }
        }
    }

    /**
     * 根据剧目ID查询数字头像ID
     *
     * @param repertoireId
     * @return
     */
    @Override
    public List<DigitalAvatar> findDigitalAvatarById(Long repertoireId, Long theaterId) {
        return baseMapper.findDigitalAvatarById(repertoireId, theaterId);
    }

    /**
     * 根据剧目ID查询数字头像（随机挑选）
     *
     * @param repertoireId
     * @return
     */
    @Override
    public List<String> findDigitalAvatarByRepertoireId(Long repertoireId) {
        DigitalAvatar digitalAvatar = getOne(new LambdaQueryWrapper<DigitalAvatar>()
                .eq(DigitalAvatar::getRepertoireId, repertoireId)
                .eq(DigitalAvatar::getAudit, AuditFlag.PASS.getCode())
                .eq(DigitalAvatar::getSoldOut, StatusFlag.PROHIBITION.getCode())
                .eq(DigitalAvatar::getStatus, StatusFlag.OK.getCode()));
        return digitalAvatarImageService.findDigitalAvatarImageByDigitalAvatarId(digitalAvatar.getId());
    }

    /**
     * 判断数字头像是否有组件
     *
     * @param repertoireId
     * @return
     */
    @Override
    public Long findDigitalAvatarImage(Long repertoireId) {
        return digitalAvatarImageService.count(new LambdaQueryWrapper<DigitalAvatarImage>()
                .eq(DigitalAvatarImage::getRepertoireId, repertoireId));
    }

    @Override
    public List<String> findDigitalAvatar(Long digitalAvatarId, Long number) {
        List<DigitalAvatarImage> avatarImages = digitalAvatarImageService.findAvatarImageByDigitalAvatar(digitalAvatarId);
        if (CollectionUtils.isEmpty(avatarImages)) {
            return null;
        }
        Map<Integer, List<DigitalAvatarImage>> map =
                avatarImages.stream().collect(Collectors.groupingBy(DigitalAvatarImage::getGroup));

        Map<Character, List<String>> lists = new HashMap<>(map.size());
        char sort = '0';
        for (Map.Entry<Integer, List<DigitalAvatarImage>> entry : map.entrySet()) {
            List<DigitalAvatarImage> value = entry.getValue();
            List<String> list = new ArrayList<>();
            for (DigitalAvatarImage digitalAvatarImage : value) {
                list.add(digitalAvatarImage.getImage());
            }
            lists.put(sort, list);
            sort++;
        }

        List<List<String>> combinations = new ArrayList<>();
        List<List<String>> list = getCombinations(lists, map.size(), combinations);
        int flag = 1;
        for (List<String> strings : list) {
            if (flag == number) {
                return strings;
            }
            flag++;
        }
        return null;
    }

    /**
     * 数字头像列表
     *
     * @param pageDomain
     * @return
     */
    @Override
    public List<DigitalAvatarResponse> listByPage(PageDomain pageDomain) {
        return baseMapper.listByPage(pageDomain);
    }

    /**
     * 查询数字头像表详情
     *
     * @param portfolioId
     * @return
     */
    @Override
    public DigitalAvatarResponse getDigitalAvatarInfo(Long portfolioId) {
        return baseMapper.getDigitalAvatarInfo(portfolioId, SecurityUtils.getUserId());
    }
}

