package com.youying.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.DigitalAvatarGet;
import com.youying.system.mapper.DigitalAvatarGetMapper;
import com.youying.system.service.DigitalAvatarGetService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-30
 */
@Service
public class DigitalAvatarGetServiceImpl extends ServiceImpl<DigitalAvatarGetMapper, DigitalAvatarGet> implements DigitalAvatarGetService {

    /**
     * 查询纸质票电子头像图层
     *
     * @param no
     * @return
     */
    @Override
    public List<String> findDigitalAvatarGetByNo(String no) {
        return baseMapper.findDigitalAvatarGetByNo(no);
    }

    /**
     * 保存纸质票数字头像数据
     *
     * @param no
     * @param digitalAvatarId
     * @param digitalAvatarUrl
     */
    @Override
    @Transactional
    public void add(String no, Long digitalAvatarId, List<String> digitalAvatarUrl) {
        if (CollectionUtils.isNotEmpty(digitalAvatarUrl)) {
            List<DigitalAvatarGet> digitalAvatarGetList = new ArrayList<DigitalAvatarGet>();
            for (String url : digitalAvatarUrl) {
                DigitalAvatarGet digitalAvatarGet = new DigitalAvatarGet();
                digitalAvatarGet.setDigitalAvatarId(digitalAvatarId);
                digitalAvatarGet.setNo(no);
                digitalAvatarGet.setImage(url);
                digitalAvatarGet.setUserId(0L);
                digitalAvatarGet.setUserLookId(0L);
            }
            saveBatch(digitalAvatarGetList);
        }
    }
}
