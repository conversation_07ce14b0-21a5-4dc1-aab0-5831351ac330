package com.youying.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.RankMedalInfo;
import com.youying.common.core.domain.entity.UserPushCollection;
import com.youying.common.enums.Enums.BadgeTypeFlag;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.common.utils.SecurityUtils;
import com.youying.system.domain.userpushcollection.UserPushCollectionResponse;
import com.youying.system.mapper.UserPushCollectionMapper;
import com.youying.system.service.UserPushCollectionService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 用户推送藏品表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-02
 */
@Service
public class UserPushCollectionServiceImpl extends ServiceImpl<UserPushCollectionMapper, UserPushCollection> implements UserPushCollectionService {

    /**
     * 添加用户推送等级勋章
     *
     * @param rankMedalInfoList
     * @return
     */
    @Override
    @Transactional
    public Integer addRankMedalInfo(List<RankMedalInfo> rankMedalInfoList) {
        List<UserPushCollection> userPushCollectionList = new ArrayList<UserPushCollection>(rankMedalInfoList.size());
        UserPushCollection userPushCollection = new UserPushCollection();
        for (RankMedalInfo rankMedalInfo : rankMedalInfoList) {
            userPushCollection = new UserPushCollection();
            userPushCollection.setUserId(SecurityUtils.getUserId());
            userPushCollection.setTheaterId(rankMedalInfo.getTheaterId());
            userPushCollection.setRepertoireId(rankMedalInfo.getRepertoireId());
            userPushCollection.setRankMedalId(rankMedalInfo.getRankMedalId());
            userPushCollection.setRankMedalInfoId(rankMedalInfo.getId());
            userPushCollection.setStatus(StatusFlag.PROHIBITION.getCode());
            userPushCollectionList.add(userPushCollection);
        }
        saveBatch(userPushCollectionList);
        return userPushCollectionList.size();
    }

    /**
     * 查询用户推送数字藏品
     *
     * @return
     */
    @Override
    public List<UserPushCollectionResponse> listByPage() {
        return baseMapper.listByPage(SecurityUtils.getUserId());
    }

    /**
     * 查询纪念徽章领取人数
     *
     * @param souvenirBadgeId
     * @return
     */
    @Override
    public Long findSouvenirBadgeGetCount(Long souvenirBadgeId) {
        return count(new LambdaQueryWrapper<UserPushCollection>()
                .eq(UserPushCollection::getSouvenirBadgeId, souvenirBadgeId)
                .eq(UserPushCollection::getStatus, StatusFlag.OK.getCode()));
    }

    /**
     * 查询是否推送过等级勋章、纪念勋章
     *
     * @param userId
     * @param relationId
     * @param code
     * @return
     */
    @Override
    public boolean findUserGet(Long userId, Long relationId, Integer code) {
        if (BadgeTypeFlag.RANK_MEDAL.getCode().equals(code)) {
            return count(new LambdaQueryWrapper<UserPushCollection>()
                    .eq(UserPushCollection::getUserId, userId)
                    .eq(UserPushCollection::getRankMedalInfoId, relationId)) > 0;
        }
        if (BadgeTypeFlag.SOUVENIR_BADGE.getCode().equals(code)) {
            return count(new LambdaQueryWrapper<UserPushCollection>()
                    .eq(UserPushCollection::getUserId, userId)
                    .eq(UserPushCollection::getSouvenirBadgeId, relationId)) > 0;
        }
        return false;
    }
}
