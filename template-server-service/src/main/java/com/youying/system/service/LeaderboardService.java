package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.Leaderboard;
import com.youying.system.domain.leaderboard.AddLeaderboardRequest;
import com.youying.system.domain.leaderboard.LeaderboardResponse;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 榜单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-09
 */
public interface LeaderboardService extends IService<Leaderboard> {

    /**
     * 添加榜单
     *
     * @param request
     * @return
     */
    Long add(AddLeaderboardRequest request);

    /**
     * 修改榜单
     *
     * @param request
     * @return
     */
    Long update(AddLeaderboardRequest request);

    /**
     * 查询用户榜单列表
     *
     * @return
     */
    List<LeaderboardResponse> findUserLeaderboardList();

    /**
     * 查询用户榜单最后修改时间
     *
     * @return
     */
    Date findUserLeaderboardLastTime();

    /**
     * 查询用户榜单列表
     *
     * @return
     */
    Integer findLeaderboardCount();

    /**
     * 查询用户榜单详情
     *
     * @param id
     * @return
     */
    LeaderboardResponse details(Long id);

}
