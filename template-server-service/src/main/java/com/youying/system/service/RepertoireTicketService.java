package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.RepertoireTicket;
import com.youying.common.core.page.PageDomain;
import com.youying.system.domain.repertoireticket.RepertoireTicketResponse;

import java.util.List;

/**
 * <p>
 * 剧目电子票表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface RepertoireTicketService extends IService<RepertoireTicket> {

    /**
     * 根据剧目ID查询电子票
     *
     * @param repertoireId
     * @param theaterId
     * @return
     */
    List<RepertoireTicket> findTicketByRepertoireId(Long repertoireId, Long theaterId);

    /**
     * 查询电子票
     *
     * @param id
     * @param relationId
     * @return
     */
    RepertoireTicketResponse findRepertoireTicketInfo(Long id, Long relationId);

    /**
     * 查询剧目电子票表列表
     *
     * @param pageDomain
     * @return
     */
    List<RepertoireTicketResponse> listByPage(PageDomain pageDomain);

    /**
     * 查询电子票详情
     *
     * @param portfolioId
     * @return
     */
    RepertoireTicketResponse getRepertoireTicketInfo(Long portfolioId);
}
