package com.youying.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.Theater;
import com.youying.system.domain.repertoire.RepertoireDisplayResponse;
import com.youying.system.domain.theater.TheaterRequest;
import com.youying.system.domain.theater.TheaterResponse;
import com.youying.system.mapper.TheaterMapper;
import com.youying.system.service.TheaterService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 剧场表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@Service
public class TheaterServiceImpl extends ServiceImpl<TheaterMapper, Theater> implements TheaterService {

    /**
     * 查询首页剧场
     *
     * @param userId
     * @return
     */
    @Override
    public List<TheaterResponse> listByIndex(Long userId) {
        return baseMapper.listByIndex(userId);
    }

    /**
     * 剧场列表
     *
     * @param request
     * @return
     */
    @Override
    public List<TheaterResponse> listByPage(TheaterRequest request) {
        return baseMapper.listByPage(request);
    }

    /**
     * 查询剧场详情
     *
     * @param id
     * @param userId
     * @return
     */
    @Override
    public TheaterResponse details(Long id, Long userId) {
        return baseMapper.details(id, userId);
    }

    /**
     * 模糊查询是否存在该剧场
     *
     * @param str
     * @return
     */
    @Override
    public Theater findTheaterName(String str) {
        return baseMapper.findTheaterName(str);
    }

    /**
     * 查询剧场橱窗列表
     *
     * @param theaterId
     * @return
     */
    @Override
    public List<RepertoireDisplayResponse> findTheaterDisplay(Long theaterId) {
        return baseMapper.findTheaterDisplay(theaterId == null ? 0 : theaterId);
    }
}
