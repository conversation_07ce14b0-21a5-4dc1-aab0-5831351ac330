package com.youying.system.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.Comment;
import com.youying.system.domain.comment.CommentRequest;
import com.youying.system.domain.comment.CommentResponse;
import com.youying.system.domain.comment.SaveCommentRequest;

/**
 * <p>
 * 剧目剧场评论 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface CommentService extends IService<Comment> {

    /**
     * 剧目剧场评论列表
     *
     * @param request
     * @return
     */
    List<CommentResponse> listByPage(CommentRequest request);

    /**
     * 查询剧场、剧目对应评论（填加评论时的两段评论）
     *
     * @param id
     * @return
     */
    CommentResponse findMappingComment(Long id);

    /**
     * 添加评论
     *
     * @param request
     * @return
     */
    Long add(SaveCommentRequest request);

    /**
     * 查询用户是否添加评论
     *
     * @param repertoireId
     * @param theaterId
     * @param repertoireInfoDetailId
     * @return
     */
    Long findUserAdd(Long repertoireId, Long theaterId, Long repertoireInfoDetailId);

    /**
     * 查询评论详情
     *
     * @param id
     * @param userId
     * @return
     */
    CommentResponse details(Long id, Long userId);

    /**
     * 回复评论
     *
     * @param comment
     * @return
     */
    Long reply(Comment comment);

    /**
     * 查询用户评论列表(分页)
     *
     * @param request
     * @return
     */
    List<CommentResponse> listByUser(CommentRequest request);

    /**
     * 查询用户回复列表(分页)
     *
     * @param request
     * @return
     */
    List<CommentResponse> listReplyByUser(CommentRequest request);

    /**
     * 查询用户评论条数
     *
     * @param request
     * @return
     */
    Long listByUserCount(CommentRequest request);

    /**
     * 获取评论数据分页列表
     * 每条评论数据显示评论ID、评论发布人头像、名称、剧目名称、场次名称、剧场名称、
     * 剧目图片或者上传的票的图片、评论内容、评论发布时间、评论被点赞的次数、
     * 评论被转发的次数、评论被关注的次数
     *
     * @param request
     * @return
     */
    List<CommentResponse> getCommentPageList(CommentRequest request);

    /**
     * 评论详情接口
     * 包含评论的评论ID、评论发布人头像、名称、评论发布的地点、ip、剧目名称、场次名称、剧场名称、
     * 剧目图片或者上传的票的图片、评论内容、评论发布时间、评论被点赞的次数、评论被转发的次数、
     * 评论被关注的次数、评论的回复列表（最多两级）
     *
     * @param commentId 评论ID
     * @param userId    用户ID
     * @return
     */
    CommentResponse getCommentDetail(Long commentId, Long userId);
}
