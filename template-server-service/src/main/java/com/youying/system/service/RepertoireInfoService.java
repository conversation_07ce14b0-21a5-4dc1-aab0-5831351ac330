package com.youying.system.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.RepertoireInfo;
import com.youying.system.domain.repertoireinfo.RepertoireInfoRequest;
import com.youying.system.domain.repertoireinfo.RepertoireInfoResponse;

/**
 * <p>
 * 剧目场次信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface RepertoireInfoService extends IService<RepertoireInfo> {
    /**
     * 根据剧目查询关联剧目
     *
     * @param request
     * @return
     */
    List<RepertoireInfoResponse> findRepertoireByTheaterId(RepertoireInfoRequest request);

    /**
     * 查询剧目剧场信息
     *
     * @param repertoireId
     * @return
     */
    List<RepertoireInfoResponse> findRepertoireTheaterInfo(Long repertoireId);
}
