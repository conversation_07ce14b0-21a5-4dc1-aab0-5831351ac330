package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.UserLeaderboard;

/**
 * <p>
 * 用户排行榜表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-09
 */
public interface UserLeaderboardService extends IService<UserLeaderboard> {

    /**
     * 根据榜单删除用户榜单内容
     *
     * @param leaderboardId
     */
    void deleteByLeaderboard(Long leaderboardId);
}
