package com.youying.system.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bestpay.digital.collection.open.req.OpenCreateDropReq;
import com.bestpay.digital.collection.open.req.OpenSkuCreateReq;
import com.youying.common.constant.Constants;
import com.youying.common.core.domain.entity.DigitalAvatar;
import com.youying.common.core.domain.entity.Portfolio;
import com.youying.common.core.domain.entity.RankMedalInfo;
import com.youying.common.core.domain.entity.Repertoire;
import com.youying.common.core.domain.entity.RepertoireTicket;
import com.youying.common.core.domain.entity.SouvenirBadge;
import com.youying.common.core.domain.entity.Theater;
import com.youying.common.core.domain.entity.User;
import com.youying.common.core.domain.entity.UserAdornCollection;
import com.youying.common.core.domain.entity.UserPushCollection;
import com.youying.common.core.domain.entity.UserReceivingRecords;
import com.youying.common.core.domain.entity.UserTicketGroup;
import com.youying.common.enums.Enums.AuditFlag;
import com.youying.common.enums.Enums.BadgeTypeFlag;
import com.youying.common.enums.Enums.DataFlag;
import com.youying.common.enums.Enums.LookFlag;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.common.exception.ServiceException;
import com.youying.common.utils.DateUtils;
import com.youying.common.utils.SecurityUtils;
import com.youying.common.utils.SnowFlake;
import com.youying.common.utils.StringUtils;
import com.youying.common.utils.http.HttpUtils;
import com.youying.system.domain.repertoireticket.GetRepertoireTicketRequest;
import com.youying.system.domain.ticketgroup.TicketGroupRequest;
import com.youying.system.domain.ticketgroup.TicketGroupResponse;
import com.youying.system.domain.userreceivingrecords.AddUserReceivingRecordsRequest;
import com.youying.system.domain.userreceivingrecords.UserGetResponse;
import com.youying.system.domain.userreceivingrecords.UserReceivingRecordsRequest;
import com.youying.system.domain.userreceivingrecords.UserReceivingRecordsResponse;
import com.youying.system.mapper.UserReceivingRecordsMapper;
import com.youying.system.service.DigitalAvatarService;
import com.youying.system.service.PortfolioService;
import com.youying.system.service.RankMedalService;
import com.youying.system.service.RepertoireService;
import com.youying.system.service.RepertoireTicketService;
import com.youying.system.service.SouvenirBadgeRequireService;
import com.youying.system.service.SouvenirBadgeService;
import com.youying.system.service.TheaterService;
import com.youying.system.service.UserAdornCollectionService;
import com.youying.system.service.UserPushCollectionService;
import com.youying.system.service.UserReceivingRecordsService;
import com.youying.system.service.UserReceivingRecordsTextService;
import com.youying.system.service.UserService;
import com.youying.system.service.UserTicketGroupService;
import com.youying.system.service.UserTreasureService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <p>
 * 用户数字藏品领取记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@Service
@Slf4j
public class UserReceivingRecordsServiceImpl extends ServiceImpl<UserReceivingRecordsMapper, UserReceivingRecords> implements UserReceivingRecordsService {
    private final String ZERO = "0";
    @Autowired
    private TheaterService theaterService;
    @Autowired
    private RepertoireService repertoireService;
    @Autowired
    private RepertoireTicketService repertoireTicketService;
    @Autowired
    private UserReceivingRecordsService userReceivingRecordsService;
    @Autowired
    private DigitalAvatarService digitalAvatarService;
    @Autowired
    private UserTreasureService userTreasureService;
    @Autowired
    private SouvenirBadgeService souvenirBadgeService;
    @Autowired
    private SouvenirBadgeRequireService souvenirBadgeRequireService;
    @Autowired
    private UserService userService;
    @Autowired
    private RankMedalService rankMedalService;
    @Autowired
    private UserPushCollectionService userPushCollectionService;
    @Autowired
    private PortfolioService portfolioService;
    @Autowired
    private UserAdornCollectionService userAdornCollectionService;
    @Autowired
    private UserReceivingRecordsTextService userReceivingRecordsTextService;
    @Autowired
    private UserTicketGroupService userTicketGroupService;

    public static boolean isInteger(String number) {
        try {
            Integer.parseInt(number);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    public static boolean isFloat(String number) {
        try {
            Float.parseFloat(number);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    public static String extractDigits(String text) {
        Pattern pattern = Pattern.compile("\\d+(?:\\.\\d{1,2})?+");
        Matcher matcher = pattern.matcher(text);
        if (matcher.find()) {
            return matcher.group();
        } else {
            return "0";
        }
    }

    /**
     * 添加用户数字藏品领取记录
     *
     * @param request
     * @return
     */
    @Transactional
    public UserReceivingRecordsResponse add(GetRepertoireTicketRequest request) {
        UserReceivingRecords userReceivingRecords = new UserReceivingRecords();
        Long getNumber = 0L;
        switch (request.getBadgeType()) {
            case 1:
                RepertoireTicket repertoireTicket = repertoireTicketService.getById(request.getRelationId());
                long exist = userReceivingRecordsService.count(new LambdaQueryWrapper<UserReceivingRecords>()
                        .eq(UserReceivingRecords::getUserId, SecurityUtils.getUserId())
                        .eq(UserReceivingRecords::getRelationId, repertoireTicket.getId())
                        .eq(UserReceivingRecords::getRepertoireInfoId, request.getRepertoireInfoId())
                        .eq(UserReceivingRecords::getRepertoireInfoDetailId, request.getRepertoireInfoDetailId())
                        .eq(UserReceivingRecords::getBadgeType, BadgeTypeFlag.ELECTRONIC_TICKET.getCode()));
                if (exist > 0) {
                    throw new ServiceException("同场演出，无法重复领取电子票");
                }
                if (repertoireTicket == null || !AuditFlag.PASS.getCode().equals(repertoireTicket.getAudit())) {
                    throw new ServiceException("商品不存在");
                }
                // 查询领取数量
                getNumber = userReceivingRecordsService.findGetNumber(request.getRelationId(), BadgeTypeFlag.ELECTRONIC_TICKET.getCode());
                if (repertoireTicket.getIssuedQuantity() < getNumber + 1) {
                    throw new ServiceException("商品已被全部领取，请持续关注下一批~");
                }
                // 判断时间是否符合预期
                if (DateUtils.compareTo(new Date(), repertoireTicket.getStartTime(), "yyyy-MM-dd HH:mm:ss") > 0) {
                    throw new ServiceException("领取时间未到，请耐心等待。");
                }
                if (DateUtils.compareTo(new Date(), repertoireTicket.getEndTime(), "yyyy-MM-dd HH:mm:ss") < 0) {
                    throw new ServiceException("领取时间已过期，请关注下一批。");
                }
                userReceivingRecords.setIssuedQuantity(repertoireTicket.getIssuedQuantity());
                userReceivingRecords.setStartTime(repertoireTicket.getStartTime());
                userReceivingRecords.setEndTime(repertoireTicket.getEndTime());
                break;
            case 2:
                DigitalAvatar digitalAvatar = digitalAvatarService.getById(request.getRelationId());
                if (digitalAvatar == null || !AuditFlag.PASS.getCode().equals(digitalAvatar.getAudit())) {
                    throw new ServiceException("商品不存在");
                }
                // 查询领取数量
                getNumber = userReceivingRecordsService.findGetNumber(request.getRelationId(), BadgeTypeFlag.DIGITAL_AVATAR.getCode());
                if (digitalAvatar.getIssuedQuantity() < getNumber + 1) {
                    throw new ServiceException("商品已被全部领取，请持续关注下一批~");
                }
                // 判断时间是否符合预期
                if (DateUtils.compareTo(new Date(), digitalAvatar.getStartTime(), "yyyy-MM-dd HH:mm:ss") > 0) {
                    throw new ServiceException("领取时间未到，请耐心等待。");
                }
                if (DateUtils.compareTo(new Date(), digitalAvatar.getEndTime(), "yyyy-MM-dd HH:mm:ss") < 0) {
                    throw new ServiceException("领取时间已过期，请关注下一批。");
                }

                userReceivingRecords.setIssuedQuantity(digitalAvatar.getIssuedQuantity());
                userReceivingRecords.setStartTime(digitalAvatar.getStartTime());
                userReceivingRecords.setEndTime(digitalAvatar.getEndTime());
                break;
            case 3:
                SouvenirBadge souvenirBadge = souvenirBadgeService.getById(request.getRelationId());
                if (souvenirBadge == null || !AuditFlag.PASS.getCode().equals(souvenirBadge.getAudit())) {
                    throw new ServiceException("商品不存在");
                }
                // 查询领取数量
                getNumber = userReceivingRecordsService.findGetNumber(request.getRelationId(), BadgeTypeFlag.SOUVENIR_BADGE.getCode());
                if (souvenirBadge.getIssuedQuantity() < getNumber + 1) {
                    throw new ServiceException("商品已被全部领取，请持续关注下一批~");
                }
                // 判断时间是否符合预期
                if (DateUtils.compareTo(new Date(), souvenirBadge.getStartTime(), "yyyy-MM-dd HH:mm:ss") > 0) {
                    throw new ServiceException("领取时间未到，请耐心等待。");
                }
                if (DateUtils.compareTo(new Date(), souvenirBadge.getEndTime(), "yyyy-MM-dd HH:mm:ss") < 0) {
                    userPushCollectionService.removeById(request.getId());
                    throw new ServiceException("领取时间已过期，请关注下一批。");
                }

                if (request.getId() != null && request.getId() > 0L) {
                    UserPushCollection userPushCollection = userPushCollectionService.getById(request.getId());
                    userPushCollection.setStatus(StatusFlag.OK.getCode());
                    userPushCollectionService.updateById(userPushCollection);
                }
                break;
            case 4:
                if (request.getId() != null && request.getId() > 0L) {
                    UserPushCollection userPushCollection = userPushCollectionService.getById(request.getId());
                    userPushCollection.setStatus(StatusFlag.OK.getCode());
                    userPushCollectionService.updateById(userPushCollection);
                }
                break;
            default:
        }

        // 添加领取记录
        // TODO userReceivingRecords.setNo(request.getNo());
        userReceivingRecords.setNo(SnowFlake.getSnowFlakeId());
        userReceivingRecords.setIssuerName(request.getIssuerName());
        userReceivingRecords.setCollectionNo(SnowFlake.getSnowFlakeId());
        userReceivingRecords.setUserId(SecurityUtils.getUserId());
        userReceivingRecords.setRepertoireInfoId(request.getRepertoireInfoId() == null ? 0L : request.getRepertoireInfoId());
        userReceivingRecords.setRepertoireInfoDetailId(request.getRepertoireInfoDetailId());
        userReceivingRecords.setTheaterId(request.getTheaterId());
        userReceivingRecords.setTime(StringUtils.isBlank(request.getTime()) ? null : request.getTime());
        userReceivingRecords.setRepertoireId(request.getRepertoireId());
        userReceivingRecords.setRelationId(request.getRelationId());
        userReceivingRecords.setBadgeType(request.getBadgeType());
        userReceivingRecords.setImage(request.getImage());
        userReceivingRecords.setSeatNumber(StringUtils.isBlank(request.getSeatNumber()) ? null : request.getSeatNumber());
        userReceivingRecords.setAmount(request.getAmount());
        String price = request.getAmount().replaceAll("[0-9.]+", "");
        if (!isInteger(price) && !isFloat(price)) {
            price = "0";
        }
        BigDecimal decimal = new BigDecimal(price);
        userReceivingRecords.setPrice(decimal);
        save(userReceivingRecords);

        if (BadgeTypeFlag.SOUVENIR_BADGE.getCode().equals(request.getBadgeType()) || BadgeTypeFlag.DIGITAL_AVATAR.getCode().equals(request.getBadgeType())) {
            update(userReceivingRecords);
        }

        if (BadgeTypeFlag.ELECTRONIC_TICKET.getCode().equals(request.getBadgeType())) {
            User user = userService.getById(SecurityUtils.getUserId());
            Long userConsumptionCount = userReceivingRecordsService.findUserConsumptionCount(request.getTheaterId(), request.getRepertoireId(), SecurityUtils.getUserId());
            user.setAmount(decimal.add(user.getAmount()));
            userService.updateById(user);

            // 判断是否可领取等级徽章
            List<RankMedalInfo> rankMedalInfoList = rankMedalService.findRankMedalCanHave(request.getTheaterId(), request.getRepertoireId(), user.getAmount(), userConsumptionCount);
            // 判断是否可领取纪念徽章
            souvenirBadgeRequireService.findUserConsumptionCount(request.getTheaterId(), SecurityUtils.getUserId());
            if (CollectionUtils.isNotEmpty(rankMedalInfoList)) {
                userPushCollectionService.addRankMedalInfo(rankMedalInfoList);
            }
        }

        // 自动关注剧目剧场
        if (userReceivingRecords.getTheaterId() != null) {
            userTreasureService.add(userReceivingRecords.getTheaterId(), null, userReceivingRecords.getUserId());
        }
        if (userReceivingRecords.getRepertoireId() != null) {
            userTreasureService.add(null, userReceivingRecords.getRepertoireId(), userReceivingRecords.getUserId());
        }

        UserReceivingRecordsResponse userReceivingRecordsResponse = new UserReceivingRecordsResponse();
        BeanUtils.copyProperties(userReceivingRecords, userReceivingRecordsResponse);
        Repertoire repertoire = repertoireService.getById(userReceivingRecords.getRepertoireId());
        Theater theater = theaterService.getById(userReceivingRecords.getTheaterId());
        userReceivingRecordsResponse.setRepertoireName(repertoire == null ? null : repertoire.getName());
        userReceivingRecordsResponse.setTheaterName(theater == null ? null : theater.getName());

        userReceivingRecordsResponse.setGetNumber(findGetNumber(userReceivingRecords.getRelationId(), userReceivingRecords.getBadgeType()));
        return userReceivingRecordsResponse;
    }

    /**
     * 添加藏品信息
     *
     * @param request
     * @return
     */
    @Override
    @Transactional
    public List<UserReceivingRecords> add(AddUserReceivingRecordsRequest request) {
        List<GetRepertoireTicketRequest> repertoireTicketList = request.getRepertoireTicketList();

        String portfolioNo = SnowFlake.getSnowFlakeId();
        Long portfolioId = request.getPortfolioId();
        Long portfolioInfoId = request.getPortfolioInfoId();
        String no = request.getNo();

        Portfolio portfolio = portfolioService.getById(portfolioId);
        if (portfolio == null) {
            throw new ServiceException("数据错误");
        }

        UserReceivingRecords userReceivingRecords = new UserReceivingRecords();
        List<UserReceivingRecords> userReceivingRecordsList = new ArrayList<>(repertoireTicketList.size());
        Long userId = SecurityUtils.getUserId();

        // 判断是否票据是否被扫描
        if (StatusFlag.OK.getCode().equals(portfolio.getSeatStatus())) {
            if (StringUtils.isBlank(repertoireTicketList.get(0).getSeatNumber())) {
                throw new ServiceException("座位不能为空，请补充完整座位号");
            }
            Long count = userReceivingRecordsService.findIsPickedUp(
                    portfolio.getId(),
                    repertoireTicketList.get(0).getSeatNumber(),
                    repertoireTicketList.get(0).getTime());
            if (count > 0) {
                throw new ServiceException("当前纸质票的电子收藏票已被领取");
            }
        }

        for (GetRepertoireTicketRequest details : repertoireTicketList) {
            userReceivingRecords = new UserReceivingRecords();
            BeanUtils.copyProperties(details, userReceivingRecords);
            userReceivingRecords.setFileUrl(request.getFileUrl());
            userReceivingRecords.setUserId(userId);
            userReceivingRecords.setNo(no);
            userReceivingRecords.setStartTime(portfolio.getStartTime());
            userReceivingRecords.setEndTime(portfolio.getEndTime());
            userReceivingRecords.setLookFlag(LookFlag.DEFAULT.getCode());
            userReceivingRecords.setAdorn(StatusFlag.PROHIBITION.getCode());
            userReceivingRecords.setPortfolioNo(portfolioNo);
            userReceivingRecords.setPortfolioId(portfolioId);
            userReceivingRecords.setPortfolioInfoId(portfolioInfoId);
            userReceivingRecords.setUpgradeStatus(StatusFlag.PROHIBITION.getCode());
            userReceivingRecords.setOrderId(0L);
            if (BadgeTypeFlag.DIGITAL_AVATAR.getCode().equals(userReceivingRecords.getBadgeType()) && StringUtils.isNotBlank(userReceivingRecords.getUpgradeImage())) {
                userReceivingRecords.setUpgradeImage(null);
            }
            String price = ZERO;
            if (StringUtils.isNotBlank(details.getAmount())) {
                price = details.getAmount();
            }
            BigDecimal decimal = new BigDecimal(price);
            userReceivingRecords.setAmount(details.getAmount());
            userReceivingRecords.setPrice(decimal);
            baseMapper.insert(userReceivingRecords);

            userReceivingRecordsList.add(userReceivingRecords);

            if (BadgeTypeFlag.ELECTRONIC_TICKET.getCode().equals(details.getBadgeType())) {
                // 判断是否存在演员信息
                Long userReceivingRecordsId = userReceivingRecords.getId();
                if (CollectionUtils.isNotEmpty(request.getActorInformationList())) {
                    request.getActorInformationList().stream().forEach(item -> {
                        item.setUserReceivingRecordsId(userReceivingRecordsId);
                    });
                    userReceivingRecordsTextService.saveBatch(request.getActorInformationList());
                }
                User user = userService.getById(userId);
                user.setAmount(decimal.add(user.getAmount()));
                userService.updateById(user);

                // 添加为系统默认全部分组中
                UserTicketGroup userTicketGroup = new UserTicketGroup();
                userTicketGroup.setTicketGroupId(1L);
                userTicketGroup.setUserReceivingRecordsId(userReceivingRecords.getId());
                userTicketGroup.setUserId(userId);
                userTicketGroup.setRepertoireId(userReceivingRecords.getRepertoireId());
                userTicketGroup.setType(DataFlag.ADMIN.getCode());
                userTicketGroupService.save(userTicketGroup);

                Long userConsumptionCount = userReceivingRecordsService.findUserConsumptionCount(details.getTheaterId(), details.getRepertoireId(), userId);
                // 判断是否可领取等级徽章
                List<RankMedalInfo> rankMedalInfoList = rankMedalService.findRankMedalCanHave(details.getTheaterId(), details.getRepertoireId(), user.getAmount(), userConsumptionCount);
                // 判断是否可领取纪念徽章
                souvenirBadgeRequireService.findUserConsumptionCount(details.getTheaterId(), userId);
                if (CollectionUtils.isNotEmpty(rankMedalInfoList)) {
                    userPushCollectionService.addRankMedalInfo(rankMedalInfoList);
                }
            }
        }

        // 自动关注剧目剧场
        if (portfolio.getTheaterId() != null) {
            userTreasureService.add(portfolio.getTheaterId(), null, userId);
        }
        if (portfolio.getRepertoireId() != null) {
            userTreasureService.add(null, portfolio.getRepertoireId(), userId);
        }

        return userReceivingRecordsList;
    }

    /**
     * 添加数字头像、纪念勋章
     *
     * @param userReceivingRecords
     * @return
     */
    @Override
    @Transactional
    public Long add(UserReceivingRecords userReceivingRecords) {
        UserPushCollection userPushCollection = userPushCollectionService.getById(userReceivingRecords.getId());
        if (BadgeTypeFlag.RANK_MEDAL.getCode().equals(userReceivingRecords.getBadgeType())) {
            userReceivingRecords.setRelationId(userPushCollection.getRankMedalInfoId());
            userReceivingRecords.setBadgeType(BadgeTypeFlag.RANK_MEDAL.getCode());
        }
        if (BadgeTypeFlag.SOUVENIR_BADGE.getCode().equals(userReceivingRecords.getBadgeType())) {
            Long souvenirBadgeId = userPushCollection.getSouvenirBadgeId();
            SouvenirBadge souvenirBadge = souvenirBadgeService.getById(souvenirBadgeId);
            Long count = userPushCollectionService.findSouvenirBadgeGetCount(souvenirBadgeId);
            if (count + 1 > souvenirBadge.getIssuedQuantity()) {
                throw new ServiceException("纪念勋章已被全部领取，请关注下一批~");
            }
            userReceivingRecords.setImage(souvenirBadge.getCoverPicture());
            userReceivingRecords.setRelationId(souvenirBadgeId);
            userReceivingRecords.setIssuerName(souvenirBadge.getIssuerName());
            userReceivingRecords.setBadgeType(BadgeTypeFlag.SOUVENIR_BADGE.getCode());
        }
        userReceivingRecords.setId(null);
        userReceivingRecords.setUserId(SecurityUtils.getUserId());
        userReceivingRecordsService.save(userReceivingRecords);

        userPushCollection.setStatus(StatusFlag.OK.getCode());
        userPushCollectionService.updateById(userPushCollection);

        if (BadgeTypeFlag.RANK_MEDAL.getCode().equals(userReceivingRecords.getBadgeType())) {
            souvenirBadgeRequireService.findUserConsumptionCount(userReceivingRecords.getTheaterId(), SecurityUtils.getUserId());
        }

        return userPushCollection.getId();
    }

    /**
     * 修改用户数字藏品领取记录
     *
     * @param userReceivingRecords
     * @return
     */
    @Override
    @Transactional
    public Long update(UserReceivingRecords userReceivingRecords) {
        UserReceivingRecords receivingRecords = getById(userReceivingRecords.getId());
        receivingRecords.setPortfolioId(userReceivingRecords.getPortfolioId());
        receivingRecords.setUpgradeImage(userReceivingRecords.getUpgradeImage());
        updateById(userReceivingRecords);
        return userReceivingRecords.getId();
    }

    /**
     * 修改用户数字藏品领取记录
     *
     * @param userReceivingRecords
     * @return
     */
    @Transactional
    public Long update1(UserReceivingRecords userReceivingRecords) {
        UserReceivingRecords receivingRecords = getById(userReceivingRecords.getId());
        receivingRecords.setImage(userReceivingRecords.getImage());

        String collectionName = "";
        String dropName = "";
        Integer flag = 0;

        if (StringUtils.isNotBlank(receivingRecords.getSkuId())) {
            return userReceivingRecords.getId();
        }

        switch (userReceivingRecords.getBadgeType()) {
            case 1:
                RepertoireTicket repertoireTicket = repertoireTicketService.getById(receivingRecords.getRelationId());
                collectionName = repertoireTicket.getCreateBy();
                dropName = "电子票" + SnowFlake.getSnowFlakeId();
                flag++;
                break;
            case 2:
                DigitalAvatar digitalAvatar = digitalAvatarService.getById(receivingRecords.getRelationId());
                collectionName = digitalAvatar.getCreateBy();
                dropName = "电子头像" + SnowFlake.getSnowFlakeId();
                flag++;
                break;
            case 3:
                SouvenirBadge souvenirBadge = souvenirBadgeService.getById(receivingRecords.getRelationId());
                collectionName = souvenirBadge.getCreateBy();
                dropName = "纪念徽章" + SnowFlake.getSnowFlakeId();
                flag++;
                break;
            default:
                throw new ServiceException("数据错误");
        }

        if (flag > 0) {
            // 上链
            OpenSkuCreateReq createReq = new OpenSkuCreateReq();
            createReq.setUserName(collectionName);
            createReq.setProductName("商品：" + SnowFlake.getSnowFlakeId());
            createReq.setPreviewPic(Constants.DIGITAL_COLLECTIONS_URL + userReceivingRecords.getImage());
            createReq.setTemplateType("PHOTO");
            createReq.setTemplateJson(Constants.DIGITAL_COLLECTIONS_URL + userReceivingRecords.getImage());
            createReq.setIssuerId(Constants.ISSUER_ID);
            createReq.setManufacturerId(Constants.MANUFACTURER_ID);
            createReq.setDescription(dropName);
            createReq.setProductStock(1);
            createReq.setSkuBrightness("1");
            createReq.setOnChain(true);
            createReq.setCreateDrop(true);
            OpenCreateDropReq openCreateDropReq = new OpenCreateDropReq();
            openCreateDropReq.setUserName(collectionName);
            openCreateDropReq.setDropName(dropName);
            openCreateDropReq.setDropCount(1);
            openCreateDropReq.setBusinessId(Constants.BUSINESS_ID);
            createReq.setOpenCreateDropReq(openCreateDropReq);
            createReq.setBusinessId(Constants.BUSINESS_ID);

            // 发送藏品上链
            String jsonString = JSON.toJSONString(createReq);
            String resStr = HttpUtils.post("http://************:8110/test/createSku", jsonString);
            Map<String, Object> map = JSONObject.parseObject(resStr, Map.class);
            Map<String, Object> data = (Map<String, Object>) map.get("data");
            boolean success = (Boolean) data.get("success");
            if (!success) {
                throw new ServiceException("区块链服务错误");
            } else {
                Map<String, Object> res = (Map<String, Object>) data.get("result");
                receivingRecords.setSkuId(res.get("skuId").toString());
                receivingRecords.setDropId(res.get("dropId").toString());
            }
            updateById(receivingRecords);

//            OpenResult<OpenSkuCreateResp> res = blockchainService.createSku(createReq);
//            if (res.isSuccess()) {
//                userReceivingRecords.setDropId(res.getResult().getDropId());
//                userReceivingRecords.setSkuId(res.getResult().getSkuId());
//            } else {
//                throw new ServiceException("区块链异常");
//            }
        }

        return receivingRecords.getId();
    }

    /**
     * 判断是否被领取
     *
     * @param no
     * @return
     */
    @Override
    public Long findIsPickedUp(String no) {
        return count(new LambdaQueryWrapper<UserReceivingRecords>()
                .eq(UserReceivingRecords::getNo, no));
    }

    /**
     * 判断是否被领取
     *
     * @param portfolioId
     * @param seat
     * @param timeList
     * @return
     */
    @Override
    public Long findIsPickedUp(Long portfolioId, String seat, List<String> timeList) {
        return baseMapper.findIsPickedUp(portfolioId, seat, timeList);
    }

    /**
     * 判断是否被领取
     *
     * @param portfolioId
     * @param seat
     * @param time
     * @return
     */
    @Override
    public Long findIsPickedUp(Long portfolioId, String seat, String time) {
        return baseMapper.selectCount(new LambdaQueryWrapper<UserReceivingRecords>()
                .eq(UserReceivingRecords::getPortfolioId, portfolioId)
                .eq(UserReceivingRecords::getSeatNumber, seat)
                .eq(UserReceivingRecords::getTime, time));
    }

    /**
     * 根据藏品ID+类型查询领取数量
     *
     * @param relationId
     * @param badgeType
     * @return
     */
    @Override
    public Long findGetNumber(Long relationId, Integer badgeType) {
        return count(new LambdaQueryWrapper<UserReceivingRecords>()
                .eq(UserReceivingRecords::getRelationId, relationId)
                .eq(UserReceivingRecords::getBadgeType, badgeType));
    }

    /**
     * 根据藏品组合ID+类型查询领取数量
     *
     * @param portfolioId
     * @param badgeType
     * @return
     */
    @Override
    public Long findPortfolioGetNumber(Long portfolioId, Integer badgeType) {
        return count(new LambdaQueryWrapper<UserReceivingRecords>()
                .eq(UserReceivingRecords::getPortfolioId, portfolioId)
                .eq(UserReceivingRecords::getBadgeType, badgeType));
    }

    /**
     * 根据用户ID+类型查询领取数量
     *
     * @param userId
     * @param badgeType
     * @return
     */
    @Override
    public Long findGetNumberByUser(Long userId, Integer badgeType) {
        return count(new LambdaQueryWrapper<UserReceivingRecords>()
                .eq(UserReceivingRecords::getUserId, userId)
                .eq(UserReceivingRecords::getBadgeType, badgeType));
    }

    /**
     * 查询用户领取数字藏品
     *
     * @param request
     * @return
     */
    @Override
    public List<UserReceivingRecordsResponse> listByPage(UserReceivingRecordsRequest request) {
        clearUnviewed(request.getBadgeType());
        request.setUserId(SecurityUtils.getUserId());
        return baseMapper.listByPage(request);
    }

    /**
     * 查询用户纸质票总金额
     *
     * @param request
     * @return
     */
    @Override
    public BigDecimal findSumPrice(UserReceivingRecordsRequest request) {
        request.setUserId(SecurityUtils.getUserId());
        return baseMapper.findSumPrice(request);
    }

    /**
     * 查询用户是否观影
     *
     * @param repertoireId
     * @param theaterId
     * @param repertoireInfoDetailId
     * @return
     */
    @Override
    public Long findUserLook(Long repertoireId, Long theaterId, Long repertoireInfoDetailId) {
        return count(new LambdaQueryWrapper<UserReceivingRecords>()
                .eq(UserReceivingRecords::getUserId, SecurityUtils.getUserId())
                .eq(UserReceivingRecords::getRepertoireId, repertoireId)
                .eq(UserReceivingRecords::getTheaterId, theaterId)
                .eq(UserReceivingRecords::getRepertoireInfoDetailId, repertoireInfoDetailId)
        );
    }

    /**
     * 随机获取5个已经观影用户回答 （最多为5个）
     *
     * @param repertoireId
     * @param theaterId
     * @return
     */
    @Override
    public List<Long> findRandomUser(Long repertoireId, Long theaterId, Long userId) {
        return baseMapper.findRandomUser(repertoireId, theaterId, userId);
    }

    /**
     * 数字头像
     *
     * @param request
     * @return
     */
    @Override
    public List<UserReceivingRecordsResponse> listByDigitalAvatar(UserReceivingRecordsRequest request) {
        request.setUserId(SecurityUtils.getUserId());
        return baseMapper.listByDigitalAvatar(request);
    }

    /**
     * 我的勋章（分组）
     *
     * @param request
     * @return
     */
    @Override
    public List<UserReceivingRecordsResponse> listByRankMedal(UserReceivingRecordsRequest request) {
        clearUnviewed(BadgeTypeFlag.RANK_MEDAL.getCode());
        request.setUserId(SecurityUtils.getUserId());
        return baseMapper.listByRankMedal(request);
    }

    /**
     * 电子票详情
     *
     * @param id
     * @return
     */
    @Override
    public UserReceivingRecordsResponse detailsByRepertoireTicket(Long id) {
        return baseMapper.detailsByRepertoireTicket(id, SecurityUtils.getUserId(), BadgeTypeFlag.ELECTRONIC_TICKET.getCode());
    }

    /**
     * 电子头像详情
     *
     * @param id
     * @param upgradeStatus
     * @return
     */
    @Override
    public UserReceivingRecordsResponse detailsByDigitalAvatar(Long id, Integer upgradeStatus) {
        return baseMapper.detailsByDigitalAvatar(id, SecurityUtils.getUserId(), BadgeTypeFlag.DIGITAL_AVATAR.getCode(), upgradeStatus);
    }

    /**
     * 纪念徽章详情
     *
     * @param id
     * @param upgradeStatus
     * @return
     */
    @Override
    public UserReceivingRecordsResponse detailsBySouvenirBadge(Long id, Integer upgradeStatus) {
        return baseMapper.detailsBySouvenirBadge(id, SecurityUtils.getUserId(), BadgeTypeFlag.SOUVENIR_BADGE.getCode(), upgradeStatus);
    }

    /**
     * 等级勋章详情
     *
     * @param id
     * @return
     */
    @Override
    public UserReceivingRecordsResponse detailsByRankMedal(Long id) {
        return baseMapper.detailsByRankMedal(id, SecurityUtils.getUserId(), BadgeTypeFlag.RANK_MEDAL.getCode());
    }

    /**
     * 查询用户消费次数
     *
     * @param theaterId
     * @param repertoireId
     * @param userId
     * @return
     */
    @Override
    public Long findUserConsumptionCount(Long theaterId, Long repertoireId, Long userId) {
        if (repertoireId != null) {
            return count(new LambdaQueryWrapper<UserReceivingRecords>()
                    .eq(UserReceivingRecords::getUserId, userId)
                    .eq(UserReceivingRecords::getRepertoireId, repertoireId)
                    .eq(UserReceivingRecords::getBadgeType, BadgeTypeFlag.ELECTRONIC_TICKET.getCode()));
        } else {
            return count(new LambdaQueryWrapper<UserReceivingRecords>()
                    .eq(UserReceivingRecords::getUserId, userId)
                    .eq(UserReceivingRecords::getTheaterId, repertoireId)
                    .eq(UserReceivingRecords::getBadgeType, BadgeTypeFlag.ELECTRONIC_TICKET.getCode()));
        }
    }

    /**
     * 查询上链藏品
     *
     * @return
     */
    @Override
    public List<UserReceivingRecords> findUserReceivingRecords() {
        return baseMapper.findUserReceivingRecords();
    }

    /**
     * 修改藏品佩戴状态
     *
     * @param userAdornCollection
     * @return
     */
    @Override
    @Transactional
    public Long updateAdornStatus(UserAdornCollection userAdornCollection) {
        if (userAdornCollection.getId() != null && userAdornCollection.getId() > 0L) {
            userAdornCollectionService.removeById(userAdornCollection.getId());
            return userAdornCollection.getId();
        }
        List<UserAdornCollection> userAdornCollectionList = userAdornCollectionService.findUserAdornByBadgeType(userAdornCollection.getBadgeType());

        if (CollectionUtils.isNotEmpty(userAdornCollectionList)) {
            if (BadgeTypeFlag.SOUVENIR_BADGE.getCode().equals(userAdornCollection.getBadgeType())) {
                UserAdornCollection adornCollection = userAdornCollectionList.get(0);
                adornCollection.setUserReceivingRecordsId(userAdornCollection.getUserReceivingRecordsId());
                adornCollection.setUpgradeStatus(userAdornCollection.getUpgradeStatus());
                adornCollection.setAdornImage(userAdornCollection.getAdornImage());
                userAdornCollectionService.updateById(adornCollection);
            } else {
                userAdornCollection.setUserId(SecurityUtils.getUserId());
                userAdornCollectionService.save(userAdornCollection);
                if (userAdornCollectionList.size() >= 4) {
                    userAdornCollectionService.removeById(userAdornCollectionList.get(userAdornCollectionList.size() - 1).getId());
                }
            }
        } else {
            userAdornCollection.setUserId(SecurityUtils.getUserId());
            userAdornCollectionService.save(userAdornCollection);
        }
        return userAdornCollection.getUserReceivingRecordsId();
    }

    /**
     * 清除未查看藏品信息
     *
     * @param badgeType
     * @return
     */
    @Override
    @Transactional
    public void clearUnviewed(Integer badgeType) {
        List<UserReceivingRecords> userReceivingRecords = list(new LambdaQueryWrapper<UserReceivingRecords>()
                .eq(UserReceivingRecords::getUserId, SecurityUtils.getUserId())
                .eq(UserReceivingRecords::getLookFlag, LookFlag.DEFAULT.getCode())
                .eq(UserReceivingRecords::getBadgeType, badgeType));
        if (CollectionUtils.isNotEmpty(userReceivingRecords)) {
            userReceivingRecords.forEach(item -> item.setLookFlag(LookFlag.PASS.getCode()));
            updateBatchById(userReceivingRecords);
        }
    }

    /**
     * 藏品详情
     *
     * @param id
     * @return
     */
    @Override
    public UserReceivingRecordsResponse details(Long id) {
        return baseMapper.details(id);
    }

    /**
     * 查询领取组合列表
     *
     * @param portfolioNo
     * @return
     */
    @Override
    public List<UserReceivingRecords> findUserReceivingRecordsByPortfolioNo(String portfolioNo) {
        return list(new LambdaQueryWrapper<UserReceivingRecords>()
                .eq(UserReceivingRecords::getPortfolioNo, portfolioNo));
    }

    /**
     * 查询领取组合列表
     *
     * @param orderNo
     * @return
     */
    @Override
    public List<UserReceivingRecords> findUserReceivingRecordsByOrderNo(String orderNo) {
        return baseMapper.findUserReceivingRecordsByOrderNo(orderNo);
    }

    /**
     * 查询用户数字藏品领取记录
     *
     * @param portfolioNo
     * @return
     */
    @Override
    public List<UserReceivingRecordsResponse> findReceivingRecordsByPortfolioNo(String portfolioNo) {
        return baseMapper.findReceivingRecordsByPortfolioNo(portfolioNo);
    }

    /**
     * 修改
     *
     * @param userReceivingRecords
     */
    @Override
    public void updateUserReceivingRecord(UserReceivingRecords userReceivingRecords) {
        baseMapper.updateUserReceivingRecord(userReceivingRecords);
    }

    /**
     * 查询用户数字头像
     *
     * @param request
     * @return
     */
    @Override
    public List<UserReceivingRecordsResponse> findUserDigitalAvatar(UserReceivingRecordsRequest request) {
        List<UserReceivingRecords> list = list(new LambdaQueryWrapper<UserReceivingRecords>()
                .select(UserReceivingRecords::getId, UserReceivingRecords::getLookFlag)
                .eq(UserReceivingRecords::getUserId, SecurityUtils.getUserId())
                .eq(UserReceivingRecords::getLookFlag, LookFlag.DEFAULT.getCode())
                .eq(UserReceivingRecords::getBadgeType, BadgeTypeFlag.DIGITAL_AVATAR.getCode()));
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(item -> item.setLookFlag(LookFlag.PASS.getCode()));
            updateBatchById(list);
        }
        request.setUserId(SecurityUtils.getUserId());
        return baseMapper.findUserDigitalAvatar(request);
    }

    /**
     * 查询用户电子头像数量
     *
     * @return
     */
    @Override
    public Long findDigitalAvatarCountByUser() {
        return baseMapper.findDigitalAvatarCountByUser(SecurityUtils.getUserId());
    }

    /**
     * 查询用户纪念勋章
     *
     * @param request
     * @return
     */
    @Override
    public List<UserReceivingRecordsResponse> findUserSouvenirBadge(UserReceivingRecordsRequest request) {
        return baseMapper.findUserSouvenirBadge(request);
    }

    /**
     * 查询用户观影剧目、剧场次数
     *
     * @param userId
     * @param theaterId
     * @param code
     * @return
     */
    @Override
    public UserGetResponse findUserLookCount(Long userId, Long theaterId, Integer code) {
        return baseMapper.findUserLookCount(userId, theaterId, code);
    }

    /**
     * 查询用户是否观看过指定场次
     *
     * @param userId
     * @param theaterId
     * @param lookNumber
     * @param rankMedalId
     * @param rankMedalInfoId
     * @param repertoireInfoId
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public Long findUserLookAssignRepertoire(Long userId,
                                             Long theaterId,
                                             Integer lookNumber,
                                             Long rankMedalId,
                                             Long rankMedalInfoId,
                                             Long repertoireInfoId,
                                             Date startTime,
                                             Date endTime) {
        return baseMapper.findUserLookAssignRepertoire(userId, theaterId, lookNumber, rankMedalId, rankMedalInfoId, repertoireInfoId, startTime, endTime);
    }

    /**
     * 查询用户电子票
     *
     * @param request
     * @return
     */
    @Override
    public List<TicketGroupResponse> findElectronicTicketByUserId(TicketGroupRequest request) {
        request.setUserId(SecurityUtils.getUserId());
        if (StringUtils.isNotEmpty(request.getKeyNameStr())) {
            request.setKeyNameList(request.getKeyNameStr().split(","));
        }
        return baseMapper.findElectronicTicketByUserId(request);
    }
}
