package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.UserAdornCollection;
import com.youying.common.core.domain.entity.UserReceivingRecords;
import com.youying.system.domain.ticketgroup.TicketGroupRequest;
import com.youying.system.domain.ticketgroup.TicketGroupResponse;
import com.youying.system.domain.userreceivingrecords.AddUserReceivingRecordsRequest;
import com.youying.system.domain.userreceivingrecords.UserGetResponse;
import com.youying.system.domain.userreceivingrecords.UserReceivingRecordsRequest;
import com.youying.system.domain.userreceivingrecords.UserReceivingRecordsResponse;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 用户数字藏品领取记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface UserReceivingRecordsService extends IService<UserReceivingRecords> {

    /**
     * 添加用户数字藏品领取记录
     *
     * @param request
     * @return
     */
    List<UserReceivingRecords> add(AddUserReceivingRecordsRequest request);

    /**
     * 添加数字头像、纪念勋章
     *
     * @param userReceivingRecords
     * @return
     */
    Long add(UserReceivingRecords userReceivingRecords);

    /**
     * 修改用户数字藏品领取记录
     *
     * @param userReceivingRecords
     * @return
     */
    Long update(UserReceivingRecords userReceivingRecords);

    /**
     * 判断是否被领取
     *
     * @param no
     * @return
     */
    Long findIsPickedUp(String no);

    /**
     * 判断是否被领取
     *
     * @param portfolioId
     * @param seat
     * @param timeList
     * @return
     */
    Long findIsPickedUp(Long portfolioId, String seat, List<String> timeList);

    /**
     * 判断是否被领取
     *
     * @param portfolioId
     * @param seat
     * @param time
     * @return
     */
    Long findIsPickedUp(Long portfolioId, String seat, String time);

    /**
     * 根据藏品ID+类型查询领取数量
     *
     * @param relationId
     * @param badgeType
     * @return
     */
    Long findGetNumber(Long relationId, Integer badgeType);

    /**
     * 根据藏品组合ID+类型查询领取数量
     *
     * @param portfolioId
     * @param badgeType
     * @return
     */
    Long findPortfolioGetNumber(Long portfolioId, Integer badgeType);

    /**
     * 根据用户ID+类型查询领取数量
     *
     * @param userId
     * @param badgeType
     * @return
     */
    Long findGetNumberByUser(Long userId, Integer badgeType);

    /**
     * 查询用户领取数字藏品
     *
     * @param request
     * @return
     */
    List<UserReceivingRecordsResponse> listByPage(UserReceivingRecordsRequest request);

    /**
     * 查询用户纸质票总金额
     *
     * @param request
     * @return
     */
    BigDecimal findSumPrice(UserReceivingRecordsRequest request);

    /**
     * 查询用户是否观影
     *
     * @param repertoireId
     * @param theaterId
     * @param repertoireInfoDetailId
     * @return
     */
    Long findUserLook(Long repertoireId,
                      Long theaterId,
                      Long repertoireInfoDetailId);

    /**
     * 随机获取5个已经观影用户回答 （最多为5个）
     *
     * @param repertoireId
     * @param theaterId
     * @return
     */
    List<Long> findRandomUser(Long repertoireId,
                              Long theaterId,
                              Long userId);

    /**
     * 数字头像（分组）
     *
     * @param request
     * @return
     */
    List<UserReceivingRecordsResponse> listByDigitalAvatar(UserReceivingRecordsRequest request);

    /**
     * 我的勋章（分组）
     *
     * @param request
     * @return
     */
    List<UserReceivingRecordsResponse> listByRankMedal(UserReceivingRecordsRequest request);

    /**
     * 电子票详情
     *
     * @param id
     * @return
     */
    UserReceivingRecordsResponse detailsByRepertoireTicket(Long id);

    /**
     * 电子头像详情
     *
     * @param id
     * @param upgradeStatus
     * @return
     */
    UserReceivingRecordsResponse detailsByDigitalAvatar(Long id, Integer upgradeStatus);

    /**
     * 纪念徽章详情
     *
     * @param id
     * @param upgradeStatus
     * @return
     */
    UserReceivingRecordsResponse detailsBySouvenirBadge(Long id, Integer upgradeStatus);

    /**
     * 等级勋章详情
     *
     * @param id
     * @return
     */
    UserReceivingRecordsResponse detailsByRankMedal(Long id);

    /**
     * 查询用户消费次数（电子票）
     *
     * @param theaterId
     * @param repertoireId
     * @param userId
     * @return
     */
    Long findUserConsumptionCount(Long theaterId, Long repertoireId, Long userId);

    /**
     * 查询上链藏品
     *
     * @return
     */
    List<UserReceivingRecords> findUserReceivingRecords();

    /**
     * 修改藏品佩戴状态
     *
     * @param userAdornCollection
     * @return
     */
    Long updateAdornStatus(UserAdornCollection userAdornCollection);

    /**
     * 清除未查看藏品信息
     *
     * @param badgeType
     * @return
     */
    void clearUnviewed(Integer badgeType);

    /**
     * 藏品详情
     *
     * @param id
     * @return
     */
    UserReceivingRecordsResponse details(Long id);

    /**
     * 查询领取组合列表
     *
     * @param portfolioNo
     * @return
     */
    List<UserReceivingRecords> findUserReceivingRecordsByPortfolioNo(String portfolioNo);

    /**
     * 查询领取组合列表
     *
     * @param orderNo
     * @return
     */
    List<UserReceivingRecords> findUserReceivingRecordsByOrderNo(String orderNo);

    /**
     * 查询用户数字藏品领取记录
     *
     * @param portfolioNo
     * @return
     */
    List<UserReceivingRecordsResponse> findReceivingRecordsByPortfolioNo(String portfolioNo);

    /**
     * 修改
     *
     * @param userReceivingRecords
     */
    void updateUserReceivingRecord(UserReceivingRecords userReceivingRecords);

    /**
     * 查询用户数字头像
     *
     * @param request
     * @return
     */
    List<UserReceivingRecordsResponse> findUserDigitalAvatar(UserReceivingRecordsRequest request);

    /**
     * 查询用户电子头像数量
     *
     * @return
     */
    Long findDigitalAvatarCountByUser();

    /**
     * 查询用户纪念勋章
     *
     * @param request
     * @return
     */
    List<UserReceivingRecordsResponse> findUserSouvenirBadge(UserReceivingRecordsRequest request);

    /**
     * 查询用户观影剧目、剧场次数
     *
     * @param userId
     * @param theaterId
     * @param code
     * @return
     */
    UserGetResponse findUserLookCount(Long userId, Long theaterId, Integer code);

    /**
     * 查询用户是否有领取资格
     *
     * @param userId
     * @param theaterId
     * @param lookNumber
     * @param rankMedalId
     * @param rankMedalInfoId
     * @param repertoireInfoId
     * @param startTime
     * @param endTime
     * @return
     */
    Long findUserLookAssignRepertoire(Long userId,
                                      Long theaterId,
                                      Integer lookNumber,
                                      Long rankMedalId,
                                      Long rankMedalInfoId,
                                      Long repertoireInfoId,
                                      Date startTime,
                                      Date endTime);

    /**
     * 查询用户电子票
     *
     * @param request
     * @return
     */
    List<TicketGroupResponse> findElectronicTicketByUserId(TicketGroupRequest request);
}
