package com.youying.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.AutoReply;
import com.youying.system.mapper.AutoReplyMapper;
import com.youying.system.service.AutoReplyService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 剧目剧场自动回复表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@Service
public class AutoReplyServiceImpl extends ServiceImpl<AutoReplyMapper, AutoReply> implements AutoReplyService {

    /**
     * 查询剧目或者剧场自动回复
     *
     * @param threadId
     * @param repertoireId
     * @return
     */
    @Override
    public AutoReply findAutoReply(Long threadId, Long repertoireId) {
        if (threadId != null) {
            return getOne(new LambdaQueryWrapper<AutoReply>()
                    .eq(AutoReply::getTheaterId, threadId));
        } else {
            return getOne(new LambdaQueryWrapper<AutoReply>()
                    .eq(AutoReply::getRepertoireId, repertoireId));
        }
    }
}
