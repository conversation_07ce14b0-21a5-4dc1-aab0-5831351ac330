package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.Theater;
import com.youying.system.domain.repertoire.RepertoireDisplayResponse;
import com.youying.system.domain.theater.TheaterRequest;
import com.youying.system.domain.theater.TheaterResponse;

import java.util.List;

/**
 * <p>
 * 剧场表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface TheaterService extends IService<Theater> {

    /**
     * 查询首页剧场
     *
     * @param userId
     * @return
     */
    List<TheaterResponse> listByIndex(Long userId);

    /**
     * 剧场列表
     *
     * @param request
     * @return
     */
    List<TheaterResponse> listByPage(TheaterRequest request);

    /**
     * 查询剧场详情
     *
     * @param id
     * @param userId
     * @return
     */
    TheaterResponse details(Long id, Long userId);

    /**
     * 模糊查询是否存在该剧场
     *
     * @param str
     * @return
     */
    Theater findTheaterName(String str);

    /**
     * 查询剧场橱窗列表
     *
     * @param theaterId
     * @return
     */
    List<RepertoireDisplayResponse> findTheaterDisplay(Long theaterId);
}
