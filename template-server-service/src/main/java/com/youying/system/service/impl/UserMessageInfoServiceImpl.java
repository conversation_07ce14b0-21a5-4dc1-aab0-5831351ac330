package com.youying.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.UserMessageInfo;
import com.youying.common.utils.SecurityUtils;
import com.youying.system.mapper.UserMessageInfoMapper;
import com.youying.system.service.UserMessageInfoService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 用户消息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@Service
public class UserMessageInfoServiceImpl extends ServiceImpl<UserMessageInfoMapper, UserMessageInfo> implements UserMessageInfoService {

    /**
     * 根据用户消息ID删除消息详情
     *
     * @param ids
     */
    @Override
    @Transactional
    public void deleteByUserMessageId(List<Long> ids) {
        remove(new LambdaQueryWrapper<UserMessageInfo>()
                .eq(UserMessageInfo::getUserId, SecurityUtils.getUserId())
                .in(UserMessageInfo::getUserMessageId, ids));
    }
}
