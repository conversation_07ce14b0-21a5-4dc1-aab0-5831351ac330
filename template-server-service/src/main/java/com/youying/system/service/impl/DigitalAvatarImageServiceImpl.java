package com.youying.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.DigitalAvatarImage;
import com.youying.system.mapper.DigitalAvatarImageMapper;
import com.youying.system.service.DigitalAvatarImageService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 数字头像图片表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@Service
public class DigitalAvatarImageServiceImpl extends ServiceImpl<DigitalAvatarImageMapper, DigitalAvatarImage> implements DigitalAvatarImageService {

    /**
     * 根据数字头像ID随机获取组件
     *
     * @param digitalAvatarId
     * @return
     */
    @Override
    public List<String> findDigitalAvatarImageByDigitalAvatarId(Long digitalAvatarId) {
        List<DigitalAvatarImage> avatarImages = list(new LambdaQueryWrapper<DigitalAvatarImage>()
                .eq(DigitalAvatarImage::getDigitalAvatarId, digitalAvatarId));
        if (CollectionUtils.isNotEmpty(avatarImages)) {
            List<String> imageList = new ArrayList<>();
            Map<Integer, List<DigitalAvatarImage>> listMap = avatarImages.stream().collect(Collectors.groupingBy(item -> item.getGroup()));
            for (Map.Entry<Integer, List<DigitalAvatarImage>> map : listMap.entrySet()) {
                List<DigitalAvatarImage> images = map.getValue();
                // 随机获取一张图
                int number = (int) (Math.random() * images.size());
                imageList.add(images.get(number).getImage());
            }
            return imageList;
        }
        return null;
    }

    /**
     * 查询电子头像组件
     *
     * @param digitalAvatarId
     * @return
     */
    @Override
    public List<DigitalAvatarImage> findAvatarImageByDigitalAvatar(Long digitalAvatarId) {
        return list(new LambdaQueryWrapper<DigitalAvatarImage>()
                .eq(DigitalAvatarImage::getDigitalAvatarId, digitalAvatarId));
    }
}
