package com.youying.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.UserAdornCollection;
import com.youying.common.utils.SecurityUtils;
import com.youying.system.mapper.UserAdornCollectionMapper;
import com.youying.system.service.UserAdornCollectionService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 用户藏品佩戴表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-25
 */
@Service
public class UserAdornCollectionServiceImpl extends ServiceImpl<UserAdornCollectionMapper, UserAdornCollection> implements UserAdornCollectionService {

    /**
     * 根据佩戴类型查询用户佩戴情况
     *
     * @param badgeType
     * @return
     */
    @Override
    public List<UserAdornCollection> findUserAdornByBadgeType(Integer badgeType) {
        return list(new LambdaQueryWrapper<UserAdornCollection>()
                .eq(UserAdornCollection::getUserId, SecurityUtils.getUserId())
                .eq(UserAdornCollection::getBadgeType, badgeType));
    }
}
