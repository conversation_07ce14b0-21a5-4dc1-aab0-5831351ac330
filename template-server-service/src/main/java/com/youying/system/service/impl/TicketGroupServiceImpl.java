package com.youying.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.common.PullResponse;
import com.youying.common.core.domain.entity.TicketGroup;
import com.youying.common.core.domain.entity.UserTicketGroup;
import com.youying.common.enums.Enums.DataFlag;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.common.exception.ServiceException;
import com.youying.common.utils.SecurityUtils;
import com.youying.system.domain.ticketgroup.AddTicketGroupRequest;
import com.youying.system.domain.ticketgroup.TicketGroupResponse;
import com.youying.system.mapper.TicketGroupMapper;
import com.youying.system.service.TicketGroupService;
import com.youying.system.service.UserTicketGroupService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 剧目分组表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-08
 */
@Service
public class TicketGroupServiceImpl extends ServiceImpl<TicketGroupMapper, TicketGroup> implements TicketGroupService {
    private static final Integer MAX_ADD_COUNT = 5;
    @Autowired
    private UserTicketGroupService userTicketGroupService;

    /**
     * 添加改剧目分组数据
     *
     * @param request
     * @return
     */
    @Override
    @Transactional
    public Long add(AddTicketGroupRequest request) {
        Integer count = baseMapper.findTicketGroupCount(DataFlag.USER.getCode(), SecurityUtils.getUserId());
        if (MAX_ADD_COUNT < count + 1) {
            throw new ServiceException("最多可创建5个分组");
        }
        TicketGroup ticketGroup = new TicketGroup();
        BeanUtil.copyProperties(request, ticketGroup);
        ticketGroup.setId(null);
        ticketGroup.setType(DataFlag.USER.getCode());
        ticketGroup.setUserId(SecurityUtils.getUserId());
        ticketGroup.setStatus(StatusFlag.OK.getCode());
        baseMapper.insert(ticketGroup);

        Long ticketGroupId = ticketGroup.getId();

        List<UserTicketGroup> userTicketGroupList = request.getUserTicketGroupList();
        if (CollectionUtils.isNotEmpty(userTicketGroupList)) {
            Long userId = SecurityUtils.getUserId();
            for (UserTicketGroup userTicketGroup : userTicketGroupList) {
                userTicketGroup.setId(null);
                userTicketGroup.setTicketGroupId(ticketGroupId);
                userTicketGroup.setUserId(userId);
                userTicketGroup.setType(DataFlag.USER.getCode());
            }
            userTicketGroupService.saveBatch(userTicketGroupList);
        }

        return ticketGroupId;
    }

    /**
     * 修改剧目分组数据
     *
     * @param request
     * @return
     */
    @Override
    @Transactional
    public Long update(AddTicketGroupRequest request) {
        Long ticketGroupId = request.getId();
        TicketGroup ticketGroup = baseMapper.selectById(ticketGroupId);
        ticketGroup.setName(request.getName());
        baseMapper.updateById(ticketGroup);

        List<UserTicketGroup> userTicketGroupList = request.getUserTicketGroupList();
        // 删除分组内电子票
        userTicketGroupService.deleteByGroupId(ticketGroupId);
        if (CollectionUtils.isNotEmpty(userTicketGroupList)) {
            Long userId = SecurityUtils.getUserId();
            for (UserTicketGroup userTicketGroup : userTicketGroupList) {
                userTicketGroup.setTicketGroupId(ticketGroupId);
                userTicketGroup.setUserId(userId);
                userTicketGroup.setType(ticketGroup.getType());
            }
            userTicketGroupService.saveOrUpdateBatch(userTicketGroupList);
        }

        return ticketGroupId;
    }

    /**
     * 查询用户分类数据
     *
     * @param id
     * @return
     */
    @Override
    public List<TicketGroupResponse> details(Long id) {
        return userTicketGroupService.findTicketGroupByUserId(id, SecurityUtils.getUserId());
    }

    /**
     * 删除剧目分组
     *
     * @param id
     * @return
     */
    @Override
    @Transactional
    public Long delete(Long id) {
        baseMapper.deleteById(id);
        userTicketGroupService.deleteByGroupId(id);
        return id;
    }

    /**
     * 查询用户电子票分组数量
     *
     * @return
     */
    @Override
    public Integer findTicketGroupCount() {
        return baseMapper.findTicketGroupCount(DataFlag.USER.getCode(), SecurityUtils.getUserId());
    }

    /**
     * 查询用户电子票分组下拉
     *
     * @return
     */
    @Override
    public List<PullResponse> pull() {
        return baseMapper.pull(SecurityUtils.getUserId());
    }

    /**
     * 查询用户电子票分组
     *
     * @return
     */
    @Override
    public List<TicketGroupResponse> findTicketGroupList() {
        return baseMapper.findTicketGroupList(SecurityUtils.getUserId());
    }
}
