package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.Message;
import com.youying.system.domain.usermessage.UserMessageResponse;

import java.util.List;

/**
 * <p>
 * 群发消息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface MessageService extends IService<Message> {

    /**
     * 查询群发消息表列表(分页)
     *
     * @return
     */
    List<UserMessageResponse> listByPage();
}
