package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.UserAdornCollection;

import java.util.List;

/**
 * <p>
 * 用户藏品佩戴表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-25
 */
public interface UserAdornCollectionService extends IService<UserAdornCollection> {

    /**
     * 根据佩戴类型查询用户佩戴情况
     *
     * @param badgeType
     * @return
     */
    List<UserAdornCollection> findUserAdornByBadgeType(Integer badgeType);
}
