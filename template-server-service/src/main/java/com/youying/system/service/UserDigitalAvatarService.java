package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.UserDigitalAvatar;

/**
 * <p>
 * 用户电子头像表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-29
 */
public interface UserDigitalAvatarService extends IService<UserDigitalAvatar> {

    /**
     * 查询一条未占用升级数字头像
     *
     * @param portfolioId
     * @return
     */
    UserDigitalAvatar getUserDigitalAvatar(Long portfolioId, Long relationId);

    /**
     * 修改用户数字头像
     *
     * @param userDigitalAvatar
     */
    void updateUserDigitalAvatar(UserDigitalAvatar userDigitalAvatar);
}
