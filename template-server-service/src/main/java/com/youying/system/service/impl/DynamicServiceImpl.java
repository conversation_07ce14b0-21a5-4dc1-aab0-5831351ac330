package com.youying.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.Dynamic;
import com.youying.system.domain.dynamic.DynamicRequest;
import com.youying.system.domain.dynamic.DynamicResponse;
import com.youying.system.mapper.DynamicMapper;
import com.youying.system.service.DynamicService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 剧目剧场动态表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@Service
public class DynamicServiceImpl extends ServiceImpl<DynamicMapper, Dynamic> implements DynamicService {

    /**
     * 查询剧目剧场动态表列表(分页)
     *
     * @param request
     * @return
     */
    @Override
    public List<DynamicResponse> listByPage(DynamicRequest request) {
        return baseMapper.listByPage(request);
    }

    /**
     * 查询剧目剧场动态表详情
     *
     * @param id
     * @return
     */
    @Override
    public DynamicResponse details(Long id, Long userId) {
        return baseMapper.details(id, userId);
    }
}
