package com.youying.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.RankMedalInfo;
import com.youying.common.core.domain.entity.SouvenirBadge;
import com.youying.common.core.domain.entity.SouvenirBadgeRequire;
import com.youying.common.core.domain.entity.UserPushCollection;
import com.youying.common.enums.Enums.BadgeTypeFlag;
import com.youying.common.enums.Enums.MerchantCategoryFlag;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.system.domain.userreceivingrecords.UserGetResponse;
import com.youying.system.mapper.SouvenirBadgeRequireMapper;
import com.youying.system.service.RankMedalInfoService;
import com.youying.system.service.SouvenirBadgeRequireService;
import com.youying.system.service.SouvenirBadgeService;
import com.youying.system.service.UserPushCollectionService;
import com.youying.system.service.UserReceivingRecordsService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 纪念徽章领取规则表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@Service
public class SouvenirBadgeRequireServiceImpl extends ServiceImpl<SouvenirBadgeRequireMapper, SouvenirBadgeRequire> implements SouvenirBadgeRequireService {
    @Autowired
    UserPushCollectionService userPushCollectionService;
    @Autowired
    private SouvenirBadgeService souvenirBadgeService;
    @Autowired
    private UserReceivingRecordsService userReceivingRecordsService;
    @Autowired
    private RankMedalInfoService rankMedalInfoService;

    /**
     * 查询可领取纪念徽章
     *
     * @param theaterId
     * @param userId
     */
    @Override
    public void findUserConsumptionCount(Long theaterId, Long userId) {
        List<SouvenirBadge> souvenirBadgeList = souvenirBadgeService.findUserConsumption(theaterId);

        if (CollectionUtils.isNotEmpty(souvenirBadgeList)) {
            UserGetResponse user = userReceivingRecordsService.findUserLookCount(userId, theaterId, MerchantCategoryFlag.THEATER.getCode());
            List<UserPushCollection> userPushCollectionList = new ArrayList<UserPushCollection>();
            UserPushCollection userPushCollection = new UserPushCollection();
            for (SouvenirBadge souvenirBadge : souvenirBadgeList) {
                if (userPushCollectionService.findUserGet(userId, souvenirBadge.getId(), BadgeTypeFlag.SOUVENIR_BADGE.getCode())) {
                    continue;
                }
                SouvenirBadgeRequire souvenirBadgeRequire = getOne(new LambdaQueryWrapper<SouvenirBadgeRequire>()
                        .eq(SouvenirBadgeRequire::getSouvenirBadgeId, souvenirBadge.getId()));

                // 判断观影次数
                if (souvenirBadgeRequire.getLookNumber() > 0 && user.getNumber() < souvenirBadgeRequire.getLookNumber()) {
                    continue;
                }
                RankMedalInfo rankMedalInfo = rankMedalInfoService.getById(souvenirBadgeRequire.getRankMedalInfoId());
                Long count = userReceivingRecordsService.findUserLookAssignRepertoire(
                        userId,
                        souvenirBadge.getTheaterId(),
                        souvenirBadgeRequire.getTimeLookNumber(),
                        rankMedalInfo == null ? 0 : rankMedalInfo.getRankMedalId(),
                        souvenirBadgeRequire.getRankMedalInfoId(),
                        souvenirBadgeRequire.getRepertoireInfoDetailId(),
                        souvenirBadgeRequire.getStartTime(),
                        souvenirBadgeRequire.getEndTime());
                if (count > 0) {
                    userPushCollection = new UserPushCollection();
                    userPushCollection.setUserId(user.getUserId());
                    userPushCollection.setTheaterId(theaterId);
                    userPushCollection.setRepertoireId(0L);
                    userPushCollection.setRankMedalId(0L);
                    userPushCollection.setRankMedalInfoId(0L);
                    userPushCollection.setSouvenirBadgeId(souvenirBadge.getId());
                    userPushCollection.setStatus(StatusFlag.PROHIBITION.getCode());
                    userPushCollectionList.add(userPushCollection);
                }
            }
            if (CollectionUtils.isNotEmpty(userPushCollectionList)) {
                userPushCollectionService.saveBatch(userPushCollectionList);
            }
        }
    }
}
