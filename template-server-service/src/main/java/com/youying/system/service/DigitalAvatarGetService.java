package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.DigitalAvatarGet;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-30
 */
public interface DigitalAvatarGetService extends IService<DigitalAvatarGet> {

    /**
     * 查询纸质票电子头像图层
     *
     * @param no
     * @return
     */
    List<String> findDigitalAvatarGetByNo(String no);

    /**
     * 保存纸质票数字头像数据
     *
     * @param no
     * @param digitalAvatarId
     * @param digitalAvatarUrl
     */
    void add(String no, Long digitalAvatarId, List<String> digitalAvatarUrl);
}
