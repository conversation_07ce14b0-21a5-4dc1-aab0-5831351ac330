package com.youying.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.RankMedal;
import com.youying.common.core.domain.entity.RankMedalInfo;
import com.youying.common.utils.SecurityUtils;
import com.youying.system.mapper.RankMedalMapper;
import com.youying.system.service.RankMedalService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 等级勋章表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@Service
public class RankMedalServiceImpl extends ServiceImpl<RankMedalMapper, RankMedal> implements RankMedalService {

    /**
     * 查询可领取等级勋章
     *
     * @param theaterId
     * @param repertoireId
     * @param amount
     * @param userConsumptionCount
     * @return
     */
    @Override
    public List<RankMedalInfo> findRankMedalCanHave(Long theaterId, Long repertoireId, BigDecimal amount, Long userConsumptionCount) {
        List<RankMedalInfo> list = new ArrayList<>();
        if (theaterId != null && theaterId != 0) {
            List<RankMedalInfo> medalInfos = baseMapper.findRankMedalCanHave(SecurityUtils.getUserId(), theaterId, null, amount, userConsumptionCount);
            if (CollectionUtils.isNotEmpty(medalInfos)) {
                list.addAll(medalInfos);
            }
        }
        if (repertoireId != null && repertoireId != 0) {
            List<RankMedalInfo> medalInfos = baseMapper.findRankMedalCanHave(SecurityUtils.getUserId(), null, repertoireId, amount, userConsumptionCount);
            if (CollectionUtils.isNotEmpty(medalInfos)) {
                list.addAll(medalInfos);
            }
        }
        return list;
    }
}
