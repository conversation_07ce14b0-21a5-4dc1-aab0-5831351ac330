package com.youying.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wechat.pay.java.service.payments.model.Transaction;
import com.wechat.pay.java.service.payments.model.Transaction.TradeStateEnum;
import com.youying.common.core.domain.entity.Portfolio;
import com.youying.common.core.domain.entity.Repertoire;
import com.youying.common.core.domain.entity.UserDigitalAvatar;
import com.youying.common.core.domain.entity.UserOrder;
import com.youying.common.core.domain.entity.UserReceivingRecords;
import com.youying.common.core.page.PageDomain;
import com.youying.common.enums.Enums.BadgeTypeFlag;
import com.youying.common.enums.Enums.FreeFlag;
import com.youying.common.enums.Enums.OrderQueryType;
import com.youying.common.enums.Enums.PayStatusFlag;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.common.exception.ServiceException;
import com.youying.common.pay.PayInfo;
import com.youying.common.pay.PaymentContext;
import com.youying.common.pay.PaymentStrategy;
import com.youying.common.pay.WechatPay;
import com.youying.common.pay.WechatPayment;
import com.youying.common.utils.DateUtils;
import com.youying.common.utils.SecurityUtils;
import com.youying.common.utils.SnowFlake;
import com.youying.system.domain.userorder.UserOrderResponse;
import com.youying.system.mapper.UserOrderMapper;
import com.youying.system.service.PortfolioService;
import com.youying.system.service.RepertoireService;
import com.youying.system.service.UserDigitalAvatarService;
import com.youying.system.service.UserOrderService;
import com.youying.system.service.UserReceivingRecordsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SignatureException;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 用户订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
@Service
@Slf4j
public class UserOrderServiceImpl extends ServiceImpl<UserOrderMapper, UserOrder> implements UserOrderService {
    @Autowired
    private UserReceivingRecordsService userReceivingRecordsService;
    @Autowired
    private PortfolioService portfolioService;
    @Autowired
    private RepertoireService repertoireService;
    @Autowired
    private UserDigitalAvatarService userDigitalAvatarService;

    /**
     * 提起订单
     *
     * @param userReceivingRecordsId
     */
    @Override
    @Transactional
    public WechatPay pay(Long userReceivingRecordsId) throws NoSuchAlgorithmException, SignatureException, InvalidKeyException {
        UserReceivingRecords receivingRecords = userReceivingRecordsService.getById(userReceivingRecordsId);
        if (!BadgeTypeFlag.ELECTRONIC_TICKET.getCode().equals(receivingRecords.getBadgeType())) {
            throw new ServiceException("参数错误");
        }

        Repertoire repertoire = repertoireService.getById(receivingRecords.getRepertoireId());
        Portfolio portfolio = portfolioService.getById(receivingRecords.getPortfolioId());
        List<UserReceivingRecords> userReceivingRecordsList = userReceivingRecordsService.findUserReceivingRecordsByPortfolioNo(receivingRecords.getPortfolioNo());
        BigDecimal price = portfolio.getPrice();
        String orderNo = SnowFlake.getSnowFlakeId();

        String prepayId = "";
        UserOrder userOrder = new UserOrder();
        userOrder.setMerchantId(repertoire.getMerchantId());
        userOrder.setUserId(SecurityUtils.getUserId());
        userOrder.setPayStatus(PayStatusFlag.WITH.getCode());
        userOrder.setPortfolioId(receivingRecords.getPortfolioId());
        userOrder.setOrderNo(orderNo);
        userOrder.setPrepayId(prepayId);
        userOrder.setPayPrice(price);
        userOrder.setUserReceivingRecordsId(userReceivingRecordsId);
        userOrder.setTheaterId(receivingRecords.getTheaterId());
        userOrder.setRepertoireId(receivingRecords.getRepertoireId());
        userOrder.setRelationId(receivingRecords.getRelationId());
        userOrder.setBadgeType(receivingRecords.getBadgeType());
        save(userOrder);
        Long orderId = userOrder.getId();

        if (FreeFlag.FREE.getCode().equals(portfolio.getFree())) {
            // 免费
            Date nowTime = new Date();
            userOrder.setPayStatus(PayStatusFlag.OK.getCode());
            userOrder.setPayTime(nowTime);
            updateById(userOrder);

            userReceivingRecordsList.forEach(item -> {
                item.setUpgradeStatus(StatusFlag.OK.getCode());
                item.setUpgradeTime(nowTime);
                item.setOrderId(orderId);
                item.setOrderNo(orderNo);
            });

            // UserDigitalAvatar userDigitalAvatar = userDigitalAvatarService.getUserDigitalAvatar(receivingRecords.getPortfolioId());
            // if (userDigitalAvatar != null) {
            //     for (UserReceivingRecords userReceivingRecords : userReceivingRecordsList) {
            //         if (BadgeTypeFlag.DIGITAL_AVATAR.getCode().equals(userReceivingRecords.getBadgeType())) {
            //             userDigitalAvatar.setOrderId(orderId);
            //             userDigitalAvatar.setUserId(userReceivingRecords.getUserId());
            //             userDigitalAvatar.setUserReceivingRecordsId(userReceivingRecords.getId());
            //             userDigitalAvatar.setStatus(StatusFlag.OK.getCode());
            //             userDigitalAvatarService.updateUserDigitalAvatar(userDigitalAvatar);
            //             userReceivingRecords.setUpgradeImage(userDigitalAvatar.getImage());
            //         }
            //     }
            // }
        } else {
            userOrder.setRefundStatus(PayStatusFlag.WITH.getCode());

            PaymentContext context = new PaymentContext();
            PaymentStrategy weChatPayment = new WechatPayment();
            context.setPaymentStrategy(weChatPayment);
            prepayId = context.executePayment(orderNo, String.valueOf(price));

            userReceivingRecordsList.forEach(item -> {
                item.setOrderId(orderId);
                item.setOrderNo(orderNo);
            });
        }

        userReceivingRecordsService.updateBatchById(userReceivingRecordsList);

        if (StringUtils.isNotBlank(prepayId)) {
            return WechatPayment.sign(prepayId);
        }

        return new WechatPay();
    }

    /**
     * 根据订单编号查询订单
     *
     * @param orderNo
     * @return
     */
    @Override
    public UserOrder findUserOrderByNo(String orderNo) {
        return baseMapper.findUserOrderByNo(orderNo);
    }

    /**
     * 查询用户订单表
     *
     * @param pageDomain
     * @return
     */
    @Override
    public List<UserOrderResponse> listByPage(PageDomain pageDomain) {
        return baseMapper.listByPage(SecurityUtils.getUserId(), pageDomain);
    }

    /**
     * 订单详情
     *
     * @param id
     * @return
     */
    @Override
    public UserOrderResponse details(Long id) {
        return baseMapper.details(SecurityUtils.getUserId(), id);
    }

    /**
     * 支付回调
     *
     * @param transaction
     */
    @Override
    @Transactional
    public TradeStateEnum callback(Transaction transaction) {
        log.info("支付回调 =======================》 {}", transaction.toString());
        String orderNo = transaction.getOutTradeNo();
        UserOrder userOrder = findUserOrderByNo(orderNo);
        // 支付取消
        if (!PayStatusFlag.WITH.getCode().equals(userOrder.getPayStatus())) {
            return transaction.getTradeState();
        }
        // 支付成功
        if (transaction.getTradeState().equals(Transaction.TradeStateEnum.SUCCESS)) {
            Date payTime = DateUtils.convertToDateTime(transaction.getSuccessTime());
            userOrder.setPayTime(payTime);
            userOrder.setTransactionId(transaction.getTransactionId());
            userOrder.setPayStatus(PayStatusFlag.OK.getCode());
            baseMapper.updateUserOrder(userOrder);

            List<UserReceivingRecords> userReceivingRecordsList = userReceivingRecordsService.findUserReceivingRecordsByOrderNo(orderNo);

            for (UserReceivingRecords userReceivingRecords : userReceivingRecordsList) {
                if (BadgeTypeFlag.DIGITAL_AVATAR.getCode().equals(userReceivingRecords.getBadgeType())) {
                    // 判断是否有升级数字头像
                    UserDigitalAvatar userDigitalAvatar = userDigitalAvatarService.getUserDigitalAvatar(userReceivingRecords.getPortfolioId(), userReceivingRecords.getRelationId());
                    if (userDigitalAvatar != null) {
                        userDigitalAvatar.setOrderId(userOrder.getId());
                        userDigitalAvatar.setUserId(userReceivingRecords.getUserId());
                        userDigitalAvatar.setUserReceivingRecordsId(userReceivingRecords.getId());
                        userDigitalAvatar.setStatus(StatusFlag.OK.getCode());
                        userDigitalAvatarService.updateUserDigitalAvatar(userDigitalAvatar);
                        userReceivingRecords.setUpgradeImage(userDigitalAvatar.getImage());
                    }
                }
                userReceivingRecords.setUpgradeTime(payTime);
                userReceivingRecords.setUpgradeStatus(StatusFlag.OK.getCode());
                userReceivingRecordsService.updateUserReceivingRecord(userReceivingRecords);
            }
            return TradeStateEnum.SUCCESS;
        }
        if (transaction.getTradeState().equals(TradeStateEnum.NOTPAY)) {
            // 支付取消
            userOrder.setPayStatus(PayStatusFlag.CANCEL.getCode());
            baseMapper.updateUserOrder(userOrder);

            List<UserReceivingRecords> userReceivingRecordsList = userReceivingRecordsService.findUserReceivingRecordsByOrderNo(orderNo);
            userReceivingRecordsList.forEach(item -> {
                item.setOrderId(0L);
                item.setOrderNo(null);
            });

            for (UserReceivingRecords userReceivingRecords : userReceivingRecordsList) {
                userReceivingRecordsService.updateUserReceivingRecord(userReceivingRecords);
            }
            return TradeStateEnum.NOTPAY;
        }
        throw new ServiceException("支付异常");
    }

    /**
     * 查询用户订单未支付条数
     *
     * @return
     */
    @Override
    public Long findUserOrderCount() {
        return count(new LambdaQueryWrapper<UserOrder>()
                .eq(UserOrder::getUserId, SecurityUtils.getUserId())
                .eq(UserOrder::getPayStatus, PayStatusFlag.WITH.getCode()));
    }

    /**
     * 取消支付
     *
     * @param id
     * @return
     */
    @Override
    @Transactional
    public Long cancelOrder(Long id) {
        UserOrder userOrder = getById(id);
        PaymentContext context = new PaymentContext();
        PaymentStrategy weChatPayment = new WechatPayment();
        context.setPaymentStrategy(weChatPayment);
        PayInfo payInfo = context.findPayInfo(userOrder.getOrderNo(), OrderQueryType.ORDER_NO.getCode());
        Transaction transaction = payInfo.getTransaction();
        if (transaction.getTradeState().equals(TradeStateEnum.NOTPAY)) {
            userOrder.setPayStatus(PayStatusFlag.CANCEL.getCode());
            updateById(userOrder);
        }
        if (transaction.getTradeState().equals(TradeStateEnum.SUCCESS)) {
            throw new ServiceException("已支付，无法取消");
        }
        return id;
    }

    @Override
    @Transactional
    public void updateUserReceivingRecords(Long userReceivingRecordsId) {
        UserReceivingRecords receivingRecords = userReceivingRecordsService.getById(userReceivingRecordsId);
        UserOrder userOrder = getById(receivingRecords.getOrderId());
        List<UserReceivingRecords> userReceivingRecordsList = userReceivingRecordsService.findUserReceivingRecordsByOrderNo(receivingRecords.getOrderNo());
        Date nowTime = new Date();
        userOrder.setPayTime(nowTime);
        userOrder.setPayStatus(PayStatusFlag.OK.getCode());
        updateById(userOrder);

        if (CollectionUtils.isNotEmpty(userReceivingRecordsList)) {
            for (UserReceivingRecords userReceivingRecords : userReceivingRecordsList) {
                if (BadgeTypeFlag.DIGITAL_AVATAR.getCode().equals(userReceivingRecords.getBadgeType())) {
                    UserDigitalAvatar userDigitalAvatar = userDigitalAvatarService.getUserDigitalAvatar(receivingRecords.getPortfolioId(), userReceivingRecords.getRelationId());
                    if (userDigitalAvatar != null) {
                        userDigitalAvatar.setOrderId(userOrder.getId());
                        userDigitalAvatar.setUserId(userReceivingRecords.getUserId());
                        userDigitalAvatar.setUserReceivingRecordsId(userReceivingRecords.getId());
                        userDigitalAvatar.setStatus(StatusFlag.OK.getCode());
                        userDigitalAvatarService.updateUserDigitalAvatar(userDigitalAvatar);
                        userReceivingRecords.setUpgradeImage(userDigitalAvatar.getImage());
                    }
                }
                userReceivingRecords.setUpgradeStatus(StatusFlag.OK.getCode());
                userReceivingRecords.setUpdateTime(nowTime);
            }
            userReceivingRecordsService.updateBatchById(userReceivingRecordsList);
        }

    }

    /**
     * 查询用户未支付订单（30分钟）
     *
     * @return
     */
    @Override
    public List<UserOrder> findUserOrderNotPay() {
        return baseMapper.findUserOrderNotPay();
    }

    /**
     * 修改用户支付
     *
     * @param userOrder
     */
    @Override
    @Transactional
    public void updateUserOrder(UserOrder userOrder) {
        baseMapper.updateUserOrder(userOrder);
    }
}
