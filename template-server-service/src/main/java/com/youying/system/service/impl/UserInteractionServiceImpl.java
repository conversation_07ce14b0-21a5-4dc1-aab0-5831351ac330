package com.youying.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.UserInteraction;
import com.youying.common.utils.SecurityUtils;
import com.youying.system.domain.userinteraction.UserInteractionRequest;
import com.youying.system.domain.userinteraction.UserInteractionResponse;
import com.youying.system.mapper.UserInteractionMapper;
import com.youying.system.service.UserInteractionService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 用户互动通知表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-18
 */
@Service
public class UserInteractionServiceImpl extends ServiceImpl<UserInteractionMapper, UserInteraction> implements UserInteractionService {

    /**
     * 查询用户互动通知表列表(分页)-评论
     *
     * @param request
     * @return
     */
    @Override
    public List<UserInteractionResponse> listByPageByComment(UserInteractionRequest request) {
        return baseMapper.listByPageByComment(request);
    }

    /**
     * 查询用户互动通知表列表(分页)-点赞
     *
     * @param request
     * @return
     */
    @Override
    public List<UserInteractionResponse> listByPageByKudos(UserInteractionRequest request) {
        return baseMapper.listByPageByKudos(request);
    }

    /**
     * 查询用户互动通知表列表(分页)-提问
     *
     * @param request
     * @return
     */
    @Override
    public List<UserInteractionResponse> listByPageByIssue(UserInteractionRequest request) {
        return baseMapper.listByPageByIssue(request);
    }

    /**
     * 添加用户互动消息
     *
     * @param relevanceId      被互动关联ID
     * @param replyRelevanceId 回复互动关联ID
     * @param userId
     * @param repertoireId
     * @param type
     * @return
     */
    @Override
    @Transactional
    public Long add(Long relevanceId, Long replyRelevanceId, Long userId, Long repertoireId, Integer type) {
        UserInteraction userInteraction = new UserInteraction();
        userInteraction.setUserId(SecurityUtils.getUserId());
        userInteraction.setReplyUserId(userId);
        userInteraction.setRelevanceId(relevanceId);
        userInteraction.setReplyRelevanceId(replyRelevanceId);
        userInteraction.setType(type);
        userInteraction.setRepertoireId(repertoireId);
        save(userInteraction);
        return userInteraction.getId();
    }

    /**
     * 删除互动消息
     *
     * @param relevanceId
     * @param userId
     * @param type
     */
    @Override
    @Transactional
    public void delete(Long relevanceId, Long userId, Integer type) {
        remove(new LambdaQueryWrapper<UserInteraction>()
                .eq(UserInteraction::getRelevanceId, relevanceId)
                .eq(UserInteraction::getUserId, userId)
                .eq(UserInteraction::getType, type));
    }

}
