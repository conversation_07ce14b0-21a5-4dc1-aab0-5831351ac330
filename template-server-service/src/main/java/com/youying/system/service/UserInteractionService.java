package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.UserInteraction;
import com.youying.system.domain.userinteraction.UserInteractionRequest;
import com.youying.system.domain.userinteraction.UserInteractionResponse;

import java.util.List;

/**
 * <p>
 * 用户互动通知表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-18
 */
public interface UserInteractionService extends IService<UserInteraction> {

    /**
     * 查询用户互动通知表列表(分页)-评论
     *
     * @param request
     * @return
     */
    List<UserInteractionResponse> listByPageByComment(UserInteractionRequest request);

    /**
     * 查询用户互动通知表列表(分页)-点赞
     *
     * @param request
     * @return
     */
    List<UserInteractionResponse> listByPageByKudos(UserInteractionRequest request);

    /**
     * 查询用户互动通知表列表(分页)-提问
     *
     * @param request
     * @return
     */
    List<UserInteractionResponse> listByPageByIssue(UserInteractionRequest request);

    /**
     * 添加用户互动消息
     *
     * @param relevanceId      被互动关联ID
     * @param replyRelevanceId 回复互动关联ID
     * @param userId
     * @param type
     * @return
     */
    Long add(Long relevanceId, Long replyRelevanceId, Long userId, Long repertoireId, Integer type);

    /**
     * 删除用户互动消息
     *
     * @param relevanceId
     * @param userId
     * @param type
     */
    void delete(Long relevanceId, Long userId, Integer type);
}
