package com.youying.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.DigitalAvatarBlockchain;
import com.youying.system.mapper.DigitalAvatarBlockchainMapper;
import com.youying.system.service.DigitalAvatarBlockchainService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 电子头像区块链表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-19
 */
@Service
public class DigitalAvatarBlockchainServiceImpl extends ServiceImpl<DigitalAvatarBlockchainMapper, DigitalAvatarBlockchain> implements DigitalAvatarBlockchainService {

    /**
     * 随机获取一张数字头像
     *
     * @param digitalAvatarId
     * @param no
     * @return
     */
    @Override
    public DigitalAvatarBlockchain findRandomAvatarBlockchain(Long digitalAvatarId, String no) {
        DigitalAvatarBlockchain digitalAvatarBlockchain = baseMapper.selectOne(new LambdaQueryWrapper<DigitalAvatarBlockchain>()
                .eq(DigitalAvatarBlockchain::getUserReceivingRecordsNo, no));
        if (digitalAvatarBlockchain != null) {
            return digitalAvatarBlockchain;
        }
        DigitalAvatarBlockchain avatarBlockchain = baseMapper.findRandomAvatarBlockchain(digitalAvatarId);
        avatarBlockchain.setUserReceivingRecordsNo(no);
        updateById(avatarBlockchain);
        return avatarBlockchain;
    }
}
