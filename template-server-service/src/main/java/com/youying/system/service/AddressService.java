package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.Address;
import com.youying.system.domain.area.AreaTree;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
public interface AddressService extends IService<Address> {

    /**
     * 查询地区树形
     *
     * @return
     */
    List<AreaTree> findAreaTree();

    /**
     * 查询地区市一级
     *
     * @return
     */
    List<AreaTree> findAreaByCity(String keyword);
}
