package com.youying.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.AutoReply;
import com.youying.common.core.domain.entity.UserMessage;
import com.youying.common.core.domain.entity.UserMessageInfo;
import com.youying.common.enums.Enums.LookFlag;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.common.exception.ServiceException;
import com.youying.common.utils.SecurityUtils;
import com.youying.system.domain.usermessage.UserMessageResponse;
import com.youying.system.mapper.UserMessageMapper;
import com.youying.system.service.AutoReplyService;
import com.youying.system.service.RepertoireService;
import com.youying.system.service.TheaterService;
import com.youying.system.service.UserMessageInfoService;
import com.youying.system.service.UserMessageService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 用户会话表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@Service
public class UserMessageServiceImpl extends ServiceImpl<UserMessageMapper, UserMessage> implements UserMessageService {
    @Autowired
    private UserMessageInfoService userMessageInfoService;
    @Autowired
    private AutoReplyService autoReplyService;
    @Autowired
    private RepertoireService repertoireService;
    @Autowired
    private TheaterService theaterService;

    /**
     * 用户会话列表
     *
     * @return
     */
    @Override
    public List<UserMessageResponse> listByPage() {
        return baseMapper.listByPage(SecurityUtils.getUserId());
    }

    /**
     * 删除会话列表
     *
     * @param ids
     * @return
     */
    @Override
    @Transactional
    public Integer delete(List<Long> ids) {
        userMessageInfoService.deleteByUserMessageId(ids);
        removeBatchByIds(ids);
        return ids.size();
    }

    /**
     * 添加会话列表
     *
     * @param userMessage
     * @return
     */
    @Override
    @Transactional
    public Long add(UserMessage userMessage) {
        // 查询是否存在会话
        Long userMessageId = findUserMessageByUserId(userMessage);
        if (userMessageId == -1L) {
            throw new ServiceException("数据错误");
        } else if (userMessageId > 0) {
            return userMessageId;
        }
        userMessage.setUserId(SecurityUtils.getUserId());
        userMessage.setLookFlag(StatusFlag.OK.getCode());
        save(userMessage);

        return userMessage.getId();
    }

    /**
     * 添加会话并发送欢迎语
     *
     * @param threadId
     * @param repertoireId
     * @param userId
     * @return
     */
    @Override
    @Transactional
    public Long addUserMessage(Long threadId, Long repertoireId, Long userId) {
        Long merchantId = findMerchant(threadId, repertoireId);
        UserMessage userMessageInfo = findUserMessageExists(threadId, repertoireId, userId);
        Long userMessageId = 0L;
        // 判断是否关注或私聊过
        if (userMessageInfo == null) {
            UserMessage userMessage = new UserMessage();
            userMessage.setMerchantId(merchantId);
            userMessage.setTheaterId(threadId);
            userMessage.setRepertoireId(repertoireId);
            userMessage.setUserId(userId);
            userMessage.setLookFlag(LookFlag.DEFAULT.getCode());
            save(userMessage);
            userMessageId = userMessage.getId();
        } else {
            userMessageId = userMessageInfo.getId();
            userMessageInfo.setUpdateTime(new Date());
            updateById(userMessageInfo);
        }

        AutoReply autoReply = autoReplyService.findAutoReply(threadId, repertoireId);
        // 添加回复信息
        UserMessageInfo messageInfo = new UserMessageInfo();
        messageInfo.setUserMerchantId(merchantId);
        messageInfo.setMerchantId(merchantId);
        messageInfo.setTheaterId(threadId);
        messageInfo.setRepertoireId(repertoireId);
        messageInfo.setUserMessageId(userMessageId);
        messageInfo.setUserMerchantId(threadId == null ? repertoireId : threadId);
        messageInfo.setUserId(userId);
        messageInfo.setBody(autoReply != null ? autoReply.getBody() : "感谢关注，希望能留下您宝贵的评论~");
        messageInfo.setLookFlag(LookFlag.DEFAULT.getCode());
        userMessageInfoService.save(messageInfo);

        return merchantId;
    }

    /**
     * 获取剧目或剧场用户消息
     *
     * @param threadId
     * @param repertoireId
     * @return
     */
    private UserMessage findUserMessageExists(Long threadId, Long repertoireId, Long userId) {
        if (threadId != null) {
            return getOne(new LambdaQueryWrapper<UserMessage>()
                    .eq(UserMessage::getTheaterId, threadId)
                    .eq(UserMessage::getUserId, userId));
        } else {
            return getOne(new LambdaQueryWrapper<UserMessage>()
                    .eq(UserMessage::getRepertoireId, repertoireId)
                    .eq(UserMessage::getUserId, userId));
        }
    }

    /**
     * 获取剧目或剧场商家ID
     *
     * @param threadId
     * @param repertoireId
     * @return
     */
    private Long findMerchant(Long threadId, Long repertoireId) {
        if (threadId != null) {
            return theaterService.getById(threadId).getMerchantId();
        } else {
            return repertoireService.getById(repertoireId).getMerchantId();
        }
    }

    /**
     * 查询用户会话表详情
     *
     * @param id
     * @return
     */
    @Override
    public UserMessageResponse details(Long id) {
        // 清除未读
        List<UserMessageInfo> userMessageInfos = userMessageInfoService.list(new LambdaQueryWrapper<UserMessageInfo>()
                .select(UserMessageInfo::getId, UserMessageInfo::getLookFlag)
                .eq(UserMessageInfo::getUserMessageId, id)
                .eq(UserMessageInfo::getUserId, SecurityUtils.getUserId())
                .gt(UserMessageInfo::getUserMerchantId, 0)
                .eq(UserMessageInfo::getLookFlag, LookFlag.DEFAULT.getCode()));
        if (CollectionUtils.isNotEmpty(userMessageInfos)) {
            userMessageInfos.stream().forEach(item -> item.setLookFlag(LookFlag.PASS.getCode()));
            userMessageInfoService.updateBatchById(userMessageInfos);
        }
        return baseMapper.details(id, SecurityUtils.getUserId());
    }

    /**
     * 查询
     *
     * @param userMessage
     * @return
     */
    private Long findUserMessageByUserId(UserMessage userMessage) {
        // 查询剧场是否存在消息
        if (userMessage.getTheaterId() != null) {
            UserMessage message = getOne(new LambdaQueryWrapper<UserMessage>()
                    .eq(UserMessage::getUserId, SecurityUtils.getUserId())
                    .eq(UserMessage::getTheaterId, userMessage.getTheaterId()));
            return message == null ? 0 : message.getId();
        } else if (userMessage.getRepertoireId() != null) {
            UserMessage message = getOne(new LambdaQueryWrapper<UserMessage>()
                    .eq(UserMessage::getUserId, SecurityUtils.getUserId())
                    .eq(UserMessage::getRepertoireId, userMessage.getTheaterId()));
            return message == null ? 0 : message.getId();
        }
        return -1L;
    }

}
