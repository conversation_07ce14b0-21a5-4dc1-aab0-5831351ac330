package com.youying.system.service.impl;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.PortfolioInfo;
import com.youying.system.mapper.PortfolioInfoMapper;
import com.youying.system.service.PortfolioInfoService;

/**
 * <p>
 * 藏品组合表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-24
 */
@Service
public class PortfolioInfoServiceImpl extends ServiceImpl<PortfolioInfoMapper, PortfolioInfo>
        implements PortfolioInfoService {

    /**
     * 根据剧场和剧目查询组合信息
     *
     */
    @Override
    public PortfolioInfo findPortfolioInfo(Long theaterId, Long repertoireId) {
        return getOne(new LambdaQueryWrapper<PortfolioInfo>()
                .eq(PortfolioInfo::getTheaterId, theaterId)
                .eq(PortfolioInfo::getRepertoireId, repertoireId));
    }

    /**
     * 根据组合ID查询组合信息
     *
     */
    @Override
    public PortfolioInfo getByPortfolioId(Long id) {
        return getOne(new LambdaQueryWrapper<PortfolioInfo>()
                .eq(PortfolioInfo::getPortfolioId, id));
    }
}
