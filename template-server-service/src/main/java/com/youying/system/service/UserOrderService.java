package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wechat.pay.java.service.payments.model.Transaction;
import com.wechat.pay.java.service.payments.model.Transaction.TradeStateEnum;
import com.youying.common.core.domain.entity.UserOrder;
import com.youying.common.core.page.PageDomain;
import com.youying.common.pay.WechatPay;
import com.youying.system.domain.userorder.UserOrderResponse;

import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SignatureException;
import java.util.List;

/**
 * <p>
 * 用户订单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
public interface UserOrderService extends IService<UserOrder> {

    /**
     * 提起订单
     *
     * @param userReceivingRecordsId
     * @return
     * @throws NoSuchAlgorithmException
     * @throws SignatureException
     * @throws InvalidKeyException
     */
    WechatPay pay(Long userReceivingRecordsId) throws NoSuchAlgorithmException, SignatureException, InvalidKeyException;

    /**
     * 根据订单编号查询订单
     *
     * @param orderNo
     * @return
     */
    UserOrder findUserOrderByNo(String orderNo);

    /**
     * 查询用户订单表
     *
     * @param pageDomain
     * @return
     */
    List<UserOrderResponse> listByPage(PageDomain pageDomain);

    /**
     * 订单详情
     *
     * @param id
     * @return
     */
    UserOrderResponse details(Long id);

    /**
     * 支付回调
     *
     * @param transaction
     * @return
     */
    TradeStateEnum callback(Transaction transaction);

    /**
     * 查询用户订单未支付条数
     *
     * @return
     */
    Long findUserOrderCount();

    /**
     * 取消支付
     *
     * @param id
     * @return
     */
    Long cancelOrder(Long id);

    /**
     * 修改用户领取藏品为升级
     *
     * @param userReceivingRecordsId
     */
    void updateUserReceivingRecords(Long userReceivingRecordsId);

    /**
     * 查询用户未支付订单（30分钟）
     *
     * @return
     */
    List<UserOrder> findUserOrderNotPay();

    /**
     * 修改用户支付
     *
     * @param userOrder
     */
    void updateUserOrder(UserOrder userOrder);
}
