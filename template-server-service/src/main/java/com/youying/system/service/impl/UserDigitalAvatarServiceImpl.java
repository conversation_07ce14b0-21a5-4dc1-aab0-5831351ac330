package com.youying.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.UserDigitalAvatar;
import com.youying.system.mapper.UserDigitalAvatarMapper;
import com.youying.system.service.UserDigitalAvatarService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 用户电子头像表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-29
 */
@Service
public class UserDigitalAvatarServiceImpl extends ServiceImpl<UserDigitalAvatarMapper, UserDigitalAvatar> implements UserDigitalAvatarService {

    /**
     * 查询一条未占用升级数字头像
     *
     * @param portfolioId
     * @return
     */
    @Override
    public UserDigitalAvatar getUserDigitalAvatar(Long portfolioId, Long relationId) {
        return baseMapper.getUserDigitalAvatar(portfolioId, relationId);
    }

    /**
     * 修改用户数字头像
     *
     * @param userDigitalAvatar
     */
    @Override
    @Transactional
    public void updateUserDigitalAvatar(UserDigitalAvatar userDigitalAvatar) {
        baseMapper.updateUserDigitalAvatar(userDigitalAvatar);
    }
}
