package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.UserTreasure;
import com.youying.system.domain.usertreasure.UserTreasureRequest;
import com.youying.system.domain.usertreasure.UserTreasureResponse;

import java.util.List;

/**
 * <p>
 * 用户剧目剧场收藏表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface UserTreasureService extends IService<UserTreasure> {

    /**
     * 剧目剧关注添加或取消
     *
     * @param userTreasure
     */
    Long addOrDelete(UserTreasure userTreasure);

    /**
     * 添加剧目剧场关注 如已关注则直接返回退出
     *
     * @param theaterId
     * @param repertoireId
     * @param userId
     * @return
     */
    Long add(Long theaterId, Long repertoireId, Long userId);

    /**
     * 用户关注列表
     *
     * @param request
     * @return
     */
    List<UserTreasureResponse> listByPage(UserTreasureRequest request);
}
