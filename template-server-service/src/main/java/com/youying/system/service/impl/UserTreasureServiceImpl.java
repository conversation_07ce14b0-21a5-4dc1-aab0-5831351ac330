package com.youying.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.Repertoire;
import com.youying.common.core.domain.entity.Theater;
import com.youying.common.core.domain.entity.UserTreasure;
import com.youying.common.utils.SecurityUtils;
import com.youying.system.domain.usertreasure.UserTreasureRequest;
import com.youying.system.domain.usertreasure.UserTreasureResponse;
import com.youying.system.mapper.UserTreasureMapper;
import com.youying.system.service.RepertoireService;
import com.youying.system.service.TheaterService;
import com.youying.system.service.UserMessageService;
import com.youying.system.service.UserTreasureService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 用户剧目剧场收藏表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@Service
public class UserTreasureServiceImpl extends ServiceImpl<UserTreasureMapper, UserTreasure> implements UserTreasureService {
    @Autowired
    private TheaterService theaterService;
    @Autowired
    private RepertoireService repertoireService;
    @Autowired
    private UserMessageService userMessageService;

    /**
     * 剧目剧关注添加或取消
     *
     * @param userTreasure
     * @return
     */
    @Override
    public Long addOrDelete(UserTreasure userTreasure) {
        UserTreasure userTreasureDetails = null;
        Theater theater = null;
        Repertoire repertoire = null;

        if (userTreasure.getTheaterId() != null) {
            userTreasureDetails = baseMapper.selectOne(new LambdaQueryWrapper<UserTreasure>()
                    .eq(UserTreasure::getUserId, SecurityUtils.getUserId())
                    .eq(UserTreasure::getTheaterId, userTreasure.getTheaterId()));
            theater = theaterService.getById(userTreasure.getTheaterId());
        } else {
            userTreasureDetails = baseMapper.selectOne(new LambdaQueryWrapper<UserTreasure>()
                    .eq(UserTreasure::getUserId, SecurityUtils.getUserId())
                    .eq(UserTreasure::getRepertoireId, userTreasure.getRepertoireId()));
            repertoire = repertoireService.getById(userTreasure.getRepertoireId());
        }

        boolean isTheater = (theater != null);

        if (userTreasureDetails == null) {
            incrementFocusNumber(isTheater, theater, repertoire);
            save(userTreasure);
            // 添加欢迎语
            userMessageService.addUserMessage(userTreasure.getTheaterId(), userTreasure.getRepertoireId(), userTreasure.getUserId());
            return userTreasure.getId();
        } else {
            decrementFocusNumber(isTheater, theater, repertoire);
            removeById(userTreasureDetails.getId());
            return userTreasureDetails.getId();
        }
    }

    /**
     * 添加剧目剧场关注 如已关注则直接返回退出
     *
     * @param theaterId
     * @param repertoireId
     * @param userId
     * @return
     */
    @Override
    @Transactional
    public Long add(Long theaterId, Long repertoireId, Long userId) {
        UserTreasure userTreasure = new UserTreasure();
        UserTreasure userTreasureInfo = new UserTreasure();
        Theater theater = null;
        Repertoire repertoire = null;
        if (theaterId != null && theaterId > 0) {
            userTreasureInfo = baseMapper.selectOne(new LambdaQueryWrapper<UserTreasure>()
                    .eq(UserTreasure::getUserId, SecurityUtils.getUserId())
                    .eq(UserTreasure::getTheaterId, theaterId));
            theater = theaterService.getById(theaterId);
            userTreasure.setMerchantId(theater.getMerchantId());
        } else {
            userTreasureInfo = baseMapper.selectOne(new LambdaQueryWrapper<UserTreasure>()
                    .eq(UserTreasure::getUserId, SecurityUtils.getUserId())
                    .eq(UserTreasure::getRepertoireId, repertoireId));
            repertoire = repertoireService.getById(repertoireId);
            userTreasure.setMerchantId(repertoire.getMerchantId());
        }
        // 返回关注ID
        if (userTreasureInfo != null) {
            return userTreasureInfo.getId();
        }
        // 添加关注
        userTreasure.setTheaterId(theaterId);
        userTreasure.setUserId(userId);
        userTreasure.setRepertoireId(repertoireId);
        save(userTreasure);
        userMessageService.addUserMessage(userTreasure.getTheaterId(), userTreasure.getRepertoireId(), userId);

        incrementFocusNumber(theaterId != null, theater, repertoire);

        return userTreasure.getId();
    }

    /**
     * 用户关注列表
     *
     * @param request
     * @return
     */
    @Override
    public List<UserTreasureResponse> listByPage(UserTreasureRequest request) {
        return baseMapper.listByPage(request);
    }

    // 辅助方法：增加关注数
    private void incrementFocusNumber(boolean isTheater, Theater theater, Repertoire repertoire) {
        if (isTheater) {
            theater.setFocusNumber(theater.getFocusNumber() + 1);
            theaterService.updateById(theater);
        } else {
            repertoire.setFocusNumber(repertoire.getFocusNumber() + 1);
            repertoireService.updateById(repertoire);
        }
    }

    // 辅助方法：减少关注数
    private void decrementFocusNumber(boolean isTheater, Theater theater, Repertoire repertoire) {
        if (isTheater) {
            theater.setFocusNumber(theater.getFocusNumber() - 1);
            theaterService.updateById(theater);
        } else {
            repertoire.setFocusNumber(repertoire.getFocusNumber() - 1);
            repertoireService.updateById(repertoire);
        }
    }
}
