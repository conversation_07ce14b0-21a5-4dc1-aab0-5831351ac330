package com.youying.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.Dynamic;
import com.youying.common.core.domain.entity.DynamicKudos;
import com.youying.common.enums.Enums.UserInteractionFlag;
import com.youying.common.utils.SecurityUtils;
import com.youying.system.mapper.DynamicKudosMapper;
import com.youying.system.service.DynamicKudosService;
import com.youying.system.service.DynamicService;
import com.youying.system.service.UserInteractionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 剧场动态点赞表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-18
 */
@Service
public class DynamicKudosServiceImpl extends ServiceImpl<DynamicKudosMapper, DynamicKudos> implements DynamicKudosService {
    @Autowired
    private UserInteractionService userInteractionService;
    @Autowired
    private DynamicService dynamicService;

    /**
     * 添加/修改剧场动态点赞
     *
     * @param dynamicKudos
     * @return
     */
    @Override
    public Long addOrUpdate(DynamicKudos dynamicKudos) {
        if (dynamicKudos.getType() == null) {
            baseMapper.deleteUserDynamicKudos(dynamicKudos.getDynamicId(), SecurityUtils.getUserId());
            userInteractionService.delete(dynamicKudos.getDynamicId(), SecurityUtils.getUserId(), UserInteractionFlag.DYNAMIC_KUDOS.getCode());
            return dynamicKudos.getDynamicId();
        }
        Dynamic dynamic = dynamicService.getById(dynamicKudos.getDynamicId());
        save(dynamicKudos);
        userInteractionService.add(dynamicKudos.getDynamicId(), null, null, dynamic.getRepertoireId(), UserInteractionFlag.DYNAMIC_KUDOS.getCode());
        return dynamicKudos.getDynamicId();
    }
}
