package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.SouvenirBadge;
import com.youying.system.domain.souvenirbadge.SouvenirBadgeRequest;
import com.youying.system.domain.souvenirbadge.SouvenirBadgeResponse;

import java.util.List;

/**
 * <p>
 * 剧场纪念徽章表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface SouvenirBadgeService extends IService<SouvenirBadge> {

    /**
     * 查询剧场纪念徽章表列表(分页)
     *
     * @param request
     * @return
     */
    List<SouvenirBadgeResponse> listByPage(SouvenirBadgeRequest request);

    /**
     * 查询剧场纪念徽章表详情
     *
     * @param id
     * @param relationId
     * @return
     */
    SouvenirBadgeResponse details(Long id, Long relationId);

    /**
     * 查询剧场可领取纪念徽章
     *
     * @param theaterId
     * @return
     */
    List<SouvenirBadge> findUserConsumption(Long theaterId);
}
