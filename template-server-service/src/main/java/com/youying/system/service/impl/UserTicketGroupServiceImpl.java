package com.youying.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.UserTicketGroup;
import com.youying.common.enums.Enums.DataFlag;
import com.youying.common.utils.SecurityUtils;
import com.youying.system.domain.ticketgroup.TicketGroupResponse;
import com.youying.system.mapper.UserTicketGroupMapper;
import com.youying.system.service.UserTicketGroupService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 用户电子票分组表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-08
 */
@Service
public class UserTicketGroupServiceImpl extends ServiceImpl<UserTicketGroupMapper, UserTicketGroup> implements UserTicketGroupService {

    /**
     * 删除分组内电子票
     *
     * @param ticketGroupId
     */
    @Override
    @Transactional
    public void deleteByGroupId(Long ticketGroupId) {
        baseMapper.delete(new LambdaQueryWrapper<UserTicketGroup>()
                .eq(UserTicketGroup::getTicketGroupId, ticketGroupId)
                .eq(UserTicketGroup::getType, DataFlag.USER.getCode())
                .eq(UserTicketGroup::getUserId, SecurityUtils.getUserId()));
    }

    /**
     * 查询用户分类数据
     *
     * @param ticketGroupId
     * @param userId
     * @return
     */
    @Override
    public List<TicketGroupResponse> findTicketGroupByUserId(Long ticketGroupId, Long userId) {
        return baseMapper.findTicketGroupByUserId(ticketGroupId, userId);
    }

    /**
     * 添加分组
     *
     * @param repertoireId
     * @param repertoireFlag
     * @param userReceivingRecordsId
     */
    @Override
    @Transactional
    public void add(Integer repertoireFlag, Long userReceivingRecordsId, Long repertoireId) {
        UserTicketGroup userTicketGroup = new UserTicketGroup();
        if (repertoireFlag == 0) {
            userTicketGroup.setTicketGroupId(3L);
        } else {
            userTicketGroup.setTicketGroupId(2L);
        }
        userTicketGroup.setUserReceivingRecordsId(userReceivingRecordsId);
        userTicketGroup.setUserId(SecurityUtils.getUserId());
        userTicketGroup.setRepertoireId(repertoireId);
        userTicketGroup.setType(DataFlag.ADMIN.getCode());
        baseMapper.insert(userTicketGroup);
    }
}
