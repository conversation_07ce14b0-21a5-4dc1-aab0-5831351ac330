package com.youying.system.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.Comment;
import com.youying.common.core.domain.entity.CommentInfo;
import com.youying.common.enums.Enums.UserInteractionFlag;
import com.youying.common.utils.SecurityUtils;
import com.youying.system.mapper.CommentInfoMapper;
import com.youying.system.service.CommentInfoService;
import com.youying.system.service.CommentService;
import com.youying.system.service.UserInteractionService;

/**
 * <p>
 * 评论点赞、踩详情表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@Service
public class CommentInfoServiceImpl extends ServiceImpl<CommentInfoMapper, CommentInfo> implements CommentInfoService {
    @Autowired
    private UserInteractionService userInteractionService;
    @Autowired
    private CommentService commentService;

    /**
     * 添加评论点赞、踩、转发、关注或删除
     * 扩展后的数据结构和意义：
     * {
     * commentId: number, // 评论ID
     * userId: number, // 用户ID
     * type: number // 1:赞, 0:踩, 2:转发, 3:关注
     * }
     * 如果已经有了就代表本次请求是删除，没有表示新增
     *
     * @param commentInfo
     * @return
     */
    @Override
    @Transactional
    public Long addOrUpdate(CommentInfo commentInfo) {
        if (commentInfo.getType() == null) {
            // 删除所有类型的互动记录
            baseMapper.deleteUserComment(commentInfo.getCommentId(), SecurityUtils.getUserId());
            userInteractionService.delete(commentInfo.getCommentId(), SecurityUtils.getUserId(),
                    UserInteractionFlag.COMMENT_KUDOS.getCode());
            userInteractionService.delete(commentInfo.getCommentId(), SecurityUtils.getUserId(),
                    UserInteractionFlag.COMMENT_SHARE.getCode());
            userInteractionService.delete(commentInfo.getCommentId(), SecurityUtils.getUserId(),
                    UserInteractionFlag.COMMENT_FOLLOW.getCode());
            return commentInfo.getCommentId();
        }

        // 设置用户ID
        commentInfo.setUserId(SecurityUtils.getUserId());

        // 查询是否已存在相同类型的记录
        CommentInfo existingInfo = getOne(new LambdaQueryWrapper<CommentInfo>()
                .eq(CommentInfo::getCommentId, commentInfo.getCommentId())
                .eq(CommentInfo::getUserId, SecurityUtils.getUserId())
                .eq(CommentInfo::getType, commentInfo.getType()));

        Comment comment = commentService.getById(commentInfo.getCommentId());
        Long targetUserId = comment.getReplyId() == null || comment.getReplyId() == 0L ? comment.getUserId()
                : comment.getReplyId();

        if (existingInfo != null) {
            // 如果已存在相同类型的记录，则删除（取消操作）
            remove(new LambdaQueryWrapper<CommentInfo>()
                    .eq(CommentInfo::getCommentId, commentInfo.getCommentId())
                    .eq(CommentInfo::getUserId, SecurityUtils.getUserId())
                    .eq(CommentInfo::getType, commentInfo.getType()));

            // 删除对应的用户交互记录
            deleteUserInteraction(commentInfo.getCommentId(), commentInfo.getType());
        } else {
            // 如果不存在，则新增
            save(commentInfo);

            // 添加对应的用户交互记录（不给自己发通知）
            if (!SecurityUtils.getUserId().equals(targetUserId)) {
                addUserInteraction(commentInfo.getCommentId(), targetUserId, comment.getRepertoireId(),
                        commentInfo.getType());
            }
        }

        return commentInfo.getCommentId();
    }

    /**
     * 根据类型添加用户交互记录
     *
     * @param commentId    评论ID
     * @param targetUserId 目标用户ID
     * @param repertoireId 剧目ID
     * @param type         操作类型
     */
    private void addUserInteraction(Long commentId, Long targetUserId, Long repertoireId, Integer type) {
        switch (type) {
            case 1: // 点赞
                userInteractionService.add(commentId, null, targetUserId, repertoireId,
                        UserInteractionFlag.COMMENT_KUDOS.getCode());
                break;
            case 2: // 转发
                userInteractionService.add(commentId, null, targetUserId, repertoireId,
                        UserInteractionFlag.COMMENT_SHARE.getCode());
                break;
            case 3: // 关注
                userInteractionService.add(commentId, null, targetUserId, repertoireId,
                        UserInteractionFlag.COMMENT_FOLLOW.getCode());
                break;
            case 0: // 踩 - 不发送通知
            default:
                // 踩或其他类型不发送用户交互通知
                break;
        }
    }

    /**
     * 根据类型删除用户交互记录
     *
     * @param commentId 评论ID
     * @param type      操作类型
     */
    private void deleteUserInteraction(Long commentId, Integer type) {
        switch (type) {
            case 1: // 点赞
                userInteractionService.delete(commentId, SecurityUtils.getUserId(),
                        UserInteractionFlag.COMMENT_KUDOS.getCode());
                break;
            case 2: // 转发
                userInteractionService.delete(commentId, SecurityUtils.getUserId(),
                        UserInteractionFlag.COMMENT_SHARE.getCode());
                break;
            case 3: // 关注
                userInteractionService.delete(commentId, SecurityUtils.getUserId(),
                        UserInteractionFlag.COMMENT_FOLLOW.getCode());
                break;
            case 0: // 踩
            default:
                // 踩或其他类型不需要删除用户交互记录
                break;
        }
    }
}
