package com.youying.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.RepertoireInfoDetail;
import com.youying.system.mapper.RepertoireInfoDetailMapper;
import com.youying.system.service.RepertoireInfoDetailService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 剧目剧场场次表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@Service
public class RepertoireInfoDetailServiceImpl extends ServiceImpl<RepertoireInfoDetailMapper, RepertoireInfoDetail> implements RepertoireInfoDetailService {

    /**
     * 根据剧目剧场与场次时间查询场次详情信息
     *
     * @param theaterName
     * @param repertoireName
     * @param dateStr
     * @param timeStr
     * @return
     */
    @Override
    public RepertoireInfoDetail findRepertoireInfoDetail(String theaterName, String repertoireName, String dateStr, String timeStr, String address) {
        return baseMapper.findRepertoireInfoDetail(theaterName, repertoireName, dateStr, timeStr, address);
    }

    /**
     * 根据剧目剧场与场次时间查询场次详情信息
     *
     * @param theaterId
     * @param repertoireId
     * @param dateStr
     * @return
     */
    @Override
    public RepertoireInfoDetail findRepertoireInfoDetail(Long theaterId, Long repertoireId, String dateStr) {
        return baseMapper.findRepertoireInfoDetails(theaterId, repertoireId, dateStr);
    }

    /**
     * 根据剧目剧场与场次时间查询场次详情信息
     *
     * @param theaterId
     * @param repertoireId
     * @param timeList
     * @return
     */
    @Override
    public RepertoireInfoDetail findRepertoireInfoDetail(Long theaterId, Long repertoireId, List<String> timeList) {
        return baseMapper.findRepertoireInfo(theaterId, repertoireId, timeList);
    }

    /**
     * 根据时间查询剧目
     *
     * @param time
     * @return
     */
    @Override
    public List<Long> findRepertoireByTime(List<String> time) {
        return baseMapper.findRepertoireByTime(time);
    }
}
