package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.UserTicketGroup;
import com.youying.system.domain.ticketgroup.TicketGroupResponse;

import java.util.List;

/**
 * <p>
 * 用户电子票分组表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-08
 */
public interface UserTicketGroupService extends IService<UserTicketGroup> {

    /**
     * 删除分组内电子票
     *
     * @param ticketGroupId
     */
    void deleteByGroupId(Long ticketGroupId);

    /**
     * 查询用户分类数据
     *
     * @param ticketGroupId
     * @param userId
     * @return
     */
    List<TicketGroupResponse> findTicketGroupByUserId(Long ticketGroupId, Long userId);

    /**
     * 添加分组
     *
     * @param repertoireFlag
     * @param userReceivingRecordsId
     */
    void add(Integer repertoireFlag, Long userReceivingRecordsId, Long repertoireId);
}
