package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.RepertoireInfoDetail;

import java.util.List;

/**
 * <p>
 * 剧目剧场场次表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface RepertoireInfoDetailService extends IService<RepertoireInfoDetail> {

    /**
     * 根据剧目剧场与场次时间查询场次详情信息
     *
     * @param theaterName
     * @param repertoireName
     * @param dateStr
     * @param timeStr
     * @param address
     */
    RepertoireInfoDetail findRepertoireInfoDetail(String theaterName,
                                                  String repertoireName,
                                                  String dateStr,
                                                  String timeStr,
                                                  String address);

    /**
     * 根据剧目剧场与场次时间查询场次详情信息
     *
     * @param theaterId
     * @param repertoireId
     * @param dateStr
     * @return
     */
    RepertoireInfoDetail findRepertoireInfoDetail(Long theaterId, Long repertoireId, String dateStr);

    RepertoireInfoDetail findRepertoireInfoDetail(Long theaterId, Long repertoireId, List<String> timeList);

    /**
     * 根据时间查询剧目
     *
     * @param time
     * @return
     */
    List<Long> findRepertoireByTime(List<String> time);
}
