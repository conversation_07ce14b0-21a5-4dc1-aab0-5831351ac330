package com.youying.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.Address;
import com.youying.system.domain.area.AreaTree;
import com.youying.system.mapper.AddressMapper;
import com.youying.system.service.AddressService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
@Service
public class AddressServiceImpl extends ServiceImpl<AddressMapper, Address> implements AddressService {

    /**
     * 树形菜单
     *
     * @return
     */
    @Override
    public List<AreaTree> findAreaTree() {
        List<AreaTree> areas = baseMapper.findAreaAll();
        if (CollectionUtils.isNotEmpty(areas)) {
            Map<Long, AreaTree> treeMap = areas.stream().collect(Collectors.toMap(item -> item.getId(), item -> item));
            List<AreaTree> areaList = areas.stream().filter(item -> !treeMap.containsKey(item.getParentId())).collect(Collectors.toList());
            for (AreaTree area : areas) {
                if (treeMap.containsKey(area.getParentId())) {
                    AreaTree parentTree = treeMap.get(area.getParentId());
                    List<AreaTree> children = parentTree.getChildren();
                    children.add(area);
                }
            }
            return areaList;
        }
        return areas;
    }

    /**
     * 查询地区市一级
     *
     * @return
     */
    @Override
    public List<AreaTree> findAreaByCity(String keyword) {
        return baseMapper.findAreaByCity(keyword);
    }
}
