package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.UserMessageInfo;

import java.util.List;

/**
 * <p>
 * 用户消息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface UserMessageInfoService extends IService<UserMessageInfo> {

    /**
     * 删除用户消息
     *
     * @param ids
     */
    void deleteByUserMessageId(List<Long> ids);
}
