package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.ImageScanRecord;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
public interface ImageScanRecordService extends IService<ImageScanRecord> {

    /**
     * 添加
     *
     * @param imageScanRecord
     */
    void add(ImageScanRecord imageScanRecord);

    /**
     * 查询扫描成功次数
     *
     * @param portfolioId
     * @return
     */
    Long findPortfolioCount(Long portfolioId);
}
