package com.youying.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.TheaterRepertoireNo;
import com.youying.system.mapper.TheaterRepertoireNoMapper;
import com.youying.system.service.TheaterRepertoireNoService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 剧目剧场关联编号表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Service
public class TheaterRepertoireNoServiceImpl extends ServiceImpl<TheaterRepertoireNoMapper, TheaterRepertoireNo> implements TheaterRepertoireNoService {

    /**
     * 获取剧目剧场关联编号
     *
     * @param theaterId
     * @param repertoireId
     * @return
     */
    @Override
    public String findTheaterRepertoireNo(Long theaterId, Long repertoireId) {
        TheaterRepertoireNo theaterRepertoireNo = getOne(new LambdaQueryWrapper<TheaterRepertoireNo>()
                .eq(TheaterRepertoireNo::getTheaterId, theaterId)
                .eq(TheaterRepertoireNo::getRepertoireId, repertoireId));
        return theaterRepertoireNo.getNo();
    }
}
