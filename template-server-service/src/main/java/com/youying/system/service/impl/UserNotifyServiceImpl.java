package com.youying.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.UserNotify;
import com.youying.common.enums.Enums.LookFlag;
import com.youying.common.enums.Enums.PortFlag;
import com.youying.common.utils.SecurityUtils;
import com.youying.system.domain.usernotify.UserNotifyResponse;
import com.youying.system.mapper.UserNotifyMapper;
import com.youying.system.service.UserNotifyService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 用户推送信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@Service
public class UserNotifyServiceImpl extends ServiceImpl<UserNotifyMapper, UserNotify> implements UserNotifyService {

    /**
     * 查询用户推送信息表列表(分页)
     *
     * @return
     */
    @Override
    public List<UserNotifyResponse> listByPage() {
        List<UserNotify> notifies = list(new LambdaQueryWrapper<UserNotify>()
                .select(UserNotify::getId, UserNotify::getLookFlag)
                .eq(UserNotify::getUserId, SecurityUtils.getUserId())
                .eq(UserNotify::getLookFlag, LookFlag.DEFAULT.getCode()));
        if (CollectionUtils.isNotEmpty(notifies)) {
            notifies.forEach(item -> item.setLookFlag(LookFlag.PASS.getCode()));
            updateBatchById(notifies);
        }
        return baseMapper.listByPage(SecurityUtils.getUserId(), PortFlag.USER.getCode());
    }

    /**
     * 查询用户推送信息表详情
     *
     * @param id
     * @return
     */
    @Override
    public UserNotifyResponse details(Long id) {
        UserNotify userNotify = getById(id);
        if (userNotify != null && !LookFlag.PASS.getCode().equals(userNotify.getLookFlag())) {
            userNotify.setLookFlag(LookFlag.PASS.getCode());
            updateById(userNotify);
        }
        return baseMapper.details(id, SecurityUtils.getUserId(), PortFlag.USER.getCode());
    }
}
