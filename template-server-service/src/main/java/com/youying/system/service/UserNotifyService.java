package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.UserNotify;
import com.youying.system.domain.usernotify.UserNotifyResponse;

import java.util.List;

/**
 * <p>
 * 用户推送信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface UserNotifyService extends IService<UserNotify> {
    /**
     * 查询用户推送信息表列表(分页)
     *
     * @return
     */
    List<UserNotifyResponse> listByPage();

    /**
     * 查询用户推送信息表详情
     *
     * @param id
     * @return
     */
    UserNotifyResponse details(Long id);
}
