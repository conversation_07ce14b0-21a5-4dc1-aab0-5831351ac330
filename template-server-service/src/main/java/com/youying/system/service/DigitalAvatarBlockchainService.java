package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.DigitalAvatarBlockchain;

/**
 * <p>
 * 电子头像区块链表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-19
 */
public interface DigitalAvatarBlockchainService extends IService<DigitalAvatarBlockchain> {

    /**
     * 随机获取一张数字头像
     *
     * @param digitalAvatarId
     * @param no
     * @return
     */
    DigitalAvatarBlockchain findRandomAvatarBlockchain(Long digitalAvatarId, String no);
}
