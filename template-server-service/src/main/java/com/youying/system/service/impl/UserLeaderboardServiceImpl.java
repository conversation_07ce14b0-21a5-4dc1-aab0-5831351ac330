package com.youying.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.UserLeaderboard;
import com.youying.common.utils.SecurityUtils;
import com.youying.system.mapper.UserLeaderboardMapper;
import com.youying.system.service.UserLeaderboardService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 用户排行榜表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-09
 */
@Service
public class UserLeaderboardServiceImpl extends ServiceImpl<UserLeaderboardMapper, UserLeaderboard> implements UserLeaderboardService {

    /**
     * 根据榜单删除用户榜单内容
     *
     * @param leaderboardId
     */
    @Override
    @Transactional
    public void deleteByLeaderboard(Long leaderboardId) {
        baseMapper.delete(new LambdaQueryWrapper<UserLeaderboard>()
                .eq(UserLeaderboard::getLeaderboardId, leaderboardId)
                .eq(UserLeaderboard::getUserId, SecurityUtils.getUserId()));
    }
}
