package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.RankMedal;
import com.youying.common.core.domain.entity.RankMedalInfo;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 等级勋章表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface RankMedalService extends IService<RankMedal> {

    /**
     * 查询可领取等级勋章
     *
     * @param theaterId
     * @param repertoireId
     * @param amount
     * @param userConsumptionCount
     * @return
     */
    List<RankMedalInfo> findRankMedalCanHave(Long theaterId, Long repertoireId, BigDecimal amount, Long userConsumptionCount);
}
