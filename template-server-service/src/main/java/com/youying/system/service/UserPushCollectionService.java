package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.RankMedalInfo;
import com.youying.common.core.domain.entity.UserPushCollection;
import com.youying.system.domain.userpushcollection.UserPushCollectionResponse;

import java.util.List;

/**
 * <p>
 * 用户推送藏品表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-02
 */
public interface UserPushCollectionService extends IService<UserPushCollection> {

    /**
     * 添加用户推送等级勋章
     *
     * @param rankMedalInfoList
     * @return
     */
    Integer addRankMedalInfo(List<RankMedalInfo> rankMedalInfoList);

    /**
     * 查询用户推送数字藏品
     *
     * @return
     */
    List<UserPushCollectionResponse> listByPage();

    /**
     * 查询纪念徽章领取人数
     *
     * @param souvenirBadgeId
     * @return
     */
    Long findSouvenirBadgeGetCount(Long souvenirBadgeId);

    /**
     * 查询是否推送过等级勋章、纪念勋章
     *
     * @param userId
     * @param relationId
     * @param code
     * @return
     */
    boolean findUserGet(Long userId, Long relationId, Integer code);
}
