package com.youying.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.Leaderboard;
import com.youying.common.core.domain.entity.UserLeaderboard;
import com.youying.common.enums.Enums.DataFlag;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.common.exception.ServiceException;
import com.youying.common.utils.SecurityUtils;
import com.youying.system.domain.leaderboard.AddLeaderboardRequest;
import com.youying.system.domain.leaderboard.LeaderboardResponse;
import com.youying.system.mapper.LeaderboardMapper;
import com.youying.system.service.LeaderboardService;
import com.youying.system.service.UserLeaderboardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 榜单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-09
 */
@Service
public class LeaderboardServiceImpl extends ServiceImpl<LeaderboardMapper, Leaderboard> implements LeaderboardService {
    private static final Integer MAX_ADD_COUNT = 3;
    @Autowired
    private UserLeaderboardService userLeaderboardService;

    /**
     * 添加榜单
     *
     * @param request
     * @return
     */
    @Override
    @Transactional
    public Long add(AddLeaderboardRequest request) {
        Integer count = baseMapper.findLeaderboardCount(DataFlag.USER.getCode(), SecurityUtils.getUserId());
        if (MAX_ADD_COUNT < count + 1) {
            throw new ServiceException("最多可创建3个榜单");
        }

        Leaderboard leaderboard = new Leaderboard();
        BeanUtil.copyProperties(request, leaderboard);
        leaderboard.setType(DataFlag.USER.getCode());
        leaderboard.setUserId(SecurityUtils.getUserId());
        leaderboard.setStatus(StatusFlag.OK.getCode());
        baseMapper.insert(leaderboard);

        UserLeaderboard userLeaderboard = request.getUserLeaderboard();
        if (userLeaderboard != null) {
            userLeaderboard.setLeaderboardId(leaderboard.getId());
            userLeaderboard.setUserId(SecurityUtils.getUserId());
            userLeaderboard.setType(DataFlag.USER.getCode());
            userLeaderboardService.save(userLeaderboard);
        }

        return leaderboard.getId();
    }

    /**
     * 修改榜单
     *
     * @param request
     * @return
     */
    @Override
    @Transactional
    public Long update(AddLeaderboardRequest request) {
        Leaderboard leaderboard = baseMapper.selectById(request.getId());
        leaderboard.setName(request.getName());
        baseMapper.updateById(leaderboard);

        UserLeaderboard userLeaderboard = request.getUserLeaderboard();
        userLeaderboardService.deleteByLeaderboard(request.getId());
        if (userLeaderboard != null) {
            userLeaderboard.setLeaderboardId(leaderboard.getId());
            userLeaderboard.setUserId(SecurityUtils.getUserId());
            userLeaderboard.setType(leaderboard.getType());
            userLeaderboardService.saveOrUpdate(userLeaderboard);
        }

        return leaderboard.getId();
    }

    /**
     * 查询用户榜单列表
     *
     * @return
     */
    @Override
    public List<LeaderboardResponse> findUserLeaderboardList() {
        return baseMapper.findUserLeaderboardList(SecurityUtils.getUserId());
    }

    /**
     * 查询用户榜单最后修改时间
     *
     * @return
     */
    @Override
    public Date findUserLeaderboardLastTime() {
        return baseMapper.findUserLeaderboardLastTime(SecurityUtils.getUserId());
    }

    /**
     * 查询用户榜单列表
     *
     * @return
     */
    @Override
    public Integer findLeaderboardCount() {
        return baseMapper.findLeaderboardCount(DataFlag.USER.getCode(), SecurityUtils.getUserId());
    }

    /**
     * 查询用户榜单详情
     *
     * @param id
     * @return
     */
    @Override
    public LeaderboardResponse details(Long id) {
        return baseMapper.details(id, SecurityUtils.getUserId());
    }
}
