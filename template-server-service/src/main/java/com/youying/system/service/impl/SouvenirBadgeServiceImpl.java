package com.youying.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.SouvenirBadge;
import com.youying.system.domain.souvenirbadge.SouvenirBadgeRequest;
import com.youying.system.domain.souvenirbadge.SouvenirBadgeResponse;
import com.youying.system.mapper.SouvenirBadgeMapper;
import com.youying.system.service.SouvenirBadgeService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 剧场纪念徽章表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@Service
public class SouvenirBadgeServiceImpl extends ServiceImpl<SouvenirBadgeMapper, SouvenirBadge> implements SouvenirBadgeService {

    /**
     * 查询剧场纪念徽章表列表(分页)
     *
     * @param request
     * @return
     */
    @Override
    public List<SouvenirBadgeResponse> listByPage(SouvenirBadgeRequest request) {
        return baseMapper.listByPage(request);
    }

    /**
     * 查询剧场纪念徽章表详情
     *
     * @param id
     * @param relationId
     * @return
     */
    @Override
    public SouvenirBadgeResponse details(Long id, Long relationId) {
        return baseMapper.details(id, relationId);
    }

    /**
     * 查询剧场可领取纪念徽章
     *
     * @param theaterId
     * @return
     */
    @Override
    public List<SouvenirBadge> findUserConsumption(Long theaterId) {
        return baseMapper.findUserConsumption(theaterId);
    }
}
