package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.User;

/**
 * <p>
 * 用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface UserService extends IService<User> {

    /**
     * 根据手机查询用户信息
     *
     * @param phone
     * @return
     */
    User findUserByPhone(String phone);

    /**
     * 根据openId查询用户信息
     *
     * @param openId
     * @return
     */
    User findUserByOpenId(String openId);
}
