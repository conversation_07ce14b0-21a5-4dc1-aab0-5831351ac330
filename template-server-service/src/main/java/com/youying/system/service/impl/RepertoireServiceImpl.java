package com.youying.system.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.Repertoire;
import com.youying.system.domain.repertoire.RepertoireDisplayResponse;
import com.youying.system.domain.repertoire.RepertoireRequest;
import com.youying.system.domain.repertoire.RepertoireResponse;
import com.youying.system.mapper.RepertoireMapper;
import com.youying.system.service.RepertoireService;

/**
 * <p>
 * 剧目表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@Service
public class RepertoireServiceImpl extends ServiceImpl<RepertoireMapper, Repertoire> implements RepertoireService {

    /**
     * 查询首页推荐剧目表列表(分页)
     *
     * @param userId
     * @return
     */
    @Override
    public List<RepertoireResponse> listByIndex(Long userId, Long city) {
        return baseMapper.listByIndex(userId, city);
    }

    /**
     * 根据剧目时间查询剧目
     *
     * @param repertoireName
     * @return
     */
    @Override
    public Repertoire findRepertoireByName(String repertoireName) {
        return getOne(new LambdaQueryWrapper<Repertoire>()
                .eq(Repertoire::getName, repertoireName).last("limit 1"));
    }

    /**
     * 查询剧目表列表(分页)
     *
     * @param request
     * @return
     */
    @Override
    public List<RepertoireResponse> listByPage(RepertoireRequest request) {
        return baseMapper.listByPage(request);
    }

    /**
     * 剧目详情
     *
     * @param id
     * @param userId
     * @return
     */
    @Override
    public RepertoireResponse details(Long id, Long userId) {
        return baseMapper.details(id, userId);
    }

    /**
     * 查询剧目橱窗列表
     *
     * @param repertoireId
     * @return
     */
    @Override
    public List<RepertoireDisplayResponse> findRepertoireDisplay(Long repertoireId) {
        return baseMapper.findRepertoireDisplay(repertoireId == null ? 0 : repertoireId);
    }

}
