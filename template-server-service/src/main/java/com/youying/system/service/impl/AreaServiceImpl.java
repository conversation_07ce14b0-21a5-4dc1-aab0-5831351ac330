package com.youying.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.Area;
import com.youying.system.domain.area.AreaTree;
import com.youying.system.mapper.AreaMapper;
import com.youying.system.service.AreaService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 地区表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@Service
public class AreaServiceImpl extends ServiceImpl<AreaMapper, Area> implements AreaService {
    /**
     * 地区树形
     *
     * @return
     */
    @Override
    public List<AreaTree> findAreaTree() {
        List<AreaTree> areas = baseMapper.findAreaAll();
        if (CollectionUtils.isNotEmpty(areas)) {
            Map<Long, AreaTree> treeMap = areas.stream().collect(Collectors.toMap(item -> item.getId(), item -> item));
            List<AreaTree> areaList = areas.stream().filter(item -> !treeMap.containsKey(item.getParentId())).collect(Collectors.toList());
            for (AreaTree area : areas) {
                if (treeMap.containsKey(area.getParentId())) {
                    AreaTree parentTree = treeMap.get(area.getParentId());
                    List<AreaTree> children = parentTree.getChildren();
                    children.add(area);
                }
            }
            return areaList;
        }
        return areas;
    }
}
