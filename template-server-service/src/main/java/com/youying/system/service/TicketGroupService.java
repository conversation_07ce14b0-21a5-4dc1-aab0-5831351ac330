package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.common.PullResponse;
import com.youying.common.core.domain.entity.TicketGroup;
import com.youying.system.domain.ticketgroup.AddTicketGroupRequest;
import com.youying.system.domain.ticketgroup.TicketGroupResponse;

import java.util.List;

/**
 * <p>
 * 剧目分组表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-08
 */
public interface TicketGroupService extends IService<TicketGroup> {
    /**
     * 添加修改剧目分组数据
     *
     * @param request
     * @return
     */
    Long add(AddTicketGroupRequest request);

    /**
     * 修改剧目分组数据
     *
     * @param request
     * @return
     */
    Long update(AddTicketGroupRequest request);

    /**
     * 查询用户分类数据
     *
     * @param id
     * @return
     */
    List<TicketGroupResponse> details(Long id);

    /**
     * 删除剧目分组
     *
     * @param id
     * @return
     */
    Long delete(Long id);

    /**
     * 查询用户电子票分组数量
     *
     * @return
     */
    Integer findTicketGroupCount();

    /**
     * 查询用户电子票分组下拉
     *
     * @return
     */
    List<PullResponse> pull();

    /**
     * 查询用户电子票分组
     *
     * @return
     */
    List<TicketGroupResponse> findTicketGroupList();
}
