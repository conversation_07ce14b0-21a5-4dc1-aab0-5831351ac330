package com.youying.system.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.Repertoire;
import com.youying.system.domain.repertoire.RepertoireDisplayResponse;
import com.youying.system.domain.repertoire.RepertoireRequest;
import com.youying.system.domain.repertoire.RepertoireResponse;

/**
 * <p>
 * 剧目表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface RepertoireService extends IService<Repertoire> {

    /**
     * 查询首页推荐剧目表列表(分页)
     *
     * @param userId
     * @return
     */
    List<RepertoireResponse> listByIndex(Long userId, Long city);

    /**
     * 根据剧目时间查询剧目
     *
     * @param repertoireName
     * @return
     */
    Repertoire findRepertoireByName(String repertoireName);

    /**
     * 查询剧目表列表(分页)
     *
     * @param request
     * @return
     */
    List<RepertoireResponse> listByPage(RepertoireRequest request);

    /**
     * 剧目详情
     *
     * @param id
     * @param userId
     * @return
     */
    RepertoireResponse details(Long id, Long userId);

    /**
     * 查询剧目橱窗列表
     *
     * @param repertoireId
     * @return
     */
    List<RepertoireDisplayResponse> findRepertoireDisplay(Long repertoireId);

}
