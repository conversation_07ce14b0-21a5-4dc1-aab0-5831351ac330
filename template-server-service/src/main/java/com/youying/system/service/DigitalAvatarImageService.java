package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.DigitalAvatarImage;

import java.util.List;

/**
 * <p>
 * 数字头像图片表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface DigitalAvatarImageService extends IService<DigitalAvatarImage> {

    /**
     * 根据数字头像ID随机获取组件
     *
     * @param digitalAvatarId
     * @return
     */
    List<String> findDigitalAvatarImageByDigitalAvatarId(Long digitalAvatarId);

    /**
     * 查询电子头像组件
     *
     * @param digitalAvatarId
     * @return
     */
    List<DigitalAvatarImage> findAvatarImageByDigitalAvatar(Long digitalAvatarId);
}
