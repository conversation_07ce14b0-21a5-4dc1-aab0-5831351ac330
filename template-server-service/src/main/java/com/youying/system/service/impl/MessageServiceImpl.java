package com.youying.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.Message;
import com.youying.common.utils.SecurityUtils;
import com.youying.system.domain.usermessage.UserMessageResponse;
import com.youying.system.mapper.MessageMapper;
import com.youying.system.service.MessageService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 群发消息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@Service
public class MessageServiceImpl extends ServiceImpl<MessageMapper, Message> implements MessageService {

    /**
     * 查询群发消息表列表(分页)
     *
     * @return
     */
    @Override
    public List<UserMessageResponse> listByPage() {
        return baseMapper.listByPage(SecurityUtils.getUserId());
    }
}
