package com.youying.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.Issue;
import com.youying.common.core.domain.entity.UserInteraction;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.common.enums.Enums.UserInteractionFlag;
import com.youying.common.utils.SecurityUtils;
import com.youying.system.domain.issue.AddIssueRequest;
import com.youying.system.domain.issue.IssueRequest;
import com.youying.system.domain.issue.IssueResponse;
import com.youying.system.mapper.IssueMapper;
import com.youying.system.service.IssueService;
import com.youying.system.service.RepertoireService;
import com.youying.system.service.TheaterService;
import com.youying.system.service.UserInteractionService;
import com.youying.system.service.UserReceivingRecordsService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 剧目剧场问答表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@Service
public class IssueServiceImpl extends ServiceImpl<IssueMapper, Issue> implements IssueService {
    @Autowired
    private TheaterService theaterService;
    @Autowired
    private RepertoireService repertoireService;
    @Autowired
    private UserReceivingRecordsService userReceivingRecordsService;
    @Autowired
    private UserInteractionService userInteractionService;

    /**
     * 查询剧目剧场问答列表(父级)
     *
     * @param request
     * @return
     */
    @Override
    public List<IssueResponse> listByPage(IssueRequest request) {
        return baseMapper.listByPage(request);
    }

    /**
     * 查询剧目剧场问答列表(子级)
     *
     * @param request
     * @return
     */
    @Override
    public List<IssueResponse> listByParentId(IssueRequest request) {
        return baseMapper.listByParentId(request);
    }

    /**
     * 添加问答
     *
     * @param issue
     * @return
     */
    @Override
    @Transactional
    public Long add(Issue issue) {
        List<Long> userIds = new ArrayList<>(5);
        if (issue.getTheaterId() != null) {
            theaterService.getById(issue.getTheaterId());
            issue.setMerchantId(theaterService.getById(issue.getTheaterId()).getMerchantId());
        } else {
            issue.setMerchantId(repertoireService.getById(issue.getRepertoireId()).getMerchantId());
        }
        issue.setStatus(StatusFlag.OK.getCode());
        issue.setUserId(SecurityUtils.getUserId());
        save(issue);

        // 随机获取5个已经观影用户回答
        userIds = userReceivingRecordsService.findRandomUser(issue.getRepertoireId(), issue.getTheaterId(), SecurityUtils.getUserId());
        if (CollectionUtils.isNotEmpty(userIds)) {
            for (Long userId : userIds) {
                // 添加问答消息
                userInteractionService.add(issue.getId(), null, userId, issue.getRepertoireId(), UserInteractionFlag.ISSUE.getCode());
            }
        }

        return issue.getId();
    }

    /**
     * 回复
     *
     * @param request
     * @return
     */
    @Override
    @Transactional
    public Long reply(AddIssueRequest request) {
        Issue issue = request.getIssue();
        Issue parentIssue = getById(issue.getParentId());
        issue.setUserId(parentIssue.getUserId());
        issue.setReplyId(SecurityUtils.getUserId());
        issue.setTop(StatusFlag.PROHIBITION.getCode());
        issue.setStatus(StatusFlag.OK.getCode());
        save(issue);

        UserInteraction userInteraction = userInteractionService.getById(request.getId());
        userInteraction.setReplyRelevanceId(issue.getId());
        userInteractionService.updateById(userInteraction);

        return issue.getId();
    }
}
