package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.DigitalAvatar;
import com.youying.common.core.page.PageDomain;
import com.youying.system.domain.digitalavatar.DigitalAvatarResponse;

import java.util.List;

/**
 * <p>
 * 数字头像表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface DigitalAvatarService extends IService<DigitalAvatar> {

    /**
     * 根据剧目ID查询数字头像ID
     *
     * @param repertoireId
     * @param theaterId
     * @return
     */
    List<DigitalAvatar> findDigitalAvatarById(Long repertoireId, Long theaterId);

    /**
     * 根据剧目ID查询数字头像（随机挑选）
     *
     * @param repertoireId
     * @return
     */
    List<String> findDigitalAvatarByRepertoireId(Long repertoireId);

    /**
     * 判断数字头像是否有组件
     *
     * @param repertoireId
     * @return
     */
    Long findDigitalAvatarImage(Long repertoireId);

    /**
     * 获取数字头像路径
     *
     * @param digitalAvatarId
     * @param number
     * @return
     */
    List<String> findDigitalAvatar(Long digitalAvatarId, Long number);

    /**
     * 数字头像列表
     *
     * @param pageDomain
     * @return
     */
    List<DigitalAvatarResponse> listByPage(PageDomain pageDomain);

    /**
     * 查询数字头像表详情
     *
     * @param portfolioId
     * @return
     */
    DigitalAvatarResponse getDigitalAvatarInfo(Long portfolioId);
}
