package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.Issue;
import com.youying.system.domain.issue.AddIssueRequest;
import com.youying.system.domain.issue.IssueRequest;
import com.youying.system.domain.issue.IssueResponse;

import java.util.List;

/**
 * <p>
 * 剧目剧场问答表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface IssueService extends IService<Issue> {

    /**
     * 查询剧目剧场问答列表(父级)
     *
     * @param request
     * @return
     */
    List<IssueResponse> listByPage(IssueRequest request);

    /**
     * 查询剧目剧场问答列表(子级)
     *
     * @param request
     * @return
     */
    List<IssueResponse> listByParentId(IssueRequest request);

    /**
     * 添加问答
     *
     * @param issue
     * @return
     */
    Long add(Issue issue);

    /**
     * 回复
     *
     * @param request
     * @return
     */
    Long reply(AddIssueRequest request);
}
