package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.UserMessage;
import com.youying.system.domain.usermessage.UserMessageResponse;

import java.util.List;

/**
 * <p>
 * 用户会话表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
public interface UserMessageService extends IService<UserMessage> {
    /**
     * 用户会话表列表
     *
     * @return
     */
    List<UserMessageResponse> listByPage();

    /**
     * 删除会话列表
     *
     * @param ids
     * @return
     */
    Integer delete(List<Long> ids);

    /**
     * 添加会话
     *
     * @param userMessage
     * @return
     */
    Long add(UserMessage userMessage);

    /**
     * 添加会话并发送欢迎语
     *
     * @param threadId
     * @param repertoireId
     * @param userId
     * @return
     */
    Long addUserMessage(Long threadId, Long repertoireId, Long userId);

    /**
     * 查询用户会话表详情
     *
     * @param id
     * @return
     */
    UserMessageResponse details(Long id);
}
