package com.youying.system.domain.usernotify;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023-06-13
 */
@Data
public class UserNotifyResponse {

    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 通知ID
     */
    private Long notifyId;

    /**
     * 查看状态
     */
    private Integer lookFlag;

    /**
     * 标题
     */
    private String title;

    /**
     * 查看状态
     */
    private String body;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
