package com.youying.system.domain.souvenirbadge;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.youying.common.core.domain.entity.SouvenirBadgeRequire;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/5/12
 */
@Data
public class AddSouvenirBadgeRequest {
    private Long id;

    /**
     * 藏品名称
     */
    private String name;

    /**
     * 剧场ID
     */
    private Long theaterId;

    /**
     * 徽章模型，支持上传3D模型，大小100M以内
     */
    private String model;

    /**
     * 发行方
     */
    private String issuerName;

    /**
     * 发放数量
     */
    private Integer issuedQuantity;

    /**
     * 发放时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    /**
     * 藏品简介
     */
    private String introduction;

    /**
     * 短信通知
     */
    private Integer smsNotify;

    /**
     * 纪念徽章条件
     */
    private SouvenirBadgeRequire souvenirBadgeRequire;
}
