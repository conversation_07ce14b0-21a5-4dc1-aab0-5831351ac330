package com.youying.system.domain.userreceivingrecords;

import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.page.PageDomain;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2023/5/12
 */
@Data
public class UserReceivingRecordsRequest extends PageDomain {
    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 1：电子票、2：数字头像、3：纪念徽章、4：等级勋章
     */
    private Integer badgeType;

    /**
     * 佩戴状态
     */
    private Integer adorn;

    /**
     * 是否查询电子票夹 1:查询 0:不查询 默认查询
     */
    private Integer electronicTicket = 1;

    /**
     * 分组ID
     */
    private Long ticketGroupId;

    /**
     * 演出时间
     */
    private TimeRequest time = new TimeRequest();

    /**
     * 领取演出
     */
    private TimeRequest getTime = new TimeRequest();
}
