package com.youying.system.domain.rankmedal;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.youying.common.core.domain.entity.RankMedalInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/5/12
 */
@Data
public class RankMedalResponse implements Serializable {
    private Long id;

    /**
     * 等级勋章名称
     */
    private String name;

    /**
     * 剧场ID
     */
    private Long theaterId;

    /**
     * 剧目ID
     */
    private Long repertoireId;

    /**
     * 剧场
     */
    private String theaterName;

    /**
     * 剧目
     */
    private String repertoireName;

    /**
     * 审核（0待审核，1驳回，2通过）
     */
    private Integer audit;

    /**
     * 审核通过时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditPassTime;

    /**
     * 驳回原因
     */
    private String reasonsRejection;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 勋章等级
     */
    private String rankMedalLevel;

    /**
     * 领取数量
     */
    private Integer receivedNumber;

    /**
     * 等级勋章详情
     */
    private List<RankMedalInfo> rankMedalInfoList = new ArrayList<RankMedalInfo>();

}