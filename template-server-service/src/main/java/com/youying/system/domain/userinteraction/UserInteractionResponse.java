package com.youying.system.domain.userinteraction;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023-06-18
 */
@Data
public class UserInteractionResponse {
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户头像
     */
    private String userAvatar;

    /**
     * 回复者用户名称
     */
    private String replyUserName;

    /**
     * 回复者用户头像
     */
    private String replyUserAvatar;

    /**
     * 关联用户ID
     */
    private Long replyUserId;

    /**
     * 关联ID
     */
    private Long relevanceId;

    /**
     * 回复关联ID （如果提问中该数据为null则用户未进行回复）
     */
    private Long replyRelevanceId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 评论信息
     */
    private String commentContent;

    /**
     * 回复者评论信息
     */
    private String replyCommentContent;

    /**
     * 动态内容
     */
    private String dynamicBody;

    /**
     * 提问信息
     */
    private String issueContent;

    /**
     * 提问回复信息
     */
    private String replyIssueContent;

    /**
     * 是否有回答 > 0 存在用户回复
     */
    private Integer issueCount;

    /**
     * 互动类型（1、评论，2、评论点赞，3、提问，4、动态点赞）
     */
    private Integer type;

    /**
     * 剧目名称
     */
    private String repertoireName;

    /**
     * 剧目图片
     */
    private String repertoireCoverPicture;

    /**
     * 剧场图片
     */
    private String theaterCoverPicture;

    /**
     * 剧目ID
     */
    private Long repertoireId;
}
