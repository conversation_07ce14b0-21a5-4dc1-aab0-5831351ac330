package com.youying.system.domain.repertoireinfo;

import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.page.PageDomain;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-05-23
 */
@Data
public class RepertoireInfoRequest extends PageDomain {
    /**
     * 剧场ID
     */
    private Long theaterId;

    /**
     * 剧目ID
     */
    private Long repertoireId;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 用户ID 未登录传 0
     */
    private Long userId = 0L;

    /**
     * 时间
     */
    private TimeRequest time = new TimeRequest();
}
