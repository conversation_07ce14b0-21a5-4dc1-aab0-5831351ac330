package com.youying.system.domain.repertoireticket;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023-05-24
 */
@Data
public class RepertoireTicketResponse {
    private Long id;

    /**
     * 批次
     */
    private Integer sort;

    /**
     * 剧场ID
     */
    private Long theaterId;

    /**
     * 剧场
     */
    private String theaterName;

    /**
     * 剧目ID
     */
    private Long repertoireId;

    /**
     * 剧目
     */
    private String repertoireName;

    /**
     * 封面版式正面
     */
    private String coverFront;

    /**
     * 封面版式反面
     */
    private String coverReverse;

    /**
     * 剧目封面图
     */
    private String repertoireCoverPicture;

    /**
     * 剧场封面图
     */
    private String theaterCoverPicture;

    /**
     * 发行方
     */
    private String issuerName;

    /**
     * 发放数量
     */
    private Integer issuedQuantity;

    /**
     * 发放时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 审核（0待审核，1驳回，2通过）
     */
    private Integer audit;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 驳回原因
     */
    private String reasonsRejection;

    /**
     * 领取数量
     */
    private Integer getCount;

    /**
     * 发放时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 藏品组合ID
     */
    private Long portfolioId;

    /**
     * 藏品组合
     */
    private String portfolioNo;

    /**
     * 藏品组合名称
     */
    private String portfolioName;

    /**
     * 普通电子票图片
     */
    private String commonImage;

    /**
     * 升级状态（0：待升级，1：已升级）
     */
    private Integer upgradeStatus;

    /**
     * 升级藏品路径
     */
    private String upgradeImage;

    /**
     * 藏品路径
     */
    private String image;

    /**
     * 领取ID
     */
    private Long userReceivingRecordsId;

    /**
     * 销售状态 0：已售罄
     */
    private Integer soldOut;
}
