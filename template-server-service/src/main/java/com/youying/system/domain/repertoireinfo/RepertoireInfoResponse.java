package com.youying.system.domain.repertoireinfo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.youying.common.core.domain.entity.RepertoireInfoDetail;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-05-23
 */
@Data
public class RepertoireInfoResponse {
    private Long id;

    /**
     * 剧目ID
     */
    private Long repertoireId;

    /**
     * 剧目名称
     */
    private String repertoireName;

    /**
     * 剧场ID
     */
    private String theaterId;

    /**
     * 剧场名称
     */
    private String theaterName;

    /**
     * 剧目封面图URL
     */
    private String coverPicture;

    /**
     * 剧目简介
     */
    private String introduction;

    /**
     * 点赞数
     */
    private Integer likeCount;

    /**
     * 点踩数
     */
    private Integer dislikeCount;

    /**
     * 是否关注收藏(大于0则未收藏)
     */
    private Integer fansFlag;

    /**
     * 评论数
     */
    private Integer commentCount;

    /**
     * 剧目结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 剧目开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 剧目标签
     */
    private String repertoireLabelName;

    /**
     * 用户头像-限制至多为5个
     */
    private List<String> userAvatar;

    /**
     * 点赞率
     */
    private String likeRatio;

    /**
     * 剧目剧场关联场次时间详情
     */
    private List<RepertoireInfoDetail> repertoireInfoDetailList = new ArrayList<>();
}
