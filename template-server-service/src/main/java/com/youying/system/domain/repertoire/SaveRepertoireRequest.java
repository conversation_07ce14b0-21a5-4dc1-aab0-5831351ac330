package com.youying.system.domain.repertoire;

import com.youying.common.core.domain.entity.Repertoire;
import com.youying.common.core.domain.entity.RepertoireActor;
import com.youying.common.core.domain.entity.RepertoireCreativeTeam;
import com.youying.common.core.domain.entity.RepertoireLabel;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-05-23
 */
@Data
public class SaveRepertoireRequest {
    /**
     * 剧目信息
     */
    private Repertoire repertoire;

    /**
     * 剧目演员信息
     */
    private List<RepertoireActor> repertoireActorList;

    /**
     * 剧目主创团队信息
     */
    private List<RepertoireCreativeTeam> repertoireCreativeTeamList;

    /**
     * 剧目标签
     */
    private List<RepertoireLabel> repertoireLabelList;
}
