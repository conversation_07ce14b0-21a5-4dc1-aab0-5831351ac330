package com.youying.system.domain.leaderboard;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024-01-09
 */
@Data
public class LeaderboardResponse {
    /**
     * 榜单ID
     */
    private Long id;

    /**
     * 用户榜单ID
     */
    private Long userLeaderboardId;

    /**
     * 榜单名称
     */
    private String name;

    /**
     * 剧目ID
     */
    private Long repertoireId;

    /**
     * 剧目
     */
    private String repertoireName;

    /**
     * 剧场
     */
    private String theaterName;

    /**
     * 电子票
     */
    private String image;

    /**
     * 演出时间
     */
    private String time;

    /**
     * 演出地点
     */
    private String cityName;

    /**
     * 电子票ID
     */
    private Long userReceivingRecordsId;

    /**
     * 类型 0：系统 1：用户
     */
    private Integer type;

    /**
     * 封面图
     */
    private String coverPicture;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
