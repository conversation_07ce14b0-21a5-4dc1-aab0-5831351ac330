package com.youying.system.domain.souvenirbadge;

import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.page.PageDomain;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/5/12
 */
@Data
public class SouvenirBadgeRequest extends PageDomain implements Serializable {

    /**
     * 用户ID 未登录传0
     */
    private Long userId;

    /**
     * 创建时间
     */
    private TimeRequest time = new TimeRequest();
}