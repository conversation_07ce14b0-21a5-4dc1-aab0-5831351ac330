package com.youying.system.domain.repertoire;

import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.page.PageDomain;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-05-24
 */
@Data
public class RepertoireRequest extends PageDomain {
    /**
     * 创建时间
     */
    private TimeRequest time = new TimeRequest();

    /**
     * 审核状态
     */
    private Integer audit;

    /**
     * 剧场名称
     */
    private String theaterName;

    /**
     * 剧目名称
     */
    private String repertoireName;

    /**
     * 标签名称
     */
    private String labelName;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 地区ID
     */
    private Long city;
}
