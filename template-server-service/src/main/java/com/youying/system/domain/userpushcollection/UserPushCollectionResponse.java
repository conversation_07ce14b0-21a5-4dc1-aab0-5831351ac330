package com.youying.system.domain.userpushcollection;

import com.youying.system.domain.rankmedal.RankMedalInfoResponse;
import com.youying.system.domain.souvenirbadge.SouvenirBadgeResponse;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023-07-02
 */
@Data
public class UserPushCollectionResponse {
    private Long id;

    /**
     * 剧场ID
     */
    private Long theaterId;

    /**
     * 剧目ID
     */
    private Long repertoireId;

    /**
     * 等级勋章ID
     */
    private Long rankMedalId;

    /**
     * 等级勋章详情ID
     */
    private Long rankMedalInfoId;

    /**
     * 纪念徽章ID
     */
    private Long souvenirBadgeId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 等级勋章
     */
    private RankMedalInfoResponse rankMedalResponse;

    /**
     * 纪念徽章
     */
    private SouvenirBadgeResponse souvenirBadge;
}
