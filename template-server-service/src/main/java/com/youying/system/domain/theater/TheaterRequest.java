package com.youying.system.domain.theater;

import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.page.PageDomain;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/5/12
 */
@Data
public class TheaterRequest extends PageDomain implements Serializable {
    /**
     * 创建时间
     */
    private TimeRequest time = new TimeRequest();

    /**
     * 审核状态
     */
    private Integer audit;

    /**
     * 用户ID
     */
    private Long userId = 0L;
}