package com.youying.system.domain.dynamic;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/5/12
 */
@Data
public class DynamicResponse implements Serializable {
    private Long id;

    /**
     * 商家ID
     */
    private Long merchantId;

    /**
     * 剧场ID (0为暂无)
     */
    private Long theaterId;

    /**
     * 剧目ID (0为暂无)
     */
    private Long repertoireId;

    /**
     * 剧目
     */
    private String repertoireName;

    /**
     * 动态标题
     */
    private String title;

    /**
     * 动态详情
     */
    private String body;

    /**
     * 封面
     */
    private String coverImage;

    /**
     * 动态图片 (最多20张)
     */
    private String images;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 点赞数
     */
    private Integer likeCount;

    /**
     * 点踩数
     */
    private Integer dislikeCount;

    /**
     * 是否关注收藏(大于0则未收藏)
     */
    private Integer fansFlag;

    /**
     * 剧场
     */
    private String theaterName;

    /**
     * 剧目封面图
     */
    private String repertoireCoverPicture;

    /**
     * 剧场封面图
     */
    private String theaterCoverPicture;

    /**
     * 用户点赞详情，0为踩 1为赞 null为为操作
     */
    private Integer kudosStatus;
}