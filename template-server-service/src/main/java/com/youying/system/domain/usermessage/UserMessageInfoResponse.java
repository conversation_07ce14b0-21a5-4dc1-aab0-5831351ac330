package com.youying.system.domain.usermessage;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023-06-13
 */
@Data
public class UserMessageInfoResponse {
    private Long id;

    /**
     * 用户回答
     */
    private String body;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 商家用户
     */
    private String userMerchantId;

    /**
     * 时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
