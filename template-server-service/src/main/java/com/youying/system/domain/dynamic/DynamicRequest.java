package com.youying.system.domain.dynamic;

import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.page.PageDomain;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2023/5/12
 */
@Data
public class DynamicRequest extends PageDomain {
    /**
     * 剧场ID
     */
    private Long theaterId;

    /**
     * 剧目ID
     */
    private Long repertoireId;

    /**
     * 用户ID 未登录传 0
     */
    private Long userId = 0L;

    /**
     * 创建时间
     */
    private TimeRequest time = new TimeRequest();
}