package com.youying.system.domain.blockchain;

import com.bestpay.digital.collection.open.base.BaseReq;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/5/11
 */
@Data
public class GetOpenAccessUrlRequest extends BaseReq {
    /**
     * 业务方侧userId
     */
    private String userId;

    /**
     * 场景页面（queryMySku，queryMySkuList)
     */
    private String scene;

    /**
     * 根据secene场景区分
     * queryMySku - shardNo,skuId,theme(AccessTheme)
     * queryMySkuList - theme(AccessTheme)
     * queryGoodsSaleList - channelCode,theme(optional)
     * queryGoodsDetail - goodsId,channelCode,theme(optional)
     * 传入示例：
     * scene = queryMySku
     * sceneParam = {"shardNo","xxx","skuId":"xxx","theme":"light"}
     */
    private Map<String, String> sceneParam;
}