package com.youying.system.domain.blockchain;

import com.bestpay.digital.collection.open.base.BaseReq;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2023/5/11
 */
@Data
public class QueryUserSkuResponse extends BaseReq {
    /**
     * 藏品凭证id
     */
    private String productName;

    /**
     * 藏品凭证编号
     */
    private String skuImg;

    /**
     * 藏品名
     */
    private String skuId;

    /**
     * 藏品图片
     */
    private String modelType;

    /**
     * 藏品id
     */
    private String shardNo;

    /**
     * 藏品库存数
     */
    private String totalStock;

    /**
     * 我的区块链地址
     */
    private String cId;

    /**
     * 出品方名称
     */
    private String ipPublisherName;

    /**
     * 凭证id(藏品哈希)
     */
    private String shardId;

    /**
     * 藏品描述
     */
    private String productDesc;

    /**
     * 新3D引擎文件
     */
    private String modelFile;

    /**
     * 藏品模型亮度
     */
    private String skuBrightness;

    /**
     * 藏品视频彩蛋
     */
    private String skuVideoUrl;

    /**
     * 藏品音频彩蛋
     */
    private String skuSoundUrl;

    /**
     * 起售时间
     */
    private String onSaleStartDateTime;

}