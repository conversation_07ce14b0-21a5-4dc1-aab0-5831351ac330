package com.youying.system.domain.blockchain;

import com.bestpay.digital.collection.open.base.BaseReq;
import com.bestpay.digital.collection.open.req.OpenCreateDropReq;
import com.bestpay.digital.collection.open.req.OpenSkuCreateReq.EggJson;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2023/5/11
 */
@Data
public class CreateSkuRequest extends BaseReq {
    /**
     * 创建人 业务自定义
     */
    private String userName;

    /**
     * 藏品名称
     */
    private String productName;

    /**
     * 预览图
     */
    private String previewPic;

    /**
     * 模型类型  PIC，VIDEO
     */
    private String templateType;

    /**
     * 发行方id  （运营后台创建）
     */
    private String issuerId;

    /**
     * 出平方id（运营后台创建）
     */
    private String manufacturerId;

    /**
     * 模型url
     */
    private String templateJson;

    /**
     * VIDEO MUSIC PHOTO OTHER 文件类型
     */
    private String eggType;

    /**
     * 菜单URL
     */
    private EggJson eggJson;

    /**
     * 描述
     */
    private String description;

    /**
     * 库存
     */
    private Integer productStock;

    /**
     * 藏品高度
     */
    private String skuBrightness;

    /**
     * 想创建活动，必须上链true
     */
    private Boolean onChain;

    /**
     *
     */
    private Boolean createDrop;

    /**
     * 创建活动
     */
    private OpenCreateDropReq openCreateDropReq;

}
