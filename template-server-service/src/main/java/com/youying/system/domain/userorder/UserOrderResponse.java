package com.youying.system.domain.userorder;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.youying.common.core.domain.entity.UserReceivingRecords;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-08-18
 */
@Data
public class UserOrderResponse {
    private Long id;

    /**
     * 商家ID
     */
    private Long merchantId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 交易编号
     */
    private String transactionId;

    /**
     * 支付编号
     */
    private String orderNo;

    /**
     * 预支付交易会话标识
     */
    private String prepayId;

    /**
     * 支付金额
     */
    private BigDecimal payPrice;

    /**
     * 退款金额
     */
    private BigDecimal refundPrice;

    /**
     * 支付状态（0待支付，1已支付，2已取消）
     */
    private Integer payStatus;

    /**
     * 退款状态（0未退款，1已退款）
     */
    private Integer refundStatus;

    /**
     * 支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /**
     * 退款时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date refundTime;

    /**
     * 用户数字藏品领取记录ID
     */
    private Long userReceivingRecordsId;

    /**
     * 剧场ID
     */
    private Long theaterId;

    /**
     * 剧目ID
     */
    private Long repertoireId;

    /**
     * 1：电子票、2：数字头像、3：纪念徽章、4：等级勋章 ID
     */
    private Long relationId;

    /**
     * 徽章类型（1：电子票、2：数字头像、3：纪念徽章、4：等级勋章）
     */
    private Integer badgeType;

    /**
     * 剧目封面图
     */
    private String repertoireCoverPicture;

    /**
     * 剧目名称
     */
    private String repertoireName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 数字藏品组合名称
     */
    private String portfolioName;

    /**
     * 金额
     */
    private BigDecimal price;

    /**
     *
     */
    private List<UserReceivingRecords> userReceivingRecordList = new ArrayList<>();
}
