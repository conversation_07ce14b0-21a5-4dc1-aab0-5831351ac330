package com.youying.system.domain.souvenirbadge;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.youying.common.core.domain.entity.SouvenirBadgeRequire;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/5/12
 */
@Data
public class SouvenirBadgeResponse implements Serializable {
    private Long id;

    /**
     * 商家ID
     */
    private Long merchantId;

    /**
     * 徽章类型（1：电子票、2：数字头像、3：纪念徽章、4：等级勋章）
     */
    private Integer badgeType;

    /**
     * 藏品名称
     */
    private String name;

    /**
     * 剧场
     */
    private String theaterName;

    /**
     * 剧场ID
     */
    private Long theaterId;

    /**
     * 电子票封面版式正面
     */
    private String coverFront;

    /**
     * 电子票封面版式反面
     */
    private String coverReverse;

    /**
     * 封面图URL
     */
    private String coverPicture;

    /**
     * 剧场封面图URL
     */
    private String theaterCoverPicture;

    /**
     * 剧场
     */
    private String repertoireName;

    /**
     * 剧场封面图URL
     */
    private String repertoireCoverPicture;

    /**
     * 徽章模型，支持上传3D模型，大小100M以内
     */
    private String model;

    /**
     * 发行方
     */
    private String issuerName;

    /**
     * 发放数量
     */
    private Integer issuedQuantity;

    /**
     * 发放时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    /**
     * 藏品简介
     */
    private String introduction;

    /**
     * 短信通知
     */
    private Integer smsNotify;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 审核（0待审核，1驳回，2通过）
     */
    private Integer audit;

    /**
     * 驳回原因
     */
    private String reasonsRejection;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 领取数量
     */
    private Integer receivedNumber;

    /**
     * 领取规则
     */
    private SouvenirBadgeRequire souvenirBadgeRequire;

    /**
     * 用户领取徽章ID 为空则未领取
     */
    private Long relationId;

    /**
     * 藏品编号
     */
    private String collectionNo;

    /**
     * 区块链发放序列ID
     */
    private String sendId;

    /**
     * 观影次数
     */
    private Integer lookNumber;

    /**
     * 销售状态 0：已售罄
     */
    private Integer soldOut;

    /**
     * 剧目观影开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date repertoireStartTime;

    /**
     * 剧目观影结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date repertoireEndTime;

    /**
     * 规定观影开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lookStartTime;

    /**
     * 规定观影结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lookEndTime;

    /**
     * 规定观影时间内观影次数
     */
    private Integer timeLookNumber;
}