package com.youying.system.domain.comment;

import com.youying.common.core.domain.entity.Comment;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2023-06-08
 */
@Data
public class SaveCommentRequest {
    /**
     * 观影记录ID
     */
    private Long userReceivingRecordsId;

    /**
     * 剧场ID (0为暂无)
     */
    @NotNull(message = "剧场ID不能为空")
    private Long theaterId;

    /**
     * 剧目ID (0为暂无)
     */
    @NotNull(message = "剧目ID不能为空")
    private Long repertoireId;

    /**
     * 剧目场次ID
     */
    private Long repertoireInfoDetailId;

    /**
     * 仅自己可见
     */
    private Integer visible;

    /**
     * 剧场点赞情况 （0：踩 1：赞） 不操作不传
     */
    private Integer theaterFlag;

    /**
     * 剧目点赞情况 （0：踩 1：赞） 不操作不传
     */
    private Integer repertoireFlag;

    /**
     * 评论内容
     */
    private Comment comment;
}
