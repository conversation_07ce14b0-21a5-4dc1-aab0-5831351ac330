package com.youying.system.domain.theater;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/5/12
 */
@Data
public class TheaterResponse implements Serializable {
    private Long id;

    /**
     * 剧场名称
     */
    private String name;

    /**
     * 商家ID
     */
    private Long merchantId;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 省
     */
    private Long provId;

    /**
     * 市
     */
    private Long cityId;

    /**
     * 区
     */
    private Long areaId;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 封面图URL
     */
    private String coverPicture;

    /**
     * 剧目图片,拼接
     */
    private String pictures;

    /**
     * 好评率
     */
    private Double goodRatingRate;

    /**
     * 关注数量
     */
    private Integer focusNumber;

    /**
     * 备注
     */
    private String remark;

    /**
     * 推荐（0否，1是）
     */
    private Integer recommend;

    /**
     * 审核（0待审核，1驳回，2通过）
     */
    private Integer audit;

    /**
     * 审核通过时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditPassTime;

    /**
     * 驳回原因
     */
    private String reasonsRejection;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 评论数量
     */
    private Integer commentCount;

    /**
     * 互动数量
     */
    private Integer interactionCount;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 是否关注收藏(大于0则未收藏)
     */
    private Integer fansFlag;

    /**
     * 点赞数
     */
    private Integer likeCount;

    /**
     * 点踩数
     */
    private Integer dislikeCount;
}