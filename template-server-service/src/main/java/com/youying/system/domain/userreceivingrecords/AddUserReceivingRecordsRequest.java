package com.youying.system.domain.userreceivingrecords;

import com.youying.common.core.domain.entity.UserReceivingRecordsText;
import com.youying.system.domain.repertoireticket.GetRepertoireTicketRequest;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-08-17
 */
@Data
public class AddUserReceivingRecordsRequest {
    /**
     * 藏品组合ID
     */
    private Long portfolioId;

    /**
     * 藏品组合ID
     */
    private Long portfolioInfoId;

    /**
     * 剧目剧场NO
     */
    private String no;

    /**
     * 扫描图片路径
     */
    private String fileUrl;

    /**
     * 演员信息
     */
    private List<UserReceivingRecordsText> actorInformationList;

    /**
     * 领取信息
     */
    private List<GetRepertoireTicketRequest> repertoireTicketList;
}
