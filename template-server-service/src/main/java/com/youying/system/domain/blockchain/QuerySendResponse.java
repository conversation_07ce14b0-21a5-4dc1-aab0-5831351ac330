package com.youying.system.domain.blockchain;

import com.bestpay.digital.collection.open.base.BaseReq;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2023/5/11
 */
@Data
public class QuerySendResponse extends BaseReq {
    /**
     * 藏品凭证id
     */
    private String shardingId;

    /**
     * 藏品凭证编号
     */
    private String shardingNo;

    /**
     * 藏品名
     */
    private String productName;

    /**
     * 藏品图片
     */
    private String skuImg;

    /**
     * 藏品id
     */
    private String skuId;

    /**
     * 藏品库存数
     */
    private String totalStorage;
}