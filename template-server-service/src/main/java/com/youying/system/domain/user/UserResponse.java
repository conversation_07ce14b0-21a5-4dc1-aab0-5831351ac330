package com.youying.system.domain.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023-05-23
 */
@Data
public class UserResponse {
    private Long id;

    /**
     * 首次登录标识
     */
    private String fistLogin;

    /**
     * 编号
     */
    private String no;

    /**
     * 名称
     */
    private String name;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 性别
     */
    private Integer sex;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 个性签名
     */
    private String personalizedSignature;

    /**
     * IP地址
     */
    private String ipaddr;

    /**
     * 等级勋章ID
     */
    private Long rankMedalInfoId;

    /**
     * 总消费金额
     */
    private BigDecimal amount;

    /**
     * 观看总数
     */
    private Integer sumLook;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 等级勋章名称
     */
    private String rankMedalName;

    /**
     * 等级勋章名称等级
     */
    private String rankMedalLevel;

    /**
     * 等级勋章颜色
     */
    private String rankMedalColor;
}
