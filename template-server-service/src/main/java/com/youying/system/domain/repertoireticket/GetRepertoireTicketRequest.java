package com.youying.system.domain.repertoireticket;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-05-30
 */
@Data
public class GetRepertoireTicketRequest {
    /**
     * 等级徽章\纪念徽章领取ID
     */
    private Long id;

    /**
     * 纸质扫描信息编号
     */
    private String no;

    /**
     * 演员信息
     */
    private String actorInformation;

    /**
     * 座位号
     */
    private String seatNumber;

    /**
     * 消费金额
     */
    private String amount;

    /**
     * 藏品编号
     */
    private String skuId;

    /**
     * 合成数字头像图片
     */
    private String image;

    /**
     * 升级藏品路径
     */
    private String upgradeImage;

    /**
     * 剧场ID
     */
    private Long theaterId;

    /**
     * 时间
     */
    private String time;

    /**
     * 剧目ID
     */
    private Long repertoireId;

    /**
     * 电子票、数字头像、纪念徽章、等级勋章 ID
     */
    private Long relationId;

    /**
     * 1：电子票、2：数字头像、3：纪念徽章、4：等级勋章 ID
     */
    private Integer badgeType;

    /**
     * 剧目场次ID
     */
    private Long repertoireInfoDetailId;

    /**
     * 剧目剧场关联ID
     */
    private Long repertoireInfoId;

    /**
     * 发行方
     */
    private String issuerName;
}
