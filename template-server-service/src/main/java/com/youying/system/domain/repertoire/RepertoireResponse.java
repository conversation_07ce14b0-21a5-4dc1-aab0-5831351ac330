package com.youying.system.domain.repertoire;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.youying.common.core.domain.entity.RepertoireActor;
import com.youying.common.core.domain.entity.RepertoireCreativeTeam;
import com.youying.system.domain.repertoireinfo.RepertoireInfoResponse;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-06-07
 */
@Data
public class RepertoireResponse {
    private Long id;

    /**
     * 剧目名称
     */
    private String name;

    /**
     * 商家ID
     */
    private Long merchantId;

    /**
     * 剧目类型ID
     */
    private Long repertoireTypeId;

    /**
     * 封面图URL
     */
    private String coverPicture;

    /**
     * 剧目图片（8张）,拼接
     */
    private String pictures;

    /**
     * 简介（500字）
     */
    private String introduction;

    /**
     * 评分
     */
    private Double rating;

    /**
     * 推荐（0否，1是）
     */
    private Integer recommend;

    /**
     * 好评率
     */
    private Double goodRatingRate;

    /**
     * 点赞率
     */
    private String likeRatio;

    /**
     * 关注数量
     */
    private Integer focusNumber;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 是否关注收藏(大于0则为收藏)
     */
    private Integer fansFlag;

    /**
     * 点赞数
     */
    private Integer likeCount;

    /**
     * 点踩数
     */
    private Integer dislikeCount;

    /**
     * 评论数
     */
    private Integer commentCount;

    /**
     * 剧目标签
     */
    private String repertoireLabelName;

    /**
     * 最后评论时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastTime;

    /**
     * 是否包含市区
     */
    private Integer cityCount;

    /**
     * 剧目剧场关联
     */
    private List<RepertoireInfoResponse> repertoireInfoList = new ArrayList<>();

    /**
     * 演员列表
     */
    private List<RepertoireActor> repertoireActorList = new ArrayList<>();

    /**
     * 主创团队列表
     */
    private List<RepertoireCreativeTeam> repertoireCreativeTeamList = new ArrayList<>();
}
