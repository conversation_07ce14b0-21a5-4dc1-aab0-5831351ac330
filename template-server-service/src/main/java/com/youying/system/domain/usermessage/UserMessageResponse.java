package com.youying.system.domain.usermessage;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-06-13
 */
@Data
public class UserMessageResponse {
    private Long id;

    /**
     * 剧场ID (0为暂无)
     */
    private Long theaterId;

    /**
     * 剧目ID
     */
    private Long repertoireId;

    /**
     * 剧场
     */
    private String theaterName;

    /**
     * 剧目
     */
    private String repertoireName;

    /**
     * 剧场封面图
     */
    private String theaterCoverPicture;

    /**
     * 剧目封面图
     */
    private String repertoireCoverPicture;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户头像
     */
    private String userAvatar;

    /**
     * 查看状态
     */
    private Integer lookFlag;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 内容
     */
    private String body;

    /**
     * 最后一次记录
     */
    private String lastBody;

    /**
     * 未查看条数
     */
    private Integer notLookCount;

    /**
     * 消息详情
     */
    private List<UserMessageInfoResponse> userMessageInfoList = new ArrayList<UserMessageInfoResponse>();
}
