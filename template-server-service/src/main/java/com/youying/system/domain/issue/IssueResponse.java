package com.youying.system.domain.issue;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023-06-08
 */
@Data
public class IssueResponse {
    private Long id;

    /**
     * 内容
     */
    private String content;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户头像
     */
    private String userAvatar;

    /**
     * 等级勋章名称
     */
    private String rankMedalName;

    /**
     * 勋章等级
     */
    private String rankMedalLevel;

    /**
     * 颜色
     */
    private String color;

    /**
     * 回复条数
     */
    private Long replyCount;

    /**
     * 商家用户ID (用于区分是否为商家回复问答)
     */
    private Long userMerchantId;

    /**
     * 商家名称
     */
    private String merchantUserName;

    /**
     * 商家头像
     */
    private String merchantUserAvatar;

    /**
     * 最后一次
     */
    private String lastContent;

    /**
     * 子集评论
     */
    private IssueResponse issueResponse;
}
