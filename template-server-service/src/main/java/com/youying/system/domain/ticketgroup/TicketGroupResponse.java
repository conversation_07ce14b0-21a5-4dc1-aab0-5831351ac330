package com.youying.system.domain.ticketgroup;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-01-08
 */
@Data
public class TicketGroupResponse {
    /**
     * 分组图片ID
     */
    private Long id;

    /**
     * 分组ID
     */
    private Long ticketGroupId;

    /**
     * 电子票图片
     */
    private String image;

    /**
     * 剧目ID
     */
    private Long repertoireId;

    /**
     * 剧目数量
     */
    private Integer number;

    /**
     * 分组类型名称
     */
    private String ticketGroupName;

    /**
     * 电子票ID
     */
    private Long userReceivingRecordsId;

    /**
     * 类型 0：系统 1：用户
     */
    private Integer type;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 封面图
     */
    private String coverPicture;

    /**
     * 电子票图片
     */
    private List<String> electronicTicketImageList;
}
