package com.youying.system.domain.userreceivingrecords;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.youying.common.core.domain.entity.UserReceivingRecordsText;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/5/12
 */
@Data
public class UserReceivingRecordsResponse implements Serializable {
    private Long id;

    /**
     * 藏品组合ID
     */
    private Long portfolioInfoId;

    /**
     * 编号
     */
    private String no;

    /**
     * 藏品编号
     */
    private String collectionNo;

    /**
     * 区块链发放序列ID
     */
    private String sendId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 剧场ID
     */
    private Long theaterId;

    /**
     * 剧目ID
     */
    private Long repertoireId;

    /**
     * 剧场
     */
    private String theaterName;

    /**
     * 剧目
     */
    private String repertoireName;

    /**
     * 纪念徽章名称
     */
    private String souvenirBadgeName;

    /**
     * 1：电子票、2：数字头像、3：纪念徽章、4：等级勋章 ID
     */
    private Long relationId;

    /**
     * 徽章类型（1：电子票、2：数字头像、3：纪念徽章、4：等级勋章）
     */
    private Integer badgeType;

    /**
     * 藏品路径
     */
    private String image;

    /**
     * 模型
     */
    private String model;

    /**
     * 座位号
     */
    private String seatNumber;

    /**
     * 消费金额
     */
    private String amount;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 剧目标签
     */
    private String repertoireLabel;

    /**
     * 演出时间
     */
    private String time;

    /**
     * 城市
     */
    private String cityName;

    /**
     * 佩戴ID
     */
    private Long adorn;

    /**
     * 佩戴图像
     */
    private String adornImage;

    /**
     * 剧场二维码
     */
    private String qrCode;

    /**
     * 纪念徽章发行数量
     */
    private Integer souvenirBadgeIssuedQuantity;

    /**
     * 电子票行数量
     */
    private Integer repertoireTicketIssuedQuantity;

    /**
     * 电子票背面图
     */
    private String repertoireTicketUrl;

    /**
     * 数字头像行数量
     */
    private Integer digitalAvatarIssuedQuantity;

    /**
     * 等级徽章名称
     */
    private String rankMedalName;

    /**
     * 等级徽章级别
     */
    private String rankMedalLevel;

    /**
     * 等级徽章颜色
     */
    private String rankMedalColor;

    /**
     * 等级徽章消费金额
     */
    private String expensePrice;

    /**
     * 发行方
     */
    private String issuerName;

    /**
     * 发行数量
     */
    private Integer issuedQuantity;

    /**
     * 等级徽章消费次数
     */
    private Integer expenseNumber;

    /**
     * 纪念徽章封面图
     */
    private String coverPicture;

    /**
     * 领取人数
     */
    private Long getNumber;

    /**
     * 藏品组合ID
     */
    private Long portfolioId;

    /**
     * 升级时间
     */
    private String upgradeTime;

    /**
     * 升级状态（0：待升级，1：已升级）
     */
    private Integer upgradeStatus;

    /**
     * 金额
     */
    private String price;

    /**
     * 升级藏品路径
     */
    private String upgradeImage;

    /**
     * 数字藏品组合名称
     */
    private String portfolioName;

    /**
     * 免责声明
     */
    private String statement;

    /**
     * 组合介绍
     */
    private String introduction;

    /**
     * 电子票封面版式正面
     */
    private String coverFront;

    /**
     * 电子票封面版式反面
     */
    private String coverReverse;

    /**
     * 藏品组合标识
     */
    private String portfolioNo;

    /**
     * 升级数量
     */
    private String upgradeCount;

    /**
     * 叠加处理图片
     */
    private String overlayImage;

    /**
     * 叠加处理图片
     */
    private String repertoireCoverPicture;

    /**
     * 升级反面票
     */
    private String upgradeCoverReverse;

    /**
     * 总金额
     */
    private BigDecimal sumPrice;

    /**
     * 演员信息
     */
    private String actorInformation;

    /**
     * 分组内数字藏品信息
     */
    private List<UserReceivingRecordsResponse> userReceivingRecordsList = new ArrayList<>();

    /**
     * 电子票演员信息
     */
    private List<UserReceivingRecordsText> userReceivingRecordsTextList = new ArrayList<>();
}
