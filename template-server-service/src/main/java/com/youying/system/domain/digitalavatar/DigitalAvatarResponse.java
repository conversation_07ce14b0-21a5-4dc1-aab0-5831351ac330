package com.youying.system.domain.digitalavatar;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.youying.common.core.domain.entity.DigitalAvatarImage;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-05-24
 */
@Data
public class DigitalAvatarResponse {
    private Long id;

    /**
     * 批次
     */
    private Integer batch;

    /**
     * 剧场ID
     */
    private Long theaterId;

    /**
     * 剧目ID
     */
    private Long repertoireId;

    /**
     * 剧场
     */
    private String theaterName;

    /**
     * 剧目
     */
    private String repertoireName;

    /**
     * 剧目封面图
     */
    private String repertoireCoverPicture;

    /**
     * 剧场封面图
     */
    private String theaterCoverPicture;

    /**
     * 发行时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 发行方
     */
    private String issuerName;

    /**
     * 发行数量
     */
    private Integer issuedQuantity;

    /**
     * 审核（0待审核，1驳回，2通过）
     */
    private Integer audit;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 电子头像
     */
    private List<DigitalAvatarImage> digitalAvatarImageList;

    /**
     * 藏品组合ID
     */
    private Long portfolioId;

    /**
     * 藏品组合
     */
    private String portfolioNo;

    /**
     * 藏品组合名称
     */
    private String portfolioName;

    /**
     * 普通电子票图片
     */
    private String commonImage;

    /**
     * 升级状态（0：待升级，1：已升级）
     */
    private Integer upgradeStatus;

    /**
     * 升级藏品路径
     */
    private String upgradeImage;

    /**
     * 藏品路径
     */
    private String image;

    /**
     * 销售状态 0：已售罄
     */
    private Integer soldOut;

    /**
     * 叠加处理图片(展示图)
     */
    private String overlayImage;
}
