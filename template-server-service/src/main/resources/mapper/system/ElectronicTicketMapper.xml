<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.ElectronicTicketMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.ElectronicTicket">
        <id column="id" property="id"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="theater_id" property="theaterId"/>
        <result column="front" property="front"/>
        <result column="reverse" property="reverse"/>
        <result column="issued_quantity" property="issuedQuantity"/>
        <result column="issuer_name" property="issuerName"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="audit" property="audit"/>
        <result column="status" property="status"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, merchant_id, repertoire_id, theater_id, front, `reverse`, issued_quantity, issuer_name, start_time,
        end_time, audit, `status`, create_by, create_time, update_by, update_time
    </sql>

</mapper>
