<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.UserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.User">
        <id column="id" property="id"/>
        <result column="no" property="no"/>
        <result column="name" property="name"/>
        <result column="avatar" property="avatar"/>
        <result column="sex" property="sex"/>
        <result column="phone" property="phone"/>
        <result column="personalized_signature" property="personalizedSignature"/>
        <result column="password" property="password"/>
        <result column="rank_medal_info_id" property="rankMedalInfoId"/>
        <result column="amount" property="amount"/>
        <result column="sum_look" property="sumLook"/>
        <result column="status" property="status"/>
        <result column="deleted" property="deleted"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , `no`, `name`, avatar, sex, phone, personalized_signature, `password`, rank_medal_info_id, amount, sum_look, `status`, deleted, create_by, create_time, update_by, update_time
    </sql>

    <select id="findUserByPhone" resultType="com.youying.common.core.domain.entity.User">
        SELECT
            id,
            fist_login,
            `no`,
            `name`,
            avatar,
            sex,
            phone,
            personalized_signature,
            `password`,
            speak_status,
            rank_medal_info_id,
            amount,
            sum_look,
            `status`,
            deleted,
            open_id
        FROM
            t_user
        WHERE
            phone = #{phone}
            AND deleted = 1
        LIMIT 1
    </select>

    <select id="findUserByOpenId" resultType="com.youying.common.core.domain.entity.User">
        SELECT
            id,
            fist_login,
            `no`,
            `name`,
            avatar,
            sex,
            phone,
            personalized_signature,
            `password`,
            speak_status,
            rank_medal_info_id,
            amount,
            sum_look,
            `status`,
            deleted,
            open_id
        FROM
            t_user
        WHERE
            open_id = #{openId}
            AND deleted = 1
        LIMIT 1
    </select>

</mapper>
