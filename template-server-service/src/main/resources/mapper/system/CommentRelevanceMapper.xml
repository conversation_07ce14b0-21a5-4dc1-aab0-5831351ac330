<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.CommentRelevanceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.CommentRelevance">
        <id column="id" property="id" />
        <result column="comment_theater_id" property="commentTheaterId" />
        <result column="comment_repertoire_id" property="commentRepertoireId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, comment_theater_id, comment_repertoire_id
    </sql>

</mapper>
