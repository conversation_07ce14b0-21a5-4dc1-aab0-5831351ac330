<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.UserDigitalAvatarMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.UserDigitalAvatar">
        <id column="id" property="id"/>
        <result column="portfolio_id" property="portfolioId"/>
        <result column="user_id" property="userId"/>
        <result column="user_receiving_records_id" property="userReceivingRecordsId"/>
        <result column="order_id" property="orderId"/>
        <result column="status" property="status"/>
        <result column="image" property="image"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , portfolio_id, user_id, user_receiving_records_id, order_id, `status`, image
    </sql>

    <update id="updateUserDigitalAvatar">
        update t_user_digital_avatar
        <set>
            <if test="userDigitalAvatar.userId != null">user_id = #{userDigitalAvatar.userId},</if>
            <if test="userDigitalAvatar.userReceivingRecordsId != null">user_receiving_records_id = #{userDigitalAvatar.userReceivingRecordsId},</if>
            <if test="userDigitalAvatar.orderId != null">order_id = #{userDigitalAvatar.orderId},</if>
            <if test="userDigitalAvatar.status != null">`status` = #{userDigitalAvatar.status},</if>
        </set>
        where id = #{userDigitalAvatar.id}
    </update>

</mapper>
