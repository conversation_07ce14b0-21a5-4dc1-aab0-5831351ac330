<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.MerchantUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.MerchantUser">
        <id column="id" property="id"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="role_name" property="roleName"/>
        <result column="merchant_category" property="merchantCategory"/>
        <result column="type" property="type"/>
        <result column="name" property="name"/>
        <result column="avatar" property="avatar"/>
        <result column="sex" property="sex"/>
        <result column="phone" property="phone"/>
        <result column="password" property="password"/>
        <result column="status" property="status"/>
        <result column="deleted" property="deleted"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , merchant_id, role_name, merchant_category, `type`, `name`, avatar, sex, phone, `password`, `status`, deleted, create_by, create_time, update_by, update_time
    </sql>

</mapper>
