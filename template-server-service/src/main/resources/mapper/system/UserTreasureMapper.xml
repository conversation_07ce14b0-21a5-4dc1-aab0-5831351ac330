<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.UserTreasureMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.UserTreasure">
        <result column="user_id" property="userId"/>
        <result column="theater_id" property="theaterId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        user_id
        , theater_id, repertoire_id, create_time
    </sql>

    <select id="listByPage" resultType="com.youying.system.domain.usertreasure.UserTreasureResponse">
        SELECT
            ut.id,
            r.`name` AS repertoireName,
            t.`name` AS theaterName,
            r.cover_picture AS repertoireCoverPicture,
            t.cover_picture AS theaterCoverPicture,
            ut.create_time,
            ut.repertoire_id,
            ut.theater_id,
            (
            SELECT
                MIN( rid.start_time ) AS startTime
            FROM
                t_repertoire_info_detail AS rid
                LEFT JOIN t_repertoire_info AS ri ON ri.id = rid.repertoire_info_id
            WHERE
                ri.`status` = 1
                AND ri.audit = 2
                AND ri.repertoire_id = ut.repertoire_id
            ) AS startTime,
            (
            SELECT
                MAX( rid.end_time ) AS endTime
            FROM
                t_repertoire_info_detail AS rid
                LEFT JOIN t_repertoire_info AS ri ON ri.id = rid.repertoire_info_id
            WHERE
                ri.`status` = 1
                AND ri.audit = 2
                AND ri.repertoire_id = ut.repertoire_id
            ) AS endTime,
            ( SELECT create_time FROM t_dynamic WHERE ( ut.repertoire_id = repertoire_id OR ut.theater_id = theater_id ) ORDER BY create_time DESC LIMIT 1 ) AS last_update_time
        FROM
            t_user_treasure AS ut
            LEFT JOIN t_repertoire AS r ON r.id = ut.repertoire_id
            LEFT JOIN t_theater AS t ON t.id = ut.theater_id
        <where>
            ut.user_id = #{userId}
            <if test="keyword != null and keyword != ''">
                AND
                (
                (r.`name` LIKE CONCAT('%',#{keyword},'%'))
                OR
                (t.`name` LIKE CONCAT('%',#{keyword},'%'))
                )
            </if>
        </where>
        ORDER BY ut.create_time DESC
    </select>

</mapper>
