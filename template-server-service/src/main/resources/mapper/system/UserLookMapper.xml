<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.UserLookMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.UserLook">
        <id column="id" property="id"/>
        <result column="no" property="no"/>
        <result column="user_id" property="userId"/>
        <result column="repertoire_name" property="repertoireName"/>
        <result column="theater_name" property="theaterName"/>
        <result column="repertoire_session" property="repertoireSession"/>
        <result column="price" property="price"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , `no`, user_id, repertoire_name, theater_name, repertoire_session, price, create_by, create_time
    </sql>

</mapper>
