<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.MerchantMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.Merchant">
        <id column="id" property="id"/>
        <result column="merchant_name" property="merchantName"/>
        <result column="merchant_category" property="merchantCategory"/>
        <result column="contact_person" property="contactPerson"/>
        <result column="phone" property="phone"/>
        <result column="account" property="account"/>
        <result column="password" property="password"/>
        <result column="address" property="address"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="business_license_front" property="businessLicenseFront"/>
        <result column="business_license_reverse" property="businessLicenseReverse"/>
        <result column="remark" property="remark"/>
        <result column="deleted" property="deleted"/>
        <result column="status" property="status"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , merchant_name, merchant_category, contact_person, phone, `account`, `password`, address, start_time, end_time, business_license_front, business_license_reverse, remark, deleted, `status`, create_by, create_time, update_by, update_time
    </sql>

</mapper>
