<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.UserReceivingRecordsTextMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.UserReceivingRecordsText">
        <id column="id" property="id"/>
        <result column="user_receiving_records_id" property="userReceivingRecordsId"/>
        <result column="actor_information" property="actorInformation"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , user_receiving_records_id, actor_information
    </sql>

</mapper>
