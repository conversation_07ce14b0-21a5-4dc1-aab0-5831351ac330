<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.MessageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.Message">
        <id column="id" property="id"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="theater_id" property="theaterId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="body" property="body"/>
        <result column="sms_notify" property="smsNotify"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , merchant_id, theater_id, repertoire_id, body, sms_notify, create_by, create_time
    </sql>

    <select id="listByPage" resultType="com.youying.system.domain.usermessage.UserMessageResponse">
        SELECT
            umi.id,
            umi.repertoire_id,
            umi.theater_id,
            umi.look_flag,
            umi.body,
            r.`name` AS repertoireName,
            t.`name` AS theaterName,
            r.`cover_picture` AS repertoireCoverPicture,
            t.`cover_picture` AS theaterCoverPicture,
            umi.create_time
        FROM
            t_user_message_info AS umi
            LEFT JOIN t_repertoire AS r ON r.id = umi.repertoire_id
            LEFT JOIN t_theater AS t ON t.id = umi.theater_id
        WHERE
            umi.user_id = #{userId}
            AND message_id > 0
        ORDER BY
            umi.create_time DESC
    </select>

</mapper>
