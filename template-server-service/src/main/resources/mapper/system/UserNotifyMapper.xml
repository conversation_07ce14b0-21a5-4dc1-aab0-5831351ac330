<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.UserNotifyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.UserNotify">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="notify_id" property="notifyId"/>
        <result column="look_flag" property="lookFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , user_id, notify_id, look_flag, create_by, create_time
    </sql>

    <select id="listByPage" resultType="com.youying.system.domain.usernotify.UserNotifyResponse">
        SELECT
            un.id,
            n.title,
            n.body,
            n.push_pass_time AS create_time
        FROM
            t_user_notify AS un
            LEFT JOIN t_notify AS n ON n.id = un.notify_id
        WHERE
            un.user_id = #{userId}
            AND un.`port` = #{port}
        ORDER BY
            n.push_pass_time DESC , n.id
    </select>

    <select id="details" resultType="com.youying.system.domain.usernotify.UserNotifyResponse">
        SELECT
            un.id,
            n.title,
            n.body,
            n.push_pass_time AS create_time
        FROM
            t_user_notify AS un
            LEFT JOIN t_notify AS n ON n.id = un.notify_id
        WHERE
            un.id = #{id}
            AND un.user_id = #{userId}
            AND un.`port` = #{port}
    </select>

</mapper>
