<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.DigitalAvatarImageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.DigitalAvatarImage">
        <id column="id" property="id"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="theater_id" property="theaterId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="digital_avatar_id" property="digitalAvatarId"/>
        <result column="image" property="image"/>
        <result column="group" property="group"/>
        <result column="sort" property="sort"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , merchant_id, theater_id, repertoire_id, digital_avatar_id, image, `group`, sort
    </sql>

</mapper>
