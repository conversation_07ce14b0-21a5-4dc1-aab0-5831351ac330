<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.LeaderboardMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.Leaderboard">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="user_id" property="userId"/>
        <result column="status" property="status"/>
        <result column="sort" property="sort"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , `name`, `type`, user_id, `status`, sort, create_by, create_time, update_by, update_time
    </sql>

    <select id="findLeaderboardCount" resultType="java.lang.Integer">
        SELECT
            COUNT( 1 )
        FROM
            t_leaderboard
        WHERE
            user_id = #{userId}
            AND `type` = #{type}
    </select>

    <select id="findUserLeaderboardList" resultType="leaderboardResponse">
        SELECT
            l.id,
            l.`name`,
            l.type,
            t.`name` AS theaterName,
            r.`name` AS repertoireName,
            r.cover_picture,
            urr.repertoire_id,
            IFNULL( urr.upgrade_image, urr.image ) AS image,
            urr.`time`,
            a.`fullname` AS cityName,
            ul.user_receiving_records_id AS userReceivingRecordsId
        FROM
            t_leaderboard AS l
            LEFT JOIN t_user_leaderboard AS ul ON ul.leaderboard_id = l.id
            AND ul.user_id = #{userId}
            LEFT JOIN t_user_receiving_records AS urr ON urr.id = ul.user_receiving_records_id
            LEFT JOIN t_theater AS t ON t.id = urr.theater_id
            LEFT JOIN t_repertoire AS r ON r.id = urr.repertoire_id
            LEFT JOIN t_area AS a ON a.id = t.city_id
        WHERE
            ( l.type = 0 OR l.user_id = #{userId} )
            AND l.status = '1'
        ORDER BY
            l.type,
            l.sort,
            l.id
    </select>

    <select id="details" resultType="com.youying.system.domain.leaderboard.LeaderboardResponse">
        SELECT
            l.id,
            l.`name`,
            ul.id AS userLeaderboardId,
            ul.user_receiving_records_id,
            IFNULL( urr.upgrade_image, urr.image ) AS image,
            urr.repertoire_id
        FROM
            t_user_leaderboard AS ul
            LEFT JOIN t_leaderboard AS l ON ul.leaderboard_id = l.id
            LEFT JOIN t_user_receiving_records AS urr ON urr.id = ul.user_receiving_records_id
        WHERE
            ul.leaderboard_id = #{leaderboardId}
            AND ul.user_id = #{userId}
        LIMIT 1
    </select>

    <select id="findUserLeaderboardLastTime" resultType="java.util.Date">
        SELECT
            max(ul.update_time)
        FROM
            t_leaderboard AS l
            LEFT JOIN t_user_leaderboard AS ul ON ul.leaderboard_id = l.id
            AND ul.user_id = #{userId}
        WHERE
            ( l.type = 0 OR l.user_id = #{userId} )
            AND l.status = '1'
    </select>

</mapper>
