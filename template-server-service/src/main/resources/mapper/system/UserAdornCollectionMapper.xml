<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.UserAdornCollectionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.UserAdornCollection">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="badge_type" property="badgeType"/>
        <result column="user_receiving_records_id" property="userReceivingRecordsId"/>
        <result column="upgrade_status" property="upgradeStatus"/>
        <result column="adorn_image" property="adornImage"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , user_id, badge_type, user_receiving_records_id, upgrade_status, adorn_image
    </sql>

</mapper>
