<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.DigitalAvatarMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.DigitalAvatar">
        <id column="id" property="id"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="batch" property="batch"/>
        <result column="theater_id" property="theaterId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="issuer_name" property="issuerName"/>
        <result column="issued_quantity" property="issuedQuantity"/>
        <result column="status" property="status"/>
        <result column="audit" property="audit"/>
        <result column="audit_pass_time" property="auditPassTime"/>
        <result column="reasons_rejection" property="reasonsRejection"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , merchant_id, batch, theater_id, repertoire_id, start_time, end_time, issuer_name, issued_quantity, `status`, audit, audit_pass_time, reasons_rejection, create_by, create_time, update_by, update_time
    </sql>

    <select id="findDigitalAvatarById" resultType="com.youying.common.core.domain.entity.DigitalAvatar">
        SELECT
            da.*
        FROM
            t_digital_avatar AS da
            LEFT JOIN t_repertoire AS r ON r.id = da.repertoire_id
            LEFT JOIN t_theater AS t ON t.id = da.theater_id
        WHERE
            da.theater_id = #{theaterId}
            AND da.repertoire_id = #{repertoireId}
            AND da.audit = 2
            AND da.`status` = 1
            AND t.deleted = 1
            AND t.`status` = 1
            AND t.audit = 2
            AND r.deleted = 1
            AND r.`status` = 1
            AND r.audit = 2
    </select>

    <select id="listByPage" resultType="DigitalAvatarResponse">
        SELECT
            da.id,
            p.id AS portfolioId,
            p.`name` AS portfolioName,
            t.`name` AS theaterName,
            t.cover_picture AS theaterCoverPicture,
            r.cover_picture AS repertoireCoverPicture,
            r.`name` AS repertoireName,
            da.common_image,
            da.overlay_image,
            p.issued_quantity,
            p.start_time,
            p.end_time,
            p.sold_out
        FROM
            t_portfolio AS p
            LEFT JOIN t_repertoire_ticket AS rt ON rt.id = p.repertoire_ticket_id
            LEFT JOIN t_digital_avatar AS da ON da.id = p.digital_avatar_id
            LEFT JOIN t_repertoire AS r ON r.id = p.repertoire_id
            LEFT JOIN t_theater AS t ON t.id = p.theater_id
        <where>
            p.`status` = 1
            AND p.audit = 2
            AND da.max_quantity > 0
            AND rt.cover_front IS NOT NULL
            <if test="keyword != null and keyword != ''">
                AND
                (
                (r.`name` LIKE CONCAT('%',#{keyword},'%'))
                OR
                (p.`name` LIKE CONCAT('%',#{keyword},'%'))
                )
            </if>
        </where>
    </select>

    <select id="getDigitalAvatarInfo" resultType="DigitalAvatarResponse">
        SELECT
            da.id,
            p.id AS portfolioId,
            p.`name` AS portfolioName,
            t.`name` AS theaterName,
            t.cover_picture AS theaterCoverPicture,
            r.cover_picture AS repertoireCoverPicture,
            r.`name` AS repertoireName,
            da.common_image,
            da.overlay_image,
            p.issued_quantity,
            p.start_time,
            p.end_time,
            urr.upgrade_status,
            urr.upgrade_image,
            urr.image,
            urr.portfolio_no,
            urr.id AS userReceivingRecordsId
        FROM
            t_portfolio AS p
            LEFT JOIN t_digital_avatar AS da ON da.id = p.digital_avatar_id
            LEFT JOIN t_repertoire AS r ON r.id = p.repertoire_id
            LEFT JOIN t_theater AS t ON t.id = p.theater_id
                LEFT JOIN (
                    SELECT
                        urr.id,
                        urr.upgrade_status,
                        urr.upgrade_image,
                        urr.image,
                        urr.portfolio_id,
                        urr.portfolio_no
                    FROM
                        t_user_receiving_records AS urr
                    WHERE
                        urr.portfolio_id = #{portfolioId} AND user_id = #{userId}
                        AND urr.badge_type = 2
                        LIMIT 1
            ) AS urr ON urr.portfolio_id = p.id
        WHERE
            p.id = #{portfolioId}
    </select>

</mapper>
