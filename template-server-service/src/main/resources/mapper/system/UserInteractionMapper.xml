<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.UserInteractionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.UserInteraction">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="reply_user_id" property="replyUserId" />
        <result column="relevance_id" property="relevanceId" />
        <result column="reply_relevance_id" property="replyRelevanceId" />
        <result column="type" property="type" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, reply_user_id, relevance_id, reply_relevance_id, `type`, create_time
    </sql>

    <select id="listByPageByComment"
            resultType="com.youying.system.domain.userinteraction.UserInteractionResponse">
        SELECT
            ui.id,
            u.`name` AS userName,
            u.avatar AS userAvatar,
            u1.`name` AS replyUserName,
            u1.avatar AS replyUserAvatar,
            c.content AS commentContent,
            c1.content AS replyCommentContent,
            ui.create_time,
            ui.reply_relevance_id,
            ui.relevance_id,
            ui.user_id,
            ui.reply_user_id
        FROM
            t_user_interaction AS ui
            LEFT JOIN t_user AS u ON u.id = ui.user_id
            LEFT JOIN t_user AS u1 ON u1.id = ui.reply_user_id
            LEFT JOIN t_comment AS c ON c.id = ui.relevance_id
            AND ui.type = 1
            LEFT JOIN t_comment AS c1 ON c1.id = ui.reply_relevance_id
            AND ui.type = 1
        <where>
            <if test="userId != null">
                AND ui.user_id = #{userId}
            </if>
            <if test="replyUserId != null">
                AND ui.reply_user_id = #{replyUserId}
            </if>
        </where>
        ORDER BY
            ui.create_time DESC,
            ui.id
    </select>

    <select id="listByPageByKudos"
            resultType="com.youying.system.domain.userinteraction.UserInteractionResponse">
        SELECT
            ui.id,
            ui.`type`,
            c.content AS commentContent,
            u.`name` AS userName,
            u.avatar AS userAvatar,
            u1.`name` AS replyUserName,
            u1.avatar AS replyUserAvatar,
            d.body AS dynamicBody,
            r.`name` AS repertoireName,
            r.cover_picture AS repertoireCoverPicture,
            ui.create_time
        FROM
            t_user_interaction AS ui
            LEFT JOIN t_user AS u ON u.id = ui.user_id
            LEFT JOIN t_user AS u1 ON u1.id = ui.reply_user_id
            LEFT JOIN t_comment AS c ON c.id = ui.relevance_id AND ui.type = 2
            LEFT JOIN t_comment AS c1 ON c1.id = ui.reply_relevance_id AND ui.type = 2
            LEFT JOIN t_dynamic AS d ON d.id = ui.relevance_id AND ui.type = 4
            LEFT JOIN t_repertoire AS r ON r.id = ui.repertoire_id
        <where>
            FIND_IN_SET(ui.type,'2,4')
            <if test="userId != null">
                AND ui.user_id = #{userId}
            </if>
            <if test="replyUserId != null">
                AND ui.reply_user_id = #{replyUserId}
            </if>
        </where>
        ORDER BY
            ui.create_time DESC,
            ui.id
    </select>

    <select id="listByPageByIssue"
            resultType="com.youying.system.domain.userinteraction.UserInteractionResponse">
        SELECT
            ui.id,
            u.`name` AS userName,
            u.avatar AS userAvatar,
            u1.`name` AS replyUserName,
            u1.avatar AS replyUserAvatar,
            i.content AS issueContent,
            i1.content AS replyIssueContent,
            ui.create_time ,
            ui.reply_relevance_id,
            ui.relevance_id,
            ui.user_id,
            ui.repertoire_id,
            ui.reply_user_id,
            ( SELECT COUNT( 1 ) FROM t_issue WHERE parent_id = i.id ) AS issueCount,
            r.cover_picture AS repertoireCoverPicture,
            t.cover_picture AS theaterCoverPicture
        FROM
            t_user_interaction AS ui
            LEFT JOIN t_user AS u ON u.id = ui.user_id
            LEFT JOIN t_user AS u1 ON u1.id = ui.reply_user_id
            LEFT JOIN t_issue AS i ON i.id = ui.relevance_id
            LEFT JOIN t_issue AS i1 ON i1.id = ui.reply_relevance_id
            LEFT JOIN t_repertoire AS r ON r.id = i.repertoire_id
            LEFT JOIN t_theater AS t ON t.id = i.theater_id
        <where>
            ui.type = 3
            <if test="userId != null">
                AND ui.user_id = #{userId}
                AND i1.content is not null
            </if>
            <if test="replyUserId != null">
                AND ui.reply_user_id = #{replyUserId}
            </if>
        </where>
        ORDER BY
            ui.create_time DESC,
            ui.id
    </select>

</mapper>
