<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.DigitalAvatarGetMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.DigitalAvatarGet">
        <id column="id" property="id"/>
        <result column="digital_avatar_id" property="digitalAvatarId"/>
        <result column="no" property="no"/>
        <result column="image" property="image"/>
        <result column="user_id" property="userId"/>
        <result column="user_look_id" property="userLookId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , digital_avatar_id, `no`, image, user_id, user_look_id
    </sql>

</mapper>
