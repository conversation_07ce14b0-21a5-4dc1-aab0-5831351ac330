<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.DynamicMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.Dynamic">
        <id column="id" property="id"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="theater_id" property="theaterId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="title" property="title"/>
        <result column="body" property="body"/>
        <result column="cover_image" property="coverImage"/>
        <result column="images" property="images"/>
        <result column="status" property="status"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , merchant_id, theater_id, repertoire_id, title, body, cover_image, images, `status`, create_by, create_time, update_by, update_time
    </sql>

    <select id="listByPage" resultType="com.youying.system.domain.dynamic.DynamicResponse">
        SELECT
            d.id,
            d.title,
            d.body,
            d.cover_image,
            d.images,
            d.create_time,
            ( SELECT COUNT( 1 ) FROM t_dynamic_kudos WHERE dynamic_id = d.id AND user_id = #{userId} ) AS fansFlag,
            ( SELECT COUNT(1) FROM t_dynamic_kudos WHERE dynamic_id = d.id AND `type` = 1 ) AS likeCount,
            ( SELECT COUNT(1) FROM t_dynamic_kudos WHERE dynamic_id = d.id AND `type` = 0 ) AS dislikeCount,
            ( SELECT `type` FROM t_dynamic_kudos WHERE dynamic_id = d.id AND user_id = #{userId} ) AS kudosStatus
        FROM
            t_dynamic AS d
        <where>
            d.`status` = 1
            <if test="repertoireId != null">
                AND d.repertoire_id = #{repertoireId}
            </if>
            <if test="theaterId != null">
                AND d.theater_id = #{theaterId}
            </if>
        </where>
            ORDER BY create_time DESC
    </select>

    <select id="details" resultType="com.youying.system.domain.dynamic.DynamicResponse">
        SELECT
            d.id,
            d.title,
            d.body,
            d.cover_image,
            d.images,
            d.create_time,
            r.`name` AS repertoireName,
            t.`name` AS theaterName ,
            r.cover_picture AS repertoireCoverPicture,
            t.cover_picture AS theaterCoverPicture,
            ( SELECT COUNT(1) FROM t_dynamic_kudos WHERE dynamic_id = d.id AND `type` = 1 ) AS likeCount,
            ( SELECT COUNT(1) FROM t_dynamic_kudos WHERE dynamic_id = d.id AND `type` = 0 ) AS dislikeCount,
            ( SELECT `type` FROM t_dynamic_kudos WHERE dynamic_id = d.id AND user_id = #{userId} ) AS kudosStatus
        FROM
            t_dynamic AS d
            LEFT JOIN t_repertoire AS r ON r.id = d.repertoire_id
            LEFT JOIN t_theater AS t ON t.id = d.theater_id
        WHERE
            d.id = #{id}
            AND d.`status` = 1
    </select>

</mapper>
