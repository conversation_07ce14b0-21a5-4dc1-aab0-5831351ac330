<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.UserTicketGroupMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.UserTicketGroup">
        <id column="id" property="id"/>
        <result column="ticket_group_id" property="ticketGroupId"/>
        <result column="user_id" property="userId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="type" property="type"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , ticket_group_id, user_id, repertoire_id, `type`, create_by, create_time, update_by, update_time
    </sql>

    <select id="findTicketGroupByUserId" resultType="ticketGroupResponse">
        SELECT
            urg.id,
            IFNULL(urr.upgrade_image,urr.image) AS image,
            urg.user_receiving_records_id,
            urr.repertoire_id
        FROM
            t_user_ticket_group AS urg
            LEFT JOIN t_user_receiving_records AS urr ON urr.id = urg.user_receiving_records_id
        <where>
            urg.ticket_group_id = #{ticketGroupId}
            AND urg.user_id = #{userId}
        </where>
        ORDER BY urr.create_time DESC
    </select>

</mapper>
