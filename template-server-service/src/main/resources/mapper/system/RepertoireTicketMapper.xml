<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.RepertoireTicketMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.RepertoireTicket">
        <id column="id" property="id"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="batch" property="batch"/>
        <result column="theater_id" property="theaterId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="cover_front" property="coverFront"/>
        <result column="cover_reverse" property="coverReverse"/>
        <result column="issuer_name" property="issuerName"/>
        <result column="issued_quantity" property="issuedQuantity"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="status" property="status"/>
        <result column="audit" property="audit"/>
        <result column="audit_pass_time" property="auditPassTime"/>
        <result column="reasons_rejection" property="reasonsRejection"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , merchant_id, batch, theater_id, repertoire_id, cover_front, cover_reverse, issuer_name, issued_quantity, start_time, end_time, `status`, audit, audit_pass_time, reasons_rejection, create_by, create_time, update_by, update_time
    </sql>

    <select id="findTicketByRepertoireId" resultType="com.youying.common.core.domain.entity.RepertoireTicket">
        SELECT
            rt.*
        FROM
            t_repertoire_ticket AS rt
            LEFT JOIN t_theater AS t ON t.id = rt.theater_id
            LEFT JOIN t_repertoire AS r ON r.id = rt.repertoire_id
        WHERE
            rt.theater_id = #{theaterId}
            AND rt.repertoire_id = #{repertoireId}
            AND rt.audit = 2
            AND rt.`status` = 1
            AND t.deleted = 1
            AND t.`status` = 1
            AND t.audit = 2
            AND r.deleted = 1
            AND r.`status` = 1
            AND r.audit = 2
    </select>

    <select id="findRepertoireTicketInfo"
            resultType="com.youying.system.domain.repertoireticket.RepertoireTicketResponse">
        SELECT
            rt.id,
            rt.cover_front,
            rt.cover_reverse,
            rt.`status`,
            rt.issued_quantity,
            rt.start_time,
            rt.end_time,
            rt.issuer_name,
            urr.collection_no,
            urr.`send_id`,
            urr.create_time,
            r.`name` AS repertoireName
        FROM
            t_repertoire_ticket AS rt
            LEFT JOIN t_repertoire AS r ON r.id = rt.repertoire_id
            LEFT JOIN t_user_receiving_records as urr on urr.id = #{relationId}
        WHERE
            rt.id = #{id}
        GROUP BY rt.id
    </select>

    <select id="listByPage" resultType="RepertoireTicketResponse">
        SELECT
            rt.id,
            p.id AS portfolioId,
            p.`name` AS portfolioName,
            t.`name` AS theaterName,
            t.cover_picture AS theaterCoverPicture,
            r.cover_picture AS repertoireCoverPicture,
            r.`name` AS repertoireName,
            rt.common_image,
            rt.cover_front,
            rt.cover_reverse,
            p.issued_quantity,
            p.start_time,
            p.end_time,
            p.sold_out
        FROM
            t_portfolio AS p
            LEFT JOIN t_repertoire_ticket AS rt ON rt.id = p.repertoire_ticket_id
            LEFT JOIN t_repertoire AS r ON r.id = p.repertoire_id
            LEFT JOIN t_theater AS t ON t.id = p.theater_id
        <where>
            p.`status` = 1
            AND p.audit = 2
            AND p.deleted = 1
            AND rt.cover_front IS NOT NULL AND rt.cover_front != ''
            <if test="keyword != null and keyword != ''">
                AND
                (
                (r.`name` LIKE CONCAT('%',#{keyword},'%'))
                OR
                (p.`name` LIKE CONCAT('%',#{keyword},'%'))
                )
            </if>
        </where>
    </select>

    <select id="getRepertoireTicketInfo" resultType="RepertoireTicketResponse">
        SELECT
            rt.id,
            p.id AS portfolioId,
            p.`name` AS portfolioName,
            t.`name` AS theaterName,
            t.cover_picture AS theaterCoverPicture,
            r.cover_picture AS repertoireCoverPicture,
            r.`name` AS repertoireName,
            rt.common_image,
            rt.cover_front,
            rt.cover_reverse,
            p.issued_quantity,
            p.start_time,
            p.end_time,
            urr.upgrade_status,
            urr.id AS userReceivingRecordsId,
            urr.portfolio_no
        FROM
            t_portfolio AS p
            LEFT JOIN t_repertoire_ticket AS rt ON rt.id = p.repertoire_ticket_id
            LEFT JOIN t_repertoire AS r ON r.id = p.repertoire_id
            LEFT JOIN t_theater AS t ON t.id = p.theater_id
            LEFT JOIN (
                SELECT
                    urr.id,
                    urr.upgrade_status,
                    urr.upgrade_image,
                    urr.image,
                    urr.portfolio_id,
                    urr.portfolio_no
                FROM
                    t_user_receiving_records AS urr
                WHERE
                    urr.portfolio_id = #{portfolioId}
                    AND user_id = #{userId}
                    AND urr.badge_type = 1
                    LIMIT 1
            ) AS urr ON urr.portfolio_id = p.id
        WHERE
            p.id = #{portfolioId}
    </select>

</mapper>
