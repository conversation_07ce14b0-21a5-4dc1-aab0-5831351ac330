<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.RankMedalMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.RankMedal">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="theater_id" property="theaterId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="audit" property="audit"/>
        <result column="audit_pass_time" property="auditPassTime"/>
        <result column="reasons_rejection" property="reasonsRejection"/>
        <result column="status" property="status"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , `name`, merchant_id, theater_id, repertoire_id, audit, audit_pass_time, reasons_rejection, deleted, `status`, create_by, create_time, update_by, update_time
    </sql>

    <select id="findRankMedalCanHave" resultType="com.youying.common.core.domain.entity.RankMedalInfo">
        SELECT
            rmi.*
        FROM
            t_rank_medal_info AS rmi
            LEFT JOIN t_rank_medal as rm on rm.id = rmi.rank_medal_id
        <where>
            <if test="repertoireId != null">
                AND rm.repertoire_id = #{repertoireId}
                AND rm.audit = 2
                AND rm.`status` = 1
            </if>
            <if test="theaterId != null">
                AND rm.theater_id = #{theaterId}
                AND rm.audit = 2
                AND rm.`status` = 1
            </if>
            AND rmi.expense_number &lt;= #{userConsumptionCount}
            AND rmi.expense_price &lt;= #{amount}
            AND ( SELECT COUNT( 1 ) FROM t_user_push_collection AS upc WHERE upc.rank_medal_info_id = rmi.id AND upc.user_id = #{userId} ) = 0
        </where>
    </select>

</mapper>
