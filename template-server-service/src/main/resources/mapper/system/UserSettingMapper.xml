<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.UserSettingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.UserSetting">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="comment_notify" property="commentNotify"/>
        <result column="well_notify" property="wellNotify"/>
        <result column="issue_notify" property="issueNotify"/>
        <result column="group_message_notify" property="groupMessageNotify"/>
        <result column="message_notify" property="messageNotify"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , user_id, comment_notify, well_notify, issue_notify, group_message_notify, message_notify
    </sql>

</mapper>
