<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.SouvenirBadgeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.SouvenirBadge">
        <id column="id" property="id"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="name" property="name"/>
        <result column="theater_id" property="theaterId"/>
        <result column="model" property="model"/>
        <result column="batch" property="batch"/>
        <result column="issuer_name" property="issuerName"/>
        <result column="issued_quantity" property="issuedQuantity"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="introduction" property="introduction"/>
        <result column="sms_notify" property="smsNotify"/>
        <result column="status" property="status"/>
        <result column="audit" property="audit"/>
        <result column="audit_pass_time" property="auditPassTime"/>
        <result column="reasons_rejection" property="reasonsRejection"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , merchant_id, `name`, theater_id, model, batch, issuer_name, issued_quantity, start_time, end_time, introduction, sms_notify, `status`, audit, audit_pass_time, reasons_rejection, create_by, create_time, update_by, update_time
    </sql>

    <select id="listByPage" resultType="com.youying.system.domain.souvenirbadge.SouvenirBadgeResponse">
        SELECT
            sb.id,
            sb.`name`,
            sb.cover_picture,
            sb.`status`,
            sb.issuer_name,
            sb.issued_quantity,
            sb.start_time,
            sb.end_time,
            sb.sold_out,
            t.`name` AS theaterName,
            t.cover_picture AS theaterCoverPicture,
            sb.create_time,
            ( SELECT id FROM t_user_receiving_records WHERE relation_id = sb.id AND user_id = #{userId} AND badge_type = 3 LIMIT 1 ) AS relationId
        FROM
            t_souvenir_badge AS sb
            LEFT JOIN t_theater AS t ON t.id = sb.theater_id
        <where>
            sb.audit = 2
            AND sb.`status` = 1
            <if test="keyword != null and keyword != ''">
                AND
                (
                (sb.`name` LIKE CONCAT('%',#{keyword},'%'))
                OR
                (t.`name` LIKE CONCAT('%',#{keyword},'%'))
                )
            </if>
        </where>
            ORDER BY sb.create_time DESC , sb.id
    </select>

    <select id="details" resultType="com.youying.system.domain.souvenirbadge.SouvenirBadgeResponse">
        SELECT
            sb.id,
            sb.`name`,
            sb.model,
            sb.cover_picture,
            sb.`status`,
            sb.issued_quantity,
            sb.start_time,
            sb.end_time,
            sb.issuer_name,
            t.`name` AS theaterName,
            urr.collection_no,
            urr.`send_id`,
            urr.create_time,
            r.`name` AS repertoireName,
            sbr.look_number,
            rid.start_time AS repertoireStartTime,
            rid.end_time AS repertoireEndTime,
            sbr.start_time AS lookStartTime,
            sbr.end_time AS lookEndTime,
            sbr.time_look_number
        FROM
            t_souvenir_badge AS sb
            LEFT JOIN t_theater AS t ON t.id = sb.theater_id
            LEFT JOIN t_souvenir_badge_require AS sbr ON sbr.souvenir_badge_id = sb.id
            LEFT JOIN t_repertoire AS r ON r.id = sbr.repertoire_id
            LEFT JOIN t_repertoire_info_detail AS rid ON rid.id = sbr.repertoire_info_detail_id
            LEFT JOIN t_user_receiving_records as urr on urr.id = #{relationId}
        WHERE
            sb.id = #{id}
        GROUP BY sb.id, sbr.id
    </select>

    <select id="findUserConsumption" resultType="com.youying.common.core.domain.entity.SouvenirBadge">
        SELECT
            *
        FROM
            t_souvenir_badge
        WHERE
            theater_id = #{theaterId}
            AND `status` = 1
            AND audit = 2
            AND sold_out = 0
            AND issued_quantity > 0
            AND CURRENT_DATE BETWEEN start_time AND end_time
    </select>

</mapper>
