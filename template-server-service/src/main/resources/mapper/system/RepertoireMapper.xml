<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.RepertoireMapper">

    <resultMap id="repertoireInfoMap" type="RepertoireResponse">
        <id column="id" property="id"/>
        <collection property="repertoireInfoList" select="repertoireInfoQuery" column="id" ofType="repertoireInfoResponse" />
        <collection property="repertoireActorList" select="repertoireActorQuery" column="id" ofType="repertoireActor" />
        <collection property="repertoireCreativeTeamList" select="repertoireCreativeTeamQuery" column="id" ofType="repertoireCreativeTeam" />
    </resultMap>

    <resultMap id="repertoireInfoDetailsMap" type="repertoireInfoResponse">
        <id column="id" property="id"/>
        <collection property="repertoireInfoDetailList" select="repertoireInfoDetailQuery" column="id" ofType="repertoireInfoDetail" />
    </resultMap>

    <select id="repertoireInfoQuery" resultMap="repertoireInfoDetailsMap">
        SELECT
            ri.id,
            t.`name` AS theaterName,
            ri.theater_id,
            MAX( rid.end_time ) AS endTime,
            MIN( rid.start_time ) AS startTime
        FROM
            t_repertoire_info AS ri
            LEFT JOIN t_theater AS t ON t.id = ri.theater_id
            LEFT JOIN t_repertoire_info_detail AS rid ON rid.repertoire_info_id = ri.id
        WHERE
            ri.repertoire_id = #{id}
            AND ri.`status` = 1
            AND t.deleted = 1
            AND t.`status` = 1
            AND t.audit = 2
        GROUP BY
            ri.id
    </select>

    <select id="repertoireActorQuery" resultType="repertoireActor">
        SELECT
            id,
            actor_type,
            picture,
            role_name,
            actor_name,
            group_performance_name
        FROM
            t_repertoire_actor AS rc
        WHERE
            rc.repertoire_id = #{id}
    </select>

    <select id="repertoireCreativeTeamQuery" resultType="repertoireCreativeTeam">
        SELECT
            id,
            repertoire_id,
            `name`,
            function_name
        FROM
            t_repertoire_creative_team AS rct
        WHERE
            rct.repertoire_id = #{id}
    </select>

    <select id="repertoireInfoDetailQuery" resultType="repertoireInfoDetail">
        SELECT
            rid.id,
            rid.start_time,
            rid.end_time
        FROM
            t_repertoire_info_detail AS rid
        WHERE
            rid.repertoire_info_id = #{id}
    </select>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , `name`, repertoire_type_id, cover_picture, pictures, introduction, rating, recommend, good_rating_rate, focus_number, audit, audit_pass_time, reasons_rejection, merchant_id, deleted, `status`, create_by, create_time, update_by, update_time
    </sql>

    <select id="listByIndex" resultType="RepertoireResponse">
        SELECT
            id,
            `name`,
            merchant_id,
            cover_picture,
            pictures,
            rating ,
            good_rating_rate ,
            create_time,
            focus_number,
            FLOOR(((likeCount / (likeCount + dislikeCount)) * 100)) AS like_ratio,
            fansFlag,
            recommend,
            last_time,
            ( SELECT COUNT( 1 ) FROM t_repertoire_info WHERE theater_id IN (SELECT id FROM t_theater WHERE city_id = #{city} OR area_id = #{city}  ) AND repertoire_id = r.id AND `status` = '1' ) AS city_count
        FROM
            (SELECT
                r.id,
                r.`name`,
                r.merchant_id,
                r.cover_picture,
                r.pictures,
                r.rating ,
                r.good_rating_rate ,
                r.create_time,
                r.focus_number,
                r.recommend ,
                ( SELECT MAX( create_time ) FROM t_comment AS c WHERE c.repertoire_id = r.id AND c.parent_id = '0' AND c.deleted = '1' AND c.`status` = '1' AND visible = '0' LIMIT 1 ) AS last_time,
                ( SELECT COUNT( 1 ) FROM t_kudos AS k WHERE k.repertoire_id = r.id AND k.type = 1 ) AS likeCount,
                ( SELECT COUNT( 1 ) FROM t_kudos AS k WHERE k.repertoire_id = r.id AND k.type = 0 ) AS dislikeCount,
                ( SELECT COUNT( 1 ) FROM t_user_treasure WHERE repertoire_id = r.id AND user_id = #{userId} ) AS fansFlag
            FROM
                t_repertoire AS r
            WHERE
                r.deleted = '1'
                AND r.audit = '2'
                AND r.`status` = '1'
            ) AS r
    </select>

    <select id="listByPage" resultType="com.youying.system.domain.repertoire.RepertoireResponse">
        SELECT
            id,
            merchant_id,
            `name`,
            cover_picture,
            pictures,
            rating ,
            good_rating_rate ,
            create_time,
            focus_number,
            IFNULL(FLOOR(((likeCount / (likeCount + dislikeCount)) * 100)),0) AS like_ratio,
            fansFlag,
            recommend,
            last_time,
            ( SELECT COUNT( 1 ) FROM t_repertoire_info WHERE theater_id IN (SELECT id FROM t_theater WHERE city_id = #{city} OR area_id = #{city}  ) AND repertoire_id = r.id AND `status` = '1' ) AS city_count
        FROM
            (SELECT
                r.id,
                r.merchant_id,
                r.`name`,
                r.cover_picture,
                r.pictures,
                r.rating ,
                r.good_rating_rate ,
                r.create_time,
                r.focus_number,
                r.recommend,
                ( SELECT MAX( create_time ) FROM t_comment AS c WHERE c.repertoire_id = r.id AND c.parent_id = '0' AND c.deleted = '1' AND c.`status` = '1' AND visible = '0' LIMIT 1 ) AS last_time,
                ( SELECT COUNT( 1 ) FROM t_kudos AS k WHERE k.repertoire_id = r.id AND k.type = 1 ) AS likeCount,
                ( SELECT COUNT( 1 ) FROM t_kudos AS k WHERE k.repertoire_id = r.id AND k.type = 0 ) AS dislikeCount,
                ( SELECT COUNT( 1 ) FROM t_user_treasure WHERE repertoire_id = r.id AND user_id = #{userId} ) AS fansFlag
            FROM
                t_repertoire AS r
            <where>
                r.deleted = '1'
                AND r.audit = '2'
                AND r.`status` = '1'
                <if test="keyword != null and keyword != ''">
                    AND r.`name` LIKE CONCAT('%',#{keyword},'%')
                </if>
            </where>
            ) AS r
    </select>

    <select id="details" resultMap="repertoireInfoMap">
        SELECT
            r.id,
            r.`name`,
            r.rating,
            r.good_rating_rate,
            r.cover_picture,
            r.introduction,
            r.pictures,
            r.merchant_id,
            r.focus_number,
            ( SELECT COUNT( 1 ) FROM t_kudos AS k WHERE k.repertoire_id = r.id AND k.type = 1 ) AS likeCount,
            ( SELECT COUNT( 1 ) FROM t_kudos AS k WHERE k.repertoire_id = r.id AND k.type = 0 ) AS dislikeCount,
            ( SELECT COUNT( 1 ) FROM t_user_treasure WHERE repertoire_id = r.id AND user_id = #{userId} ) AS fansFlag,
            COUNT( DISTINCT c.user_id ) AS commentCount,
            GROUP_CONCAT( DISTINCT rl.`name` ) AS repertoireLabelName
        FROM
            t_repertoire AS r
            LEFT JOIN t_comment AS c ON c.repertoire_id = r.id
            LEFT JOIN t_repertoire_label AS rl ON rl.repertoire_id = r.id
        WHERE
            r.id = #{id}
            AND r.audit = '2'
            AND r.`status` = '1'
        GROUP BY
            r.id
    </select>

    <select id="findRepertoireDisplay" resultType="repertoireDisplayResponse">
        SELECT
            rt.common_image AS repertoireTicketUrl,
            da.common_image AS digitalAvatarUrl,
            DATE ( p.audit_pass_time ) AS `auditPassTime`
        FROM
            t_portfolio AS p
            LEFT JOIN t_repertoire AS r ON r.id = p.repertoire_id
            LEFT JOIN t_repertoire_ticket AS rt ON rt.id = p.repertoire_ticket_id
            LEFT JOIN t_digital_avatar AS da ON da.id = p.digital_avatar_id
        <where>
            p.deleted = '1'
            AND p.repertoire_id = #{repertoireId}
            AND p.look_status = '1'
            AND rt.`status` = '1'
            AND p.audit = '2'
            AND r.`status` = '1'
            AND r.deleted = '1'
        </where>
        ORDER BY
            `auditPassTime` DESC , p.id
    </select>

    

</mapper>
