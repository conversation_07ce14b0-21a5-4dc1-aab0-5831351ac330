<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.SouvenirBadgeRequireMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.SouvenirBadgeRequire">
        <id column="id" property="id"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="theater_id" property="theaterId"/>
        <result column="souvenir_badge_id" property="souvenirBadgeId"/>
        <result column="rank_medal_id" property="rankMedalId"/>
        <result column="repertoire_info_detail_id" property="repertoireInfoDetailId"/>
        <result column="repertoire_info_id" property="repertoireInfoId"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="look_number" property="lookNumber"/>
        <result column="white_list" property="whiteList"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , merchant_id, theater_id, souvenir_badge_id, rank_medal_id, repertoire_info_detail_id, repertoire_info_id, start_time, end_time, look_number, white_list
    </sql>

    <select id="findWhiteList" resultType="java.lang.Long">
        SELECT
            COUNT( 1 )
        FROM
            t_souvenir_badge_require AS sbr
        WHERE
            sbr.id = #{id}
            AND ( SELECT COUNT( 1 ) FROM t_user_receiving_records AS urr WHERE urr.relation_id = sbr.souvenir_badge_id AND urr.badge_type = 3 AND urr.user_id = #{userId} ) = 0
            AND FIND_IN_SET( #{userId} , sbr.white_list )
    </select>

    <select id="findUserConsumptionCount" resultType="java.lang.Long">
        SELECT
            COUNT( 1 )
        FROM
            t_souvenir_badge_require AS sbr
        WHERE
            sbr.id = #{id}
            AND ( SELECT COUNT( 1 ) FROM t_user_receiving_records AS urr WHERE urr.relation_id = sbr.souvenir_badge_id AND urr.badge_type = 3 AND urr.user_id = #{userId} ) = 0
            AND ( SELECT COUNT( 1 ) FROM t_user_push_collection AS upc WHERE upc.souvenir_badge_id = sbr.souvenir_badge_id AND upc.user_id = #{userId} ) = 0
            AND ( sbr.repertoire_info_detail_id = 0 OR sbr.repertoire_info_detail_id = #{repertoireInfoDetailId} )
            AND (
                sbr.look_number = 0
                OR
                (
                    SELECT
                        COUNT( 1 )
                    FROM
                        t_user_receiving_records AS urr
                    WHERE
                        urr.theater_id = #{theaterId}
                        AND urr.repertoire_id = #{repertoireId}
                        AND urr.user_id = #{userId}
                        AND urr.badge_type = 1
                ) >= (sbr.look_number - 1)
            )
            AND (
                sbr.time_look_number = 0
                OR
                (
                    SELECT
                        COUNT( 1 )
                    FROM
                        t_user_receiving_records AS urr
                    WHERE
                        urr.user_id = #{userId}
                        AND urr.badge_type = 1
                        AND ((SELECT start_time FROM t_repertoire_info_detail WHERE id = urr.repertoire_info_detail_id) BETWEEN sbr.start_time AND sbr.end_time)
                        AND urr.theater_id IN (SELECT t.id FROM t_theater AS t WHERE t.merchant_id = #{merchantId})
                ) >= (sbr.time_look_number - 1)
            )
    </select>

</mapper>
