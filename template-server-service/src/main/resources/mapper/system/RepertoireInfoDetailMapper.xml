<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.RepertoireInfoDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.RepertoireInfoDetail">
        <id column="id" property="id"/>
        <result column="repertoire_info_id" property="repertoireInfoId"/>
        <result column="theater_id" property="theaterId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , repertoire_info_id, theater_id, repertoire_id, start_time, end_time
    </sql>

    <select id="findRepertoireInfoDetail"
            resultType="com.youying.common.core.domain.entity.RepertoireInfoDetail">
        SELECT
            rid.*
        FROM
            t_repertoire_info_detail AS rid
            LEFT JOIN t_theater AS t ON t.id = rid.theater_id
            LEFT JOIN t_repertoire AS r ON r.id = rid.repertoire_id
            LEFT JOIN t_repertoire_info AS ri ON ri.id = rid.repertoire_info_id
        WHERE
            ri.`status` = 1
            AND #{repertoireName} LIKE CONCAT( '%',r.`short_name`, '%' )
            AND #{theaterName} LIKE CONCAT('%', t.`name`, '%' )
            AND DATE_FORMAT( rid.start_time, '%Y%m%d%H%i00' ) = #{timeStr}
            LIMIT 1
    </select>

    <select id="findRepertoireInfoDetails"
            resultType="com.youying.common.core.domain.entity.RepertoireInfoDetail">
        SELECT
            rid.*
        FROM
            t_repertoire_info_detail AS rid
            LEFT JOIN t_repertoire_info AS ri ON ri.id = rid.repertoire_info_id
        WHERE
            ri.`status` = 1
            AND rid.repertoire_id = #{repertoireId}
            AND rid.theater_id = #{theaterId}
            AND
            (
                (DATE_FORMAT( rid.start_time, '%Y%c%e%H%i00' ) = #{dateStr})
                OR
                (DATE_FORMAT( rid.start_time, '%Y%m%d%H%i00' ) = #{dateStr})
                OR
                (DATE_FORMAT( rid.start_time, '%Y%m%e%H%i00' ) = #{dateStr})
                OR
                (DATE_FORMAT( rid.start_time, '%Y%c%d%H%i00' ) = #{dateStr})
            )
            LIMIT 1
    </select>

    <select id="findRepertoireInfo" resultType="com.youying.common.core.domain.entity.RepertoireInfoDetail">
        SELECT
            rid.*
        FROM
            t_repertoire_info_detail AS rid
            LEFT JOIN t_repertoire_info AS ri ON ri.id = rid.repertoire_info_id
        <where>
            ri.`status` = 1
            AND rid.repertoire_id = #{repertoireId}
            AND rid.theater_id = #{theaterId}
            AND
            <foreach item="item" index="index" collection="timeList"
                     open="(" separator="OR" close=")" nullable="true">
                (
                (DATE_FORMAT( rid.start_time, '%Y%c%e%H%i' ) = #{item})
                OR
                (DATE_FORMAT( rid.start_time, '%Y%m%d%H%i' ) = #{item})
                OR
                (DATE_FORMAT( rid.start_time, '%Y%m%e%H%i' ) = #{item})
                OR
                (DATE_FORMAT( rid.start_time, '%Y%c%d%H%i' ) = #{item})
                OR
                (DATE_FORMAT( rid.start_time, '%Y%c%e%H%i00' ) = #{item})
                OR
                (DATE_FORMAT( rid.start_time, '%Y%m%d%H%i00' ) = #{item})
                OR
                (DATE_FORMAT( rid.start_time, '%Y%m%e%H%i00' ) = #{item})
                OR
                (DATE_FORMAT( rid.start_time, '%Y%c%d%H%i00' ) = #{item})
                )
            </foreach>
        </where>
            LIMIT 1
    </select>

    <select id="findRepertoireByTime" resultType="java.lang.Long">
        SELECT
            rid.repertoire_id
        FROM
            t_repertoire_info_detail AS rid
            LEFT JOIN t_repertoire_info AS ri ON ri.id = rid.repertoire_info_id
        WHERE
            ri.`status` = '1'
            AND ri.temporary_flag = '0'
            AND ri.theater_pass = '1'
            AND ri.repertoire_pass = '1'
            AND
            <foreach item="item" index="index" collection="time"
                     open="(" separator="OR" close=")" nullable="true">
                (
                (DATE_FORMAT( rid.start_time, '%Y%c%e%H%i' ) = #{item})
                OR
                (DATE_FORMAT( rid.start_time, '%Y%m%d%H%i' ) = #{item})
                OR
                (DATE_FORMAT( rid.start_time, '%Y%m%e%H%i' ) = #{item})
                OR
                (DATE_FORMAT( rid.start_time, '%Y%c%d%H%i' ) = #{item})
                OR
                (DATE_FORMAT( rid.start_time, '%Y%c%e%H%i00' ) = #{item})
                OR
                (DATE_FORMAT( rid.start_time, '%Y%m%d%H%i00' ) = #{item})
                OR
                (DATE_FORMAT( rid.start_time, '%Y%m%e%H%i00' ) = #{item})
                OR
                (DATE_FORMAT( rid.start_time, '%Y%c%d%H%i00' ) = #{item})
                )
            </foreach>
    </select>

</mapper>
