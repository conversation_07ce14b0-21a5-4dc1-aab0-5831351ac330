<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.PortfolioMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.Portfolio">
        <id column="id" property="id"/>
        <result column="no" property="no"/>
        <result column="batch" property="batch"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="theater_id" property="theaterId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="repertoire_ticket_id" property="repertoireTicketId"/>
        <result column="digital_avatar_id" property="digitalAvatarId"/>
        <result column="introduction" property="introduction"/>
        <result column="issued_quantity" property="issuedQuantity"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="price" property="price"/>
        <result column="statement" property="statement"/>
        <result column="audit" property="audit"/>
        <result column="audit_flag" property="auditFlag"/>
        <result column="audit_pass_time" property="auditPassTime"/>
        <result column="reasons_rejection" property="reasonsRejection"/>
        <result column="deleted" property="deleted"/>
        <result column="status" property="status"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , `no`, batch, merchant_id, theater_id, repertoire_id, repertoire_ticket_id, digital_avatar_id, introduction, issued_quantity, start_time, end_time, price, statement, audit, audit_flag, audit_pass_time, reasons_rejection, deleted, `status`, create_by, create_time, update_by, update_time
    </sql>

    <select id="getPortfolioByImageText" resultType="portfolioResponse">
        SELECT
            p.id,
            p.`name`,
            t.`name` AS theaterName,
            r.`name` AS repertoireName,
            p.ocr_no,
            p.sold_out,
            p.theater_id,
            p.repertoire_id,
            p.repertoire_ticket_id,
            p.digital_avatar_id
        FROM
            t_portfolio AS p
            LEFT JOIN t_repertoire AS r on r.id = p.repertoire_id
            LEFT JOIN t_theater as t on t.id = p.theater_id
        WHERE
            p.status = 1
            AND p.deleted = 1
            AND p.audit = 2
            AND #{imageText} LIKE CONCAT('%',t.`short_name`,'%')
            AND #{imageText} LIKE CONCAT('%',r.`short_name`,'%')
    </select>

    <select id="findPortfolioList" resultType="com.youying.system.domain.portfolio.PortfolioResponse">
        SELECT
            p.id,
            p.ocr_no,
            p.sold_out,
            p.scanning_id,
            p.seat_status,
            pi.id AS portfolioId,
            pi.no,
            pi.`name`,
            pi.theater_id,
            pi.repertoire_id,
            pi.repertoire_ticket_id,
            pi.digital_avatar_id,
            pi.cover_front,
            pi.cover_reverse,
            pi.common_image,
            pi.digital_avatar_common_image,
            t.`name` AS theaterName,
            t.`short_name` AS theaterShortName,
            r.`name` AS repertoireName,
            r.`short_name` AS repertoireShortName
        FROM
            t_portfolio_info AS pi
            LEFT JOIN t_portfolio AS p ON p.id = pi.portfolio_id
            LEFT JOIN t_repertoire AS r ON r.id = p.repertoire_id
            LEFT JOIN t_theater AS t ON t.id = p.theater_id
        <where>
            pi.`status` = 1
            <if test="repertoireIdsList != null and repertoireIdsList.size() > 0">
                AND p.repertoire_id IN
                <foreach item="item" index="index" collection="repertoireIdsList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            AND pi.`status` = 1
            AND p.`status` = 1
            AND p.audit_flag = 2
            AND p.deleted = 1
            AND t.`status` = 1
            AND t.deleted = 1
            AND t.audit = 2
            AND r.`status` = 1
            AND r.deleted = 1
            AND r.audit = 2
        </where>
        ORDER BY r.create_time DESC , pi.id
    </select>

</mapper>
