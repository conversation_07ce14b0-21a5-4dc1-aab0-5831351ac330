-- 初始化随机票Portfolio数据
-- 为随机票创建Portfolio和PortfolioInfo记录

-- 插入随机票Portfolio记录
INSERT INTO t_portfolio (
    `no`, batch, merchant_id, theater_id, repertoire_id, repertoire_ticket_id, 
    digital_avatar_id, ticket_type, introduction, issued_quantity, start_time, 
    end_time, `free`, price, statement, seat_status, sold_out, audit, audit_flag, 
    audit_pass_time, deleted, `status`, create_by, create_time, update_by, update_time
) VALUES 
(1001, 1, 1, NULL, NULL, NULL, NULL, 3, '经典红色随机票面，适用于各种演出场景', 1000, NOW(), DATE_ADD(NOW(), INTERVAL 1 YEAR), 1, 0.00, '随机票面，不对应具体剧目', 0, 0, 2, 1, NOW(), 1, 1, 'system', NOW(), 'system', NOW()),
(1002, 1, 1, NULL, NULL, NULL, NULL, 3, '优雅蓝色随机票面，现代简约设计风格', 1000, NOW(), DATE_ADD(NOW(), INTERVAL 1 YEAR), 1, 0.00, '随机票面，不对应具体剧目', 0, 0, 2, 1, NOW(), 1, 1, 'system', NOW(), 'system', NOW()),
(1003, 1, 1, NULL, NULL, NULL, NULL, 3, '温暖金色随机票面，高端典雅设计', 1000, NOW(), DATE_ADD(NOW(), INTERVAL 1 YEAR), 1, 0.00, '随机票面，不对应具体剧目', 0, 0, 2, 1, NOW(), 1, 1, 'system', NOW(), 'system', NOW()),
(1004, 1, 1, NULL, NULL, NULL, NULL, 3, '清新绿色随机票面，自然环保主题', 1000, NOW(), DATE_ADD(NOW(), INTERVAL 1 YEAR), 1, 0.00, '随机票面，不对应具体剧目', 0, 0, 2, 1, NOW(), 1, 1, 'system', NOW(), 'system', NOW()),
(1005, 1, 1, NULL, NULL, NULL, NULL, 3, '神秘紫色随机票面，艺术气息浓厚', 1000, NOW(), DATE_ADD(NOW(), INTERVAL 1 YEAR), 1, 0.00, '随机票面，不对应具体剧目', 0, 0, 2, 1, NOW(), 1, 1, 'system', NOW(), 'system', NOW());

-- 插入对应的PortfolioInfo记录
INSERT INTO t_portfolio_info (
    portfolio_id, ocr_no, `no`, `name`, batch, merchant_id, theater_id, 
    repertoire_id, repertoire_ticket_id, digital_avatar_id, ticket_type, 
    introduction, issued_quantity, start_time, end_time, `free`, price, 
    portfolio_statement_id, statement, cover_front, cover_reverse, 
    common_image, digital_avatar_common_image, `status`
) VALUES 
((SELECT id FROM t_portfolio WHERE `no` = 1001), 1001, 'RT-001', '经典红色票面', 1, 1, NULL, NULL, NULL, NULL, 3, '经典红色随机票面，适用于各种演出场景', 1000, NOW(), DATE_ADD(NOW(), INTERVAL 1 YEAR), 1, 0.00, NULL, '随机票面，不对应具体剧目', '/images/random/red_front.jpg', '/images/random/red_back.jpg', '/images/random/red_common.jpg', NULL, 1),
((SELECT id FROM t_portfolio WHERE `no` = 1002), 1002, 'RT-002', '优雅蓝色票面', 1, 1, NULL, NULL, NULL, NULL, 3, '优雅蓝色随机票面，现代简约设计风格', 1000, NOW(), DATE_ADD(NOW(), INTERVAL 1 YEAR), 1, 0.00, NULL, '随机票面，不对应具体剧目', '/images/random/blue_front.jpg', '/images/random/blue_back.jpg', '/images/random/blue_common.jpg', NULL, 1),
((SELECT id FROM t_portfolio WHERE `no` = 1003), 1003, 'RT-003', '温暖金色票面', 1, 1, NULL, NULL, NULL, NULL, 3, '温暖金色随机票面，高端典雅设计', 1000, NOW(), DATE_ADD(NOW(), INTERVAL 1 YEAR), 1, 0.00, NULL, '随机票面，不对应具体剧目', '/images/random/gold_front.jpg', '/images/random/gold_back.jpg', '/images/random/gold_common.jpg', NULL, 1),
((SELECT id FROM t_portfolio WHERE `no` = 1004), 1004, 'RT-004', '清新绿色票面', 1, 1, NULL, NULL, NULL, NULL, 3, '清新绿色随机票面，自然环保主题', 1000, NOW(), DATE_ADD(NOW(), INTERVAL 1 YEAR), 1, 0.00, NULL, '随机票面，不对应具体剧目', '/images/random/green_front.jpg', '/images/random/green_back.jpg', '/images/random/green_common.jpg', NULL, 1),
((SELECT id FROM t_portfolio WHERE `no` = 1005), 1005, 'RT-005', '神秘紫色票面', 1, 1, NULL, NULL, NULL, NULL, 3, '神秘紫色随机票面，艺术气息浓厚', 1000, NOW(), DATE_ADD(NOW(), INTERVAL 1 YEAR), 1, 0.00, NULL, '随机票面，不对应具体剧目', '/images/random/purple_front.jpg', '/images/random/purple_back.jpg', '/images/random/purple_common.jpg', NULL, 1);

-- 注意：实际使用时需要将图片路径替换为真实的图片URL
