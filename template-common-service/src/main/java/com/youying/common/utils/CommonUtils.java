package com.youying.common.utils;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.ConfigurableEnvironment;

import java.net.InetAddress;

/**
 * <AUTHOR>
 * @Date 2022/11/16
 */
@Slf4j
public class CommonUtils {
    /**
     * 打印项目信息
     *
     * @param context
     */
    @SneakyThrows
    public static void printProject(ConfigurableApplicationContext context) {
        ConfigurableEnvironment ev = context.getEnvironment();
        InetAddress inetAddress = InetAddress.getLocalHost();
        log.info("DataSource: " + ev.getProperty("spring.datasource.url"));
        log.info("Host IP: " + inetAddress.getHostAddress());
        log.info("Host Name: " + inetAddress.getHostName());
        String port = ev.getProperty("server.port");
        log.info("Server Port: " + port);
        log.info("Swagger: " + "http://localhost:" + port + "/swagger-ui/index.html\n");
    }
}
