package com.youying.common.utils;

public class StringUtilsTest {
    public static void main(String[] args) {
        String sourceStr1 = "21:00";
        String sourceStr2 = "式虐心悬疑话剧";
        String sourceStr3 = "无的十字UNENRTC2025-07-2";
        String sourceStr4 = "日语舞台剧《千与千寻》";
        String sourceStr5 = "千与千寻";
        String sourceStr6 = "虚无的十字架"; // 应该高匹配度的测试用例

        String targetStr = "虚无的十字架-东野圭吾沉浸式虐心悬疑话剧《虚无的十字架》[悬疑嘉年华]QUNENRTC2025-07-2619:30|星空间322号1张票￥190.00|一层B区2排1号淘麦VIP";

        double degree = StringUtils.matchDegree(sourceStr1, targetStr);
        double degree2 = StringUtils.matchDegree(sourceStr2, targetStr);
        double degree3 = StringUtils.matchDegree(sourceStr3, targetStr);
        double degree4 = StringUtils.matchDegree(sourceStr4, targetStr);
        double degree5 = StringUtils.matchDegree(sourceStr5, targetStr);
        double degree6 = StringUtils.matchDegree(sourceStr6, targetStr);

        System.out.println("=== 改进后的匹配算法测试结果 ===");
        System.out.println("短剧名1: " + sourceStr1 + " 匹配度: " + String.format("%.2f", degree * 100) + "%");
        System.out.println("短剧名2: " + sourceStr2 + " 匹配度: " + String.format("%.2f", degree2 * 100) + "%");
        System.out.println("短剧名3: " + sourceStr3 + " 匹配度: " + String.format("%.2f", degree3 * 100) + "%");
        System.out.println("短剧名4: " + sourceStr4 + " 匹配度: " + String.format("%.2f", degree4 * 100) + "%");
        System.out.println("短剧名5: " + sourceStr5 + " 匹配度: " + String.format("%.2f", degree5 * 100) + "%");
        System.out.println("短剧名6: " + sourceStr6 + " 匹配度: " + String.format("%.2f", degree6 * 100) + "%");
        System.out.println("\n票面内容: " + targetStr);

        System.out.println("\n=== 分析 ===");
        System.out.println("- sourceStr1 (21:00) 应该是低匹配度，因为只是时间格式，与剧目无关");
        System.out.println("- sourceStr6 (虚无的十字架) 应该是高匹配度，因为完全包含在目标字符串中");
        System.out.println("- 其他包含'千与千寻'的字符串应该是低匹配度，因为目标字符串是关于'虚无的十字架'的");
    }
}
