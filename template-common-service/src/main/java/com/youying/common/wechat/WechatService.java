package com.youying.common.wechat;

import com.alibaba.fastjson2.JSON;
import com.youying.common.exception.ServiceException;
import com.youying.common.utils.http.HttpUtils;
import com.youying.common.utils.sign.Base64;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.AlgorithmParameters;
import java.security.Key;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023-05-28
 */
@Service
public class WechatService {
    private static String WECHAT_APP_ID = "wxd8b884325915e7d6";
    private static String WECHAT_SECRET = "fc00b18abfb342611bcd1eec406381e0";

    /**
     * 获取小程序用户授权信息
     *
     * @param code
     * @return
     */
    public String getWechat(String code) {
        String wechatAuthUrl = "https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code";
        wechatAuthUrl = String.format(wechatAuthUrl, WECHAT_APP_ID, WECHAT_SECRET, code);
        String authStr = HttpUtils.sendGet(wechatAuthUrl);
        Map<String, String> doctor = JSON.parseObject(authStr, Map.class);
        String openid = doctor.get("openid");
        return openid;
    }

    /**
     * 小程序登录
     *
     * @param wechatModel
     * @return
     */
    public String wechatLogin(WechatModel wechatModel) {
        String openid = null;
        String session_key = null;
        WeChatSessionModel weChatSessionModel;
        String url = "https://api.weixin.qq.com/sns/jscode2session?appid=" + WECHAT_APP_ID + "&secret=" + WECHAT_SECRET + "&js_code=" + wechatModel.getCode() + "&grant_type=authorization_code";
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.GET, null, String.class);
        if (responseEntity != null && responseEntity.getStatusCode() == HttpStatus.OK) {
            String body = responseEntity.getBody();
            weChatSessionModel = JSON.parseObject(body, WeChatSessionModel.class);
            openid = weChatSessionModel.getOpenid();
            session_key = weChatSessionModel.getSession_key();
        }
        if (openid == null || session_key == null) {
            throw new ServiceException("小程序请求参数code错误，无法返回值！");
        }
        return openid;
    }

    /**
     * 获取用户手机号码
     *
     * @param code
     * @return
     */
    public String getWechatPhone(String code) {
        String url = "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=%s";
        url = String.format(url, getAccessToken());
        Map<String, String> params = new HashMap<String, String>();
        params.put("code", code);
        String jsonStr = JSON.toJSONString(params);
        String resStr = HttpUtils.sendPost(url, jsonStr);
        WechatRes res = JSON.parseObject(resStr, WechatRes.class);
        if ("-1".equals(res.getErrcode())) {
            throw new ServiceException("系统繁忙，此时请开发者稍候再试！");
        }
        if ("40029".equals(res.getErrcode())) {
            throw new ServiceException("小程序请求参数code无效！");
        }
        return res.getPhone_info().getPhoneNumber();
    }

    /**
     * 获取微信接口调用凭据
     *
     * @return
     */
    public String getAccessToken() {
        String url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s";
        url = String.format(url, WECHAT_APP_ID, WECHAT_SECRET);
        String resStr = HttpUtils.sendGet(url);
        Map map = JSON.parseObject(resStr, Map.class);
        return (String) map.get("access_token");
    }

    /**
     * 微信反编码解析
     *
     * @param encryptedData
     * @param sessionKey
     * @param iv
     * @return
     */
    public String wxAESDecrypt(String encryptedData, String sessionKey, String iv) {
        try {
            byte[] sessionKeyByte = Base64.decode(sessionKey);
            byte[] encryptedDataByte = Base64.decode(encryptedData);
            byte[] ivByte = Base64.decode(iv);
            Key key = new SecretKeySpec(sessionKeyByte, "AES");
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            AlgorithmParameters ivp = AlgorithmParameters.getInstance("AES");
            ivp.init(new IvParameterSpec(ivByte));
            cipher.init(Cipher.DECRYPT_MODE, key, ivp);
            return new String(cipher.doFinal(encryptedDataByte));
        } catch (Exception ex) {
            return null;
        }
    }


}
