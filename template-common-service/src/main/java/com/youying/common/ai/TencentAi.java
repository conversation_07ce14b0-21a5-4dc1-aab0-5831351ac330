package com.youying.common.ai;

import com.alibaba.fastjson2.JSON;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.ocr.v20181119.OcrClient;
import com.tencentcloudapi.ocr.v20181119.models.GroupInfo;
import com.tencentcloudapi.ocr.v20181119.models.ItemInfo;
import com.tencentcloudapi.ocr.v20181119.models.LineInfo;
import com.tencentcloudapi.ocr.v20181119.models.SmartStructuralOCRV2Request;
import com.tencentcloudapi.ocr.v20181119.models.SmartStructuralOCRV2Response;
import com.youying.common.core.common.AITextResponse;
import com.youying.common.exception.ServiceException;
import com.youying.common.utils.StringUtils;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2023-10-10
 */
@Slf4j
public class TencentAi {
    private static final String SECRET_ID = "AKIDqFSSDi8LL7uXDgwzoU16yb3HH2Zs0vc6";
    private static final String SECRET_KEY = "KSO5XmrpbAWgdOwHO4ZsxoR4Yps0znOr";
    private static final String[] ITEM_NAMES = { "时间", "DATE", "TIME", "日期", "座位", "票价", "金额", "排/ROW", "座/SEAT", "ROW",
            "SEAT", "区域" };

    /**
     * @param ImageBase64
     * @return
     */
    public static AITextResponse getTencentAi(String ImageBase64) {
        try {
            Credential cred = new Credential(SECRET_ID, SECRET_KEY);
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("ocr.tencentcloudapi.com");
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            OcrClient client = new OcrClient(cred, "ap-shanghai", clientProfile);
            SmartStructuralOCRV2Request req = new SmartStructuralOCRV2Request();

            req.setImageBase64(ImageBase64);
            req.setItemNames(ITEM_NAMES);
            req.setReturnFullText(true);

            SmartStructuralOCRV2Response resp = client.SmartStructuralOCRV2(req);
            GroupInfo[] respDateList = resp.getStructuralList();
            log.info("{} Tencent --> {}", DateUtil.now(), JSON.toJSONString(resp.getWordList()));
            AITextResponse ai = getTencentAiResponse(respDateList);
            ai.setText(JSON.toJSONString(resp.getWordList()));
            ai.setBody(JSON.toJSONString(resp.getStructuralList()));
            return ai;
        } catch (TencentCloudSDKException e) {
            log.error("{} --> 腾讯智能化识别异常：{}", DateUtil.now(), e.getMessage());
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 获取腾讯ORC内容
     *
     * @param respDateList
     * @return
     */
    private static AITextResponse getTencentAiResponse(GroupInfo[] respDateList) {
        AITextResponse ai = new AITextResponse();
        if (respDateList != null && respDateList.length > 0) {
            for (GroupInfo info : respDateList) {
                LineInfo[] lineInfos = info.getGroups();
                if (lineInfos != null && lineInfos.length > 0) {
                    for (LineInfo lineInfo : lineInfos) {
                        ItemInfo[] itemInfos = lineInfo.getLines();
                        if (itemInfos != null && itemInfos.length > 0) {
                            for (ItemInfo itemInfo : itemInfos) {
                                String autoName = itemInfo.getKey().getAutoName();
                                String autoContent = itemInfo.getValue().getAutoContent();
                                extractInformation(ai, autoName, autoContent);
                            }
                        }
                    }
                }
            }
        }
        if (!ai.getAddress().contains("座") && !ai.getAddress().contains("号") && StringUtils.isNotBlank(ai.getSeat())) {
            ai.setSeat(ai.getSeat() + "座");
        }
        if (!ai.getAddress().contains("排") && StringUtils.isNotBlank(ai.getRow())) {
            ai.setRow(ai.getRow() + "排");
        }
        if (StringUtils.isNotBlank(ai.getArea())) {
            ai.setSeat(ai.getArea() + ai.getRow() + ai.getSeat());
        }
        int flag = 0;
        for (String time : ai.getTimeList()) {
            if (time.length() == 4) {
                ai.setTime(time);
                flag++;
            }
            if (time.length() > 4 && time.length() <= 8) {
                ai.setDate(time);
                flag++;
            }
        }
        if (flag == 2) {
            ai.getTimeList().add(ai.getDate() + ai.getTime());
        }
        return ai;
    }

    /**
     * 优化数据
     *
     * @param ai
     * @param autoName
     * @param autoContent
     * @return
     */
    private static AITextResponse extractInformation(AITextResponse ai, String autoName, String autoContent) {
        switch (autoName) {
            case "日期":
            case "DATE":
            case "时间":
            case "TIME":
                String time = autoContent.replaceAll("[^0-9]", "");
                if (ai.getTimeList().contains(time)) {
                    break;
                }
                ai.getTimeList().add(time);
                break;
            case "区域":
                if (ai.getAddress().contains(autoContent)) {
                    break;
                }
                ai.setArea(ai.getArea() + autoContent);
                ai.setAddress(ai.getAddress() + ai.getArea());
                break;
            case "座位":
            case "座/SEAT":
            case "SEAT":
                if (ai.getAddress().contains(autoContent)) {
                    break;
                }
                ai.setSeat(ai.getSeat() + autoContent);
                ai.setAddress(ai.getAddress() + ai.getSeat());
                break;
            case "ROW":
            case "排/ROW":
                if (ai.getAddress().contains(autoContent)) {
                    break;
                }
                ai.setRow(ai.getRow() + autoContent);
                ai.setAddress(ai.getAddress() + ai.getRow());
                break;
            case "票价":
            case "金额":
                String price = autoContent.replaceAll("¥|￥", "");
                ai.setPrice(price);
                break;
            default:
        }
        return ai;
    }

}
