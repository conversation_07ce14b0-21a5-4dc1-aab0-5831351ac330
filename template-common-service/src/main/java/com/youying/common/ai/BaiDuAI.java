package com.youying.common.ai;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.youying.common.constant.CacheConstants;
import com.youying.common.core.common.AITextResponse;
import com.youying.common.core.redis.RedisCache;
import com.youying.common.exception.ServiceException;
import com.youying.common.utils.http.HttpUtil;
import com.youying.common.utils.http.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 百度AI图片文字识别
 *
 * <AUTHOR>
 * @since 2023-05-30
 */
@Service
@Slf4j
public class BaiDuAI {
    /**
     * 百度AI识别调用地址
     */
    private final static String SERVER_URL = "https://aip.baidubce.com/rest/2.0/ocr/v1/accurate_basic";
    /**
     * 百度AI识别调用地址（建模）
     */
    private final static String SERVER_TEMPLATE_URL = "https://aip.baidubce.com/rest/2.0/solution/v1/iocr/recognise";
    /**
     * 获取请求头请求地址
     */
    private final static String ACCESS_TOKEN_URL = "https://aip.baidubce.com/oauth/2.0/token?";
    private final static String API_KEY = "GhXPpD83YtYRGKjKNiClL5ht";
    private final static String SECRET_KEY = "EGiGwimZvo8v87hqvtsnLyUpO7PY8MEZ";
    @Autowired
    private RedisCache redisCache;

    /**
     * 百度AI图片识别
     *
     * @param imgStr
     * @return
     */
    public AITextResponse imgRecognition(String imgStr) {
        // 请求url
        try {
            String imgParam = URLEncoder.encode(imgStr, "UTF-8");
            String param = "image=" + imgParam + "&detect_direction=true";
            String accessToken = getAccessToken();
            String result = HttpUtil.post(SERVER_URL, accessToken, param);
            AITextResponse aiText = getRepertoireName(result);
            return aiText;
        } catch (UnsupportedEncodingException e) {
            log.error(e.getMessage(), e);
            throw new ServiceException("扫描识别错误，请联系管理员");
        }
    }

    /**
     * 百度AI图片识别
     *
     * @param imgStr
     * @return
     */
    public String getImageText(String imgStr) {
        // 请求url
        try {
            String imgParam = URLEncoder.encode(imgStr, "UTF-8");
            String param = "image=" + imgParam + "&detect_direction=true";
            String accessToken = getAccessToken();
            return HttpUtil.post(SERVER_URL, accessToken, param);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException("扫描识别错误，请联系管理员");
        }
    }

    /**
     * 获取请求头
     *
     * @return
     */
    public final String getAccessToken() {
        String accessToken = redisCache.getCacheObject(CacheConstants.BAI_DU_AI);
        if (StringUtils.isNotBlank(accessToken)) {
            return accessToken;
        }
        final String url = ACCESS_TOKEN_URL + "client_id=" + API_KEY + "&client_secret=" + SECRET_KEY + "&grant_type=client_credentials";
        String res = HttpUtils.sendGet(url);
        Map<String, String> resMap = JSON.parseObject(res, Map.class);
        accessToken = resMap.get("access_token");
        redisCache.setCacheObject(CacheConstants.BAI_DU_AI, accessToken, CacheConstants.BAI_DU_TIME, TimeUnit.SECONDS);
        return accessToken;
    }

    /**
     * 获取剧目信息
     *
     * @param dataStr
     * @return
     */
    private AITextResponse getRepertoireName(String dataStr) {
        JSONArray jsonArray = JSONObject.parseObject(dataStr).getJSONArray("words_result");
        if (jsonArray.size() > 0) {
            StringBuffer wordsStr = new StringBuffer();
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String words = jsonObject.getString("words");
                wordsStr.append(words).append(";");
            }
            String[] patterns = {
//                    "(?<=时间[:：]).*?(?=;|$)",
                    "(\\d{4})-(\\d{2})-(\\d{2})",
                    "(?<=座位[:：]).*?(?=;|$)",
                    "(?<=地址[:：]).*?(?=;|$)",
                    "\\b\\d+元\\b",
                    "《([^》]+)》",
                    "(?<=场馆[:：]).*?(?=;|$)",
                    "(?<=票号[:：]).*?(?=;|$)",
                    "(\\d{2}[:：]\\d{2})",
                    ";(\\d{1,5}楼);|;(\\d{1,5};楼);",
                    ";(\\d{1,5}排);|;(\\d{1,5};排);",
                    ";(\\d{1,5}号);|;(\\d{1,5};号);",
                    ";(\\d{1,5}座);|;(\\d{1,5};座);"
            };
            if (patterns.length > 0) {
                AITextResponse aiText = new AITextResponse();
                for (int i = 0; i < patterns.length; i++) {
                    Pattern pattern = Pattern.compile(patterns[i]);
                    Matcher matcher = pattern.matcher(wordsStr);
                    if (matcher.find()) {
                        String match = matcher.group(0);
                        switch (i) {
                            case 0:
                                String date = match.replaceAll("星期(.)", "").replaceAll("：", ":").trim();
                                try {
                                    aiText.setDate(date);
                                } catch (Exception e) {
                                    log.error(e.getMessage(), e);
                                }
                                break;
                            case 1:
                                aiText.setSeat(match.replaceAll("☒", "区"));
                                break;
                            case 2:
                                aiText.setAddress(match);
                                break;
                            case 3:
                                aiText.setPrice(match);
                                break;
                            case 4:
                                aiText.setRepertoire(match.replaceAll("《|》", "").replace("", ""));
                                break;
                            case 5:
                                aiText.setTheater(match);
                                break;
                            case 6:
                                aiText.setNo(match);
                                break;
                            case 7:
                                String time = match.replaceAll("：", ":");
                                time = time + ":00";
                                aiText.setTime(time);
                                break;
                            case 8:
                            case 9:
                            case 10:
                            case 11:
                                String seat = match.replaceAll(";", "");
                                aiText.setSeat(aiText.getSeat() + seat);
                                break;
                            default:
                                break;
                        }
                    }
                }
                return aiText;
            }
        }
        return null;
    }

    /**
     * 根据百度建模检索信息
     *
     * @param templateSign
     * @return
     */
    public AITextResponse findImageTextByTemplateCode(String imgBase64, String templateSign) throws UnsupportedEncodingException {
        if (StringUtils.isEmpty(templateSign)) {
            throw new ServiceException("数据配置错误，请联系管理员。");
        }
        String recogniseParams = "templateSign=" + templateSign + "&image=" + URLEncoder.encode(imgBase64, "UTF-8");
        String accessToken = getAccessToken();
        String resStr = HttpUtil.post(SERVER_TEMPLATE_URL, accessToken, recogniseParams);
        JSONArray jsonArray = JSONObject.parseObject(resStr).getJSONArray("ret");
        AITextResponse response = new AITextResponse();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            String word_name = jsonObject.getString("word_name");

        }
        return response;
    }
}
