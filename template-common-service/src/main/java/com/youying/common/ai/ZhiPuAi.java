package com.youying.common.ai;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.youying.common.exception.ServiceException;
import com.zhipu.oapi.ClientV4;
import com.zhipu.oapi.Constants;
import com.zhipu.oapi.service.v4.model.ChatCompletionRequest;
import com.zhipu.oapi.service.v4.model.ChatMessage;
import com.zhipu.oapi.service.v4.model.ChatMessageRole;
import com.zhipu.oapi.service.v4.model.ModelApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 智谱AI大模型
 *
 * <AUTHOR>
 * @since 2024-07-11
 */
@Slf4j
public class ZhiPuAi {
    private static final ClientV4 CLIENT = new ClientV4.Builder("3decaa7e3908863abbd83a72b5995d01.jf7EGMXY2y3J7PYw").build();

    /**
     * 异步调用
     */
    public static ZhiPuRequest getImagesRequest(String url) {
        List<ChatMessage> messages = new ArrayList<>();
        List<Map<String, Object>> contentList = new ArrayList<>();
        Map<String, Object> textMap = new HashMap<>();
        textMap.put("type", "text");
        textMap.put("text", "提炼出日期时间，座位，票价。输出格式为{time:'',seat:'',price:''}");
        Map<String, Object> typeMap = new HashMap<>();
        typeMap.put("type", "image_url");
        Map<String, Object> urlMap = new HashMap<>();
        urlMap.put("url", url);
        typeMap.put("image_url", urlMap);
        contentList.add(textMap);
        contentList.add(typeMap);
        ChatMessage chatMessage = new ChatMessage(ChatMessageRole.USER.value(), contentList);
        messages.add(chatMessage);

        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                .model(Constants.ModelChatGLM4V)
                .stream(Boolean.FALSE)
                .invokeMethod(Constants.invokeMethod)
                .messages(messages)
                .requestId(IdUtil.getSnowflakeNextIdStr())
                .build();
        ModelApiResponse modelApiResponse = CLIENT.invokeModelApi(chatCompletionRequest);
        if (modelApiResponse.getCode() == 200 && CollectionUtils.isNotEmpty(modelApiResponse.getData().getChoices())) {
            String string = modelApiResponse.getData().getChoices().get(0).getMessage().getContent().toString().replaceAll("`", "").replaceAll("json", "");
            return JSON.parseObject(string, ZhiPuRequest.class);
        } else {
            throw new ServiceException(modelApiResponse.getMsg());
        }
    }

}
