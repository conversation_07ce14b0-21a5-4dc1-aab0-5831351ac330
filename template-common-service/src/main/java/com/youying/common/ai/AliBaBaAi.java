package com.youying.common.ai;

import com.alibaba.fastjson2.JSON;
import com.aliyun.documentautoml20221229.Client;
import com.aliyun.documentautoml20221229.models.PredictTemplateModelRequest;
import com.aliyun.documentautoml20221229.models.PredictTemplateModelResponse;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.google.gson.internal.LinkedTreeMap;
import com.youying.common.core.common.AITextResponse;
import com.youying.common.exception.ServiceException;
import com.youying.common.utils.DateUtils;
import com.youying.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-08-30
 */
@Slf4j
public class AliBaBaAi {
    private static final String ACCESS_KEY_ID = "LTAI5tNjtySxp2w7vHZu2bZw";
    private static final String ACCESS_KEY_SECRET = "******************************";

    /**
     * 使用AK&SK初始化账号Client
     *
     * @return Client
     * @throws Exception
     */
    private static Client createClient() throws Exception {
        Config config = new Config()
                .setAccessKeyId(ACCESS_KEY_ID)
                .setAccessKeySecret(ACCESS_KEY_SECRET);
        config.endpoint = "documentautoml.cn-beijing.aliyuncs.com";
        return new Client(config);
    }

    /**
     * 图片识别
     *
     * @param taskId
     * @param imageBase64Str
     * @throws Exception
     */
    public static AITextResponse imgRecognition(Long taskId, String imageBase64Str) throws Exception {
        Client client = createClient();
        PredictTemplateModelRequest predictTemplateModelRequest = new PredictTemplateModelRequest()
                .setTaskId(taskId)
                .setBinaryToText(true)
                .setBody(imageBase64Str);
        PredictTemplateModelResponse response = client.predictTemplateModelWithOptions(predictTemplateModelRequest, new RuntimeOptions().setConnectTimeout(30000));
        String code = response.getBody().getCode();
        if (!"200".equals(code)) {
            log.error("{} ->> 识别错误，{} ", DateUtils.getTime(), response.body.getMessage());
            throw new ServiceException("本场演出没有进入演都， 请联系客服帮助我们完善信息~谢谢您（客服联系方式在 我的-系统公告中）");
        }
        List<LinkedTreeMap> list = (List<LinkedTreeMap>) response.getBody().getData().get("data");
        log.info("{} 阿里ORC识别： --> ", JSON.toJSONString(list));
        AITextResponse aiText = getImageText(list);
        aiText.setBody(JSON.toJSONString(list));
        return aiText;
    }

    private static AITextResponse getImageText(List<LinkedTreeMap> list) {
        AITextResponse ai = new AITextResponse();
        for (LinkedTreeMap linkedTreeMap : list) {
            String fieldName = (String) linkedTreeMap.get("fieldName");
            String fieldWord = (String) linkedTreeMap.get("fieldWord");
            String text = fieldWord.replace(":", "").replace("：", "");
            switch (fieldName) {
                case "repertoireName":
                    ai.setRepertoire(text);
                    break;
                case "theaterName":
                    ai.setTheater(text);
                    break;
                case "date":
                    String date = text.replaceAll("[^0-9]", "");
                    ai.setDate(date);
                    break;
                case "time":
                    String time = text.replaceAll("[^0-9]", "");
                    ai.setTime(time + "00");
                    break;
                case "dateTime":
                    String dateTime = text.replaceAll("[^0-9]", "");
                    ai.setDateTime(dateTime + "00");
                    break;
                case "area":
                    ai.setArea(text);
                    break;
                case "row":
                    boolean flag = text.contains("排");
                    if (flag) {
                        ai.setRow(text);
                    } else {
                        ai.setRow(text + "排");
                    }
                case "seat":
                    ai.setSeat(text);
                    break;
                case "price":
                    String price = text.replaceAll("¥|￥", "");
                    ai.setPrice(price);
                    break;
                case "address":
                    ai.setAddress(text);
                    break;
                default:
            }
        }
        if (StringUtils.isNotBlank(ai.getTime()) && StringUtils.isNotBlank(ai.getDate())) {
            ai.setDateTime(ai.getDate() + ai.getTime());
        }
        if (StringUtils.isNotBlank(ai.getArea()) || StringUtils.isNotBlank(ai.getRow())) {
            boolean flag = ai.getSeat().contains("座");
            if (!flag) {
                ai.setSeat(ai.getSeat() + "座");
            }
            ai.setSeat(ai.getArea() + ai.getRow() + ai.getSeat());
        }
        return ai;
    }
}
