package com.youying.common.pay;


import com.wechat.pay.java.service.payments.model.Transaction;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * <AUTHOR>
 * @since 2023-08-03
 */
public interface PaymentStrategy {
    /**
     * 支付
     *
     * @param orderNo 订单号
     * @param amount  金额
     * @return
     */
    String pay(String orderNo, String amount);

    /**
     * 关闭支付
     *
     * @param outTradeNo
     */
    void closePay(String outTradeNo);

    /**
     * 查询支付信息
     *
     * @param transactionId
     * @param orderQueryType 1：支付编号 2：商家支付编号
     * @return
     */
    PayInfo findPayInfo(String transactionId, Integer orderQueryType);

    /**
     * 支付回调
     *
     * @param request
     * @return
     * @throws IOException
     */
    Transaction callback(HttpServletRequest request) throws IOException;
}