package com.youying.common.pay;


import com.wechat.pay.java.service.payments.model.Transaction;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * <AUTHOR>
 * @since 2023-08-03
 */
public class PaymentContext {
    private PaymentStrategy paymentStrategy;

    public PaymentStrategy getPaymentStrategy() {
        return paymentStrategy;
    }

    public void setPaymentStrategy(PaymentStrategy paymentStrategy) {
        this.paymentStrategy = paymentStrategy;
    }

    public String executePayment(String orderNo, String amount) {
        return paymentStrategy.pay(orderNo, amount);
    }

    public Transaction callback(HttpServletRequest request) throws IOException {
        return paymentStrategy.callback(request);
    }

    public PayInfo findPayInfo(String transactionId, Integer orderQueryType) {
        return paymentStrategy.findPayInfo(transactionId, orderQueryType);
    }

    public void closePay(String transactionId) {
        paymentStrategy.closePay(transactionId);
    }
}
