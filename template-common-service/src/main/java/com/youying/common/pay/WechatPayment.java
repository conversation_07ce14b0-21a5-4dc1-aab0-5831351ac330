package com.youying.common.pay;

import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.core.exception.ServiceException;
import com.wechat.pay.java.core.exception.ValidationException;
import com.wechat.pay.java.core.notification.NotificationConfig;
import com.wechat.pay.java.core.notification.NotificationParser;
import com.wechat.pay.java.core.notification.RequestParam;
import com.wechat.pay.java.core.util.PemUtil;
import com.wechat.pay.java.service.payments.jsapi.JsapiService;
import com.wechat.pay.java.service.payments.jsapi.model.Amount;
import com.wechat.pay.java.service.payments.jsapi.model.CloseOrderRequest;
import com.wechat.pay.java.service.payments.jsapi.model.Payer;
import com.wechat.pay.java.service.payments.jsapi.model.PrepayRequest;
import com.wechat.pay.java.service.payments.jsapi.model.PrepayResponse;
import com.wechat.pay.java.service.payments.jsapi.model.QueryOrderByIdRequest;
import com.wechat.pay.java.service.payments.jsapi.model.QueryOrderByOutTradeNoRequest;
import com.wechat.pay.java.service.payments.model.Transaction;
import com.wechat.pay.java.service.refund.RefundService;
import com.youying.common.config.FriendBetterConfig;
import com.youying.common.enums.Enums.OrderQueryType;
import com.youying.common.utils.SecurityUtils;
import com.youying.common.utils.StringUtils;
import com.youying.common.utils.uuid.IdUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.SignatureException;
import java.util.Base64;

/**
 * <AUTHOR>
 * @since 2023-08-03
 */
@Slf4j
public class WechatPayment implements PaymentStrategy {
    private static final String APP_ID = "wxd8b884325915e7d6";
    private static final String MCH_ID = "1650099458";
    private static final String WECHAT_PAY_PWD = "GWAh5BGRG11OlX3moysfFo8jc8UcRrCz";
    private static final String MERCHANT_SERIAL_NUMBER = "62FBD6CCD1FF14458C9F49297586776B83F33552";

    /**
     * 获取私钥
     *
     * @return
     */
    public static PrivateKey getPrivateKey() {
        return PemUtil.loadPrivateKeyFromPath(FriendBetterConfig.getPrivateKeyFromPath());
    }

    /**
     * 获取支付签名
     *
     * @param prepayId
     * @return
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeyException
     * @throws SignatureException
     */
    public static WechatPay sign(String prepayId) throws NoSuchAlgorithmException, InvalidKeyException, SignatureException {
        String timeStamp = System.currentTimeMillis() / 1000 + "";
        String nonceStr = IdUtils.simpleUUID();
        String packageStr = "prepay_id=" + prepayId;

        StringBuilder signStr = new StringBuilder();
        signStr.append(APP_ID).append("\n");
        signStr.append(timeStamp).append("\n");
        signStr.append(nonceStr).append("\n");
        signStr.append(packageStr).append("\n");

        byte[] message = signStr.toString().getBytes(StandardCharsets.UTF_8);

        Signature sign = Signature.getInstance("SHA256withRSA");
        sign.initSign(getPrivateKey());
        sign.update(message);
        String signStrBase64 = Base64.getEncoder().encodeToString(sign.sign());

        WechatPay wechatPay = new WechatPay();
        wechatPay.setPrepayId(prepayId);
        wechatPay.setTimeStamp(timeStamp);
        wechatPay.setNonceStr(nonceStr);
        wechatPay.setPackageStr(packageStr);
        wechatPay.setSign(signStrBase64);
        return wechatPay;
    }

    /**
     * 微信支付
     *
     * @param price
     */
    @Override
    @Transactional
    public String pay(String orderNo, String price) {
        log.info("微信支付 >>>>>>>>>>>>>>>>> 金额：{}", price);
        if (BigDecimal.ZERO.compareTo(new BigDecimal(price)) == 0) {
            return null;
        }

        PrepayRequest request = new PrepayRequest();
        Amount amount = new Amount();
        BigDecimal total = new BigDecimal(price).multiply(new BigDecimal("100"));
        amount.setTotal(total.intValue());
        Payer payer = new Payer();
        String openId = SecurityUtils.getUser().getOpenId();
        if (StringUtils.isBlank(openId)) {
            throw new com.youying.common.exception.ServiceException("参数不完整");
        }
        payer.setOpenid(openId);
        request.setAmount(amount);
        request.setPayer(payer);
        request.setAppid(APP_ID);
        request.setMchid(MCH_ID);
        request.setDescription("测试商品");
        request.setNotifyUrl(FriendBetterConfig.getNotifyUrl());
        request.setOutTradeNo(orderNo);
        // 调用下单方法，得到应答
        PrepayResponse response = getJsapiService().prepay(request);
        return response.getPrepayId();
    }

    /**
     * 关闭微信支付
     */
    @Override
    public void closePay(String outTradeNo) {
        CloseOrderRequest closeRequest = new CloseOrderRequest();
        closeRequest.setMchid(MCH_ID);
        closeRequest.setOutTradeNo(outTradeNo);
        getJsapiService().closeOrder(closeRequest);
    }

    /**
     * 查询支付信息
     *
     * @param transactionId
     * @param orderQueryType 1：支付编号 2：商家支付编号
     * @return
     */
    @Override
    public PayInfo findPayInfo(String transactionId, Integer orderQueryType) {
        PayInfo payInfo = new PayInfo();
        if (OrderQueryType.TRANSACTION_ID.getCode().equals(orderQueryType)) {
            QueryOrderByIdRequest queryRequest = new QueryOrderByIdRequest();
            queryRequest.setTransactionId(transactionId);
            queryRequest.setMchid(MCH_ID);
            try {
                payInfo.setTransaction(getJsapiService().queryOrderById(queryRequest));
            } catch (ServiceException e) {
                log.error(e.getErrorCode(), e);
            }
        } else {
            QueryOrderByOutTradeNoRequest orderByOutTradeNoRequest = new QueryOrderByOutTradeNoRequest();
            orderByOutTradeNoRequest.setOutTradeNo(transactionId);
            orderByOutTradeNoRequest.setMchid(MCH_ID);
            try {
                payInfo.setTransaction(getJsapiService().queryOrderByOutTradeNo(orderByOutTradeNoRequest));
            } catch (ServiceException e) {
                log.error(e.getErrorCode(), e);
            }
        }
        return payInfo;
    }

    /**
     * 微信回调
     *
     * @param request
     * @return
     * @throws IOException
     */
    @Override
    @Transactional
    public Transaction callback(HttpServletRequest request) throws IOException {
        log.info("微信回调 >>>>>>>>>>>>>>>>> ");
        BufferedReader br = request.getReader();
        String str;
        StringBuilder builder = new StringBuilder();
        while ((str = br.readLine()) != null) {
            builder.append(str);
        }

        RequestParam requestParam = new RequestParam.Builder()
                .serialNumber(request.getHeader(WechatPayHttpHeaders.WECHATPAY_SERIAL))
                .nonce(request.getHeader(WechatPayHttpHeaders.WECHATPAY_NONCE))
                .timestamp(request.getHeader(WechatPayHttpHeaders.WECHATPAY_TIMESTAMP))
                .signature(request.getHeader(WechatPayHttpHeaders.WECHATPAY_SIGNATURE))
                .body(builder.toString())
                .build();

        NotificationConfig config = new RSAAutoCertificateConfig.Builder()
                .merchantId(MCH_ID)
                .privateKeyFromPath(FriendBetterConfig.getPrivateKeyFromPath())
                .merchantSerialNumber(MERCHANT_SERIAL_NUMBER)
                .apiV3Key(WECHAT_PAY_PWD)
                .build();

        NotificationParser parser = new NotificationParser(config);

        try {
            // 以支付通知回调为例，验签、解密并转换成 Transaction
            return parser.parse(requestParam, Transaction.class);
        } catch (ValidationException e) {
            log.error("sign verification failed", e);
            log.error(e.getMessage(), e);
        }
        return new Transaction();
    }

    /**
     * 创建小程序支付服务
     *
     * @return
     */
    protected JsapiService getJsapiService() {
        Config config =
                new RSAAutoCertificateConfig.Builder()
                        .merchantId(MCH_ID)
                        .privateKeyFromPath(FriendBetterConfig.getPrivateKeyFromPath())
                        .merchantSerialNumber(MERCHANT_SERIAL_NUMBER)
                        .apiV3Key(WECHAT_PAY_PWD)
                        .build();
        config.createSigner().getAlgorithm();
        return new JsapiService.Builder().config(config).build();
    }

    /**
     * 创建小程序支付服务
     *
     * @return
     */
    protected RefundService getRefundService() {
        Config config =
                new RSAAutoCertificateConfig.Builder()
                        .merchantId(MCH_ID)
                        .privateKeyFromPath(FriendBetterConfig.getPrivateKeyFromPath())
                        .merchantSerialNumber(MERCHANT_SERIAL_NUMBER)
                        .apiV3Key(WECHAT_PAY_PWD)
                        .build();
        config.createSigner().getAlgorithm();
        return new RefundService.Builder().config(config).build();
    }

}
