package com.youying.common.pay;

/**
 * 微信支付HTTP请求头相关常量
 *
 * <AUTHOR>
 * @return
 */
public final class WechatPayHttpHeaders {
    /**
     * 微信回调参数==>微信序列号
     */
    public static final String WECHATPAY_SERIAL = "Wechatpay-Serial";

    /**
     * 微信回调参数==>应答随机串
     */
    public static final String WECHATPAY_NONCE = "Wechatpay-Nonce";

    /**
     * 微信回调参数==>应答时间戳
     */
    public static final String WECHATPAY_TIMESTAMP = "Wechatpay-Timestamp";

    /**
     * 微信回调参数==>应答签名
     */
    public static final String WECHATPAY_SIGNATURE = "Wechatpay-Signature";
}
