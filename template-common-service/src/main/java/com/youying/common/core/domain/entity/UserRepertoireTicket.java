package com.youying.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * <p>
 * 用户电子票表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_user_repertoire_ticket")
public class UserRepertoireTicket extends Model<UserRepertoireTicket> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 编号
     */
    @TableField("`no`")
    private String no;

    /**
     * 商家ID
     */
    @TableField("merchant_id")
    private Long merchantId;

    /**
     * 剧场ID (0为暂无)
     */
    @TableField("theater_id")
    private Long theaterId;

    /**
     * 剧目ID
     */
    @TableField("repertoire_id")
    private Long repertoireId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 电子票ID
     */
    @TableField("repertoire_ticket_id")
    private Long repertoireTicketId;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
