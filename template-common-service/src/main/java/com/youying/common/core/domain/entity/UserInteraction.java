package com.youying.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 用户互动通知表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-18
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_user_interaction")
public class UserInteraction extends Model<UserInteraction> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 关联用户ID
     */
    @TableField("reply_user_id")
    private Long replyUserId;

    /**
     * 关联ID
     */
    @TableField("relevance_id")
    private Long relevanceId;

    /**
     * 剧目ID
     */
    @TableField("repertoire_id")
    private Long repertoireId;

    /**
     * 回复关联ID
     */
    @TableField("reply_relevance_id")
    private Long replyRelevanceId;

    /**
     * 互动类型（1、评论，2、评论点赞，3、提问，4、动态点赞）
     */
    @TableField("`type`")
    private Integer type;

    /**
     * 查看状态
     */
    @TableField("look_flag")
    private Integer lookFlag;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
