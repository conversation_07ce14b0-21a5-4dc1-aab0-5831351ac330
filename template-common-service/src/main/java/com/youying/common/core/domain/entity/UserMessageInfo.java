package com.youying.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 用户消息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-06
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_user_message_info")
public class UserMessageInfo extends Model<UserMessageInfo> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商家ID
     */
    @TableField(value = "merchant_id", fill = FieldFill.INSERT)
    private Long merchantId;

    /**
     * 剧场ID (0为暂无)
     */
    @TableField("theater_id")
    private Long theaterId;

    /**
     * 剧目ID
     */
    @TableField("repertoire_id")
    private Long repertoireId;

    /**
     * 用户会话ID
     */
    @TableField("user_message_id")
    private Long userMessageId;

    /**
     * 用户商家ID
     */
    @TableField("user_merchant_id")
    private Long userMerchantId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 群发消息ID
     */
    @TableField("message_id")
    private Long messageId;

    /**
     * 消息
     */
    @TableField("body")
    private String body;

    /**
     * 查看状态
     */
    @TableField("look_flag")
    private Integer lookFlag;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
