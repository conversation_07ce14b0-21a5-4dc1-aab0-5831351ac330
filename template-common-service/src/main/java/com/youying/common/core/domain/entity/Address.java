package com.youying.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_address")
public class Address extends Model<Address> {

    private Long id;

    /**
     * 地区名称
     */
    @TableField("`name`")
    private String name;

    /**
     * 父级
     */
    @TableField("parent_id")
    private Long parentId;

    /**
     * 地区全名
     */
    @TableField("fullname")
    private String fullname;

    /**
     * 纬度
     */
    @TableField("lat")
    private String lat;

    /**
     * 经度
     */
    @TableField("lng")
    private String lng;

    /**
     * 1，省分；2，市；3，区、县
     */
    @TableField("level_type")
    private Integer levelType;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
