package com.youying.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 剧目场次信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-06
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_repertoire_info")
public class RepertoireInfo extends Model<RepertoireInfo> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 发起商家ID
     */
    @TableField("initiate_merchant_id")
    private Long initiateMerchantId;

    /**
     * 确认商家ID
     */
    @TableField("release_merchant_id")
    private Long releaseMerchantId;

    /**
     * 省
     */
    @TableField("prov_id")
    private Long provId;

    /**
     * 剧场ID (0为暂无)
     */
    @TableField("theater_id")
    private Long theaterId;

    /**
     * 剧目ID
     */
    @TableField("repertoire_id")
    private Long repertoireId;

    /**
     * 临时剧目/剧场（0否，1是）
     */
    @TableField("temporary_flag")
    private Integer temporaryFlag;

    /**
     * 剧场确认状态
     */
    @TableField("theater_pass")
    private Integer theaterPass;

    /**
     * 剧目确认状态
     */
    @TableField("repertoire_pass")
    private Integer repertoirePass;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 状态
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 审核（0待审核，1驳回，2通过）
     */
    @TableField("audit")
    private Integer audit;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
