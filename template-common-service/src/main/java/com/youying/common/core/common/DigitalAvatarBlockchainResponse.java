package com.youying.common.core.common;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 电子头像区块链表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-06
 */
@Getter
@Setter
@Accessors(chain = true)
public class DigitalAvatarBlockchainResponse {

    private Long id;

    /**
     * 商家ID
     */
    private Long merchantId;

    /**
     * 批次
     */
    private Integer batch;

    /**
     * 剧场ID
     */
    private Long theaterId;

    /**
     * 剧目ID
     */
    private Long repertoireId;

    /**
     * 发行时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 普通数字头像图片
     */
    private String commonImage;

    /**
     * 发行方
     */
    private String issuerName;

    /**
     * 发行数量
     */
    private Integer issuedQuantity;

    /**
     * 头像链接
     */
    private List<String> digitalAvatarUrl;
}
