package com.youying.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 地区表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-06
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_area")
public class Area extends Model<Area> {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 省市区名称
     */
    @TableField("`name`")
    private String name;

    /**
     * 编码
     */
    @TableField("`code`")
    private String code;

    /**
     * 上级ID
     */
    @TableField("parent_id")
    private Long parentId;

    /**
     * 简称
     */
    @TableField("shortname")
    private String shortname;

    /**
     * 级别:0,中国；1，省分；2，市；3，区、县
     */
    @TableField("level_type")
    private Integer levelType;

    /**
     * 城市代码
     */
    @TableField("city_code")
    private String cityCode;

    /**
     * 邮编
     */
    @TableField("zipcode")
    private String zipcode;

    /**
     * 经度
     */
    @TableField("lng")
    private String lng;

    /**
     * 纬度
     */
    @TableField("lat")
    private String lat;

    /**
     * 拼音
     */
    @TableField("pinyin")
    private String pinyin;

    /**
     * 状态
     */
    @TableField("`status`")
    private String status;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
