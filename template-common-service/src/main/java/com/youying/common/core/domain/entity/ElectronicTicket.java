package com.youying.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 剧目剧场电子票
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-25
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_electronic_ticket")
public class ElectronicTicket extends Model<ElectronicTicket> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商家ID
     */
    @TableField(value = "merchant_id", fill = FieldFill.INSERT)
    private Long merchantId;

    /**
     * 剧目ID
     */
    @TableField("repertoire_id")
    private Long repertoireId;

    /**
     * 剧场ID
     */
    @TableField("theater_id")
    private Long theaterId;

    /**
     * 正面
     */
    @TableField("front")
    private String front;

    /**
     * 反面
     */
    @TableField("`reverse`")
    private String reverse;

    /**
     * 发行数量
     */
    @TableField("issued_quantity")
    private Integer issuedQuantity;

    /**
     * 发行方
     */
    @TableField("issuer_name")
    private String issuerName;

    /**
     * 发行时间
     */
    @TableField("start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 审核（0待审核，1驳回，2通过）
     */
    @TableField("audit")
    private Integer audit;

    /**
     * 状态
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
