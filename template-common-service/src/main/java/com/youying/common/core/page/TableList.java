package com.youying.common.core.page;

import com.github.pagehelper.PageInfo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/2/24
 */
@Data
public class TableList<T> {

    private long total;

    private List<T> rows;

    public static <T> TableList<T> getTableList(List<T> list) {
        PageInfo pageInfo = new PageInfo<>(list);
        TableList tableList = new TableList();
        tableList.setRows(list);
        tableList.setTotal(pageInfo.getTotal());
        return tableList;
    }
}
