package com.youying.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 等级勋章表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-06
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_rank_medal")
public class RankMedal extends Model<RankMedal> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 等级勋章名称
     */
    @TableField("`name`")
    private String name;

    /**
     * 商家ID
     */
    @TableField(value = "merchant_id", fill = FieldFill.INSERT)
    private Long merchantId;

    /**
     * 剧场ID
     */
    @TableField("theater_id")
    private Long theaterId;

    /**
     * 剧目ID
     */
    @TableField("repertoire_id")
    private Long repertoireId;

    /**
     * 审核（0待审核，1驳回，2通过）
     */
    @TableField("audit")
    private Integer audit;

    /**
     * 审核通过时间
     */
    @TableField("audit_pass_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditPassTime;

    /**
     * 驳回原因
     */
    @TableField("reasons_rejection")
    private String reasonsRejection;

    /**
     * 状态
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
