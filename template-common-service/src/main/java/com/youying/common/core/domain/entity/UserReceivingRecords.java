package com.youying.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 用户数字藏品领取记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_user_receiving_records")
public class UserReceivingRecords extends Model<UserReceivingRecords> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 编号
     */
    @TableField("`no`")
    private String no;

    /**
     * 商品ID
     */
    @TableField("sku_id")
    private String skuId;

    /**
     * 空投ID
     */
    @TableField("drop_id")
    private String dropId;

    /**
     * 藏品编号
     */
    @TableField("collection_no")
    private String collectionNo;

    /**
     * 区块链发放序列ID
     */
    @TableField("send_id")
    private String sendId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 剧场ID
     */
    @TableField("theater_id")
    private Long theaterId;

    /**
     * 剧目ID
     */
    @TableField("repertoire_id")
    private Long repertoireId;

    /**
     * 剧目场次ID
     */
    @TableField("repertoire_info_detail_id")
    private Long repertoireInfoDetailId;

    /**
     * 剧目剧场关联ID
     */
    @TableField("repertoire_info_id")
    private Long repertoireInfoId;

    /**
     * 1：电子票、2：数字头像、3：纪念徽章、4：等级勋章 ID
     */
    @TableField("relation_id")
    private Long relationId;

    /**
     * 徽章类型（1：电子票、2：数字头像、3：纪念徽章、4：等级勋章）
     */
    @TableField("badge_type")
    private Integer badgeType;

    /**
     * 发行方
     */
    @TableField("issuer_name")
    private String issuerName;

    /**
     * 发放数量
     */
    @TableField("issued_quantity")
    private Integer issuedQuantity;

    /**
     * 发放时间
     */
    @TableField("start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 藏品路径
     */
    @TableField("image")
    private String image;

    /**
     * 升级藏品路径
     */
    @TableField("upgrade_image")
    private String upgradeImage;

    /**
     * 升级时间
     */
    @TableField("upgrade_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date upgradeTime;

    /**
     * 座位号
     */
    @TableField("seat_number")
    private String seatNumber;

    /**
     * 消费金额
     */
    @TableField("amount")
    private String amount;

    /**
     * 消费金额
     */
    @TableField("price")
    private BigDecimal price;

    /**
     * 演出时间
     */
    @TableField("`time`")
    private String time;

    /**
     * 评论ID
     */
    @TableField("comment_id")
    private Long commentId;

    /**
     * 追加评论ID
     */
    @TableField("two_comment_id")
    private Long twoCommentId;

    /**
     * 查看状态
     */
    @TableField("look_flag")
    private Integer lookFlag;

    /**
     * 佩戴状态
     */
    @TableField("adorn")
    private Integer adorn;

    /**
     * 支付编号
     */
    @TableField(value = "order_no", insertStrategy = FieldStrategy.IGNORED)
    private String orderNo;

    /**
     * 藏品组合标识
     */
    @TableField("portfolio_no")
    private String portfolioNo;

    /**
     * 藏品组合ID
     */
    @TableField("portfolio_id")
    private Long portfolioId;

    /**
     * 藏品组合ID
     */
    @TableField("portfolio_info_id")
    private Long portfolioInfoId;

    /**
     * 升级状态（0：待升级，1：已升级）
     */
    @TableField("upgrade_status")
    private Integer upgradeStatus;

    /**
     * 升级ID
     */
    @TableField(value = "order_id", insertStrategy = FieldStrategy.IGNORED)
    private Long orderId;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 扫描图片路径
     */
    @TableField("file_url")
    private String fileUrl;


    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
