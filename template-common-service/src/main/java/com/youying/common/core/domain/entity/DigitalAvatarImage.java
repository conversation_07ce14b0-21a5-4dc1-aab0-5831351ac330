package com.youying.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 数字头像图片表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-06
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_digital_avatar_image")
public class DigitalAvatarImage extends Model<DigitalAvatarImage> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商家ID
     */
    @TableField(value = "merchant_id", fill = FieldFill.INSERT)
    private Long merchantId;

    /**
     * 剧场ID
     */
    @TableField("theater_id")
    private Long theaterId;

    /**
     * 剧目ID
     */
    @TableField("repertoire_id")
    private Long repertoireId;

    /**
     * 数字头像ID
     */
    @TableField("digital_avatar_id")
    private Long digitalAvatarId;

    /**
     * 图片路径
     */
    @TableField("image")
    private String image;

    /**
     * 分组
     */
    @TableField("`group`")
    private Integer group;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
