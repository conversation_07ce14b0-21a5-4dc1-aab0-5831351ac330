package com.youying.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 纸质票扫描规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_scanning_info")
public class ScanningInfo extends Model<ScanningInfo> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 纸质票规则ID
     */
    @TableField("scanning_id")
    private Long scanningId;

    /**
     * 类型
     */
    @TableField("`type`")
    private Integer type;

    /**
     * key值
     */
    @TableField("`value`")
    private String value;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
