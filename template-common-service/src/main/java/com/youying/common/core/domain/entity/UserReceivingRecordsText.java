package com.youying.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 电子票演员信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-08
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_user_receiving_records_text")
public class UserReceivingRecordsText extends Model<UserReceivingRecordsText> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户藏品领取ID
     */
    @TableField("user_receiving_records_id")
    private Long userReceivingRecordsId;

    /**
     * 演员信息
     */
    @TableField("actor_information")
    private String actorInformation;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
