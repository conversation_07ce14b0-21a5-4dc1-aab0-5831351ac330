package com.youying.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 剧目演员表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-06
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_repertoire_actor")
public class RepertoireActor extends Model<RepertoireActor> {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 商家ID
     */
    @TableField(value = "merchant_id", fill = FieldFill.INSERT)
    private Long merchantId;

    /**
     * 剧目ID
     */
    @TableField("repertoire_id")
    private Long repertoireId;

    /**
     * 演员类型（1主演，2群演）
     */
    @TableField("actor_type")
    private Integer actorType;

    /**
     * 演员照片
     */
    @TableField("picture")
    private String picture;

    /**
     * 角色名称
     */
    @TableField("role_name")
    private String roleName;

    /**
     * 演员名称
     */
    @TableField("actor_name")
    private String actorName;

    /**
     * 群演名称,分隔（100）
     */
    @TableField("group_performance_name")
    private String groupPerformanceName;

    /**
     * 简介
     */
    @TableField("introduction")
    private String introduction;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
