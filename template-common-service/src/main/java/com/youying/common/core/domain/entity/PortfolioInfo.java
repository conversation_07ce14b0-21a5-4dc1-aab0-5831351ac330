package com.youying.common.core.domain.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 藏品组合表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-24
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_portfolio_info")
public class PortfolioInfo extends Model<PortfolioInfo> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 藏品组合ID
     */
    @TableField("portfolio_id")
    private Long portfolioId;

    /**
     * ocr编号
     */
    @TableField("ocr_no")
    private Long ocrNo;

    /**
     * 剧目剧场关联编号
     */
    @TableField("`no`")
    private String no;

    /**
     * 组合名称
     */
    @TableField("`name`")
    private String name;

    /**
     * 批次
     */
    @TableField("batch")
    private Integer batch;

    /**
     * 商家ID
     */
    @TableField("merchant_id")
    private Long merchantId;

    /**
     * 剧场ID
     */
    @TableField("theater_id")
    private Long theaterId;

    /**
     * 剧目ID
     */
    @TableField("repertoire_id")
    private Long repertoireId;

    /**
     * 电子票ID
     */
    @TableField("repertoire_ticket_id")
    private Long repertoireTicketId;

    /**
     * 数字头像ID
     */
    @TableField("digital_avatar_id")
    private Long digitalAvatarId;

    /**
     * 组合介绍
     */
    @TableField("introduction")
    private String introduction;

    /**
     * 发放数量
     */
    @TableField("issued_quantity")
    private Integer issuedQuantity;

    /**
     * 发放时间
     */
    @TableField("start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 免费发放
     */
    @TableField("free")
    private Integer free;

    /**
     * 价格
     */
    @TableField("price")
    private BigDecimal price;

    /**
     * 免责声明
     */
    @TableField("portfolio_statement_id")
    private Long portfolioStatementId;

    /**
     * 免责声明
     */
    @TableField("statement")
    private String statement;

    /**
     * 封面版式正面
     */
    @TableField("cover_front")
    private String coverFront;

    /**
     * 封面版式反面
     */
    @TableField("cover_reverse")
    private String coverReverse;

    /**
     * 普通电子票图片
     */
    @TableField("common_image")
    private String commonImage;

    /**
     * 普通数字头像
     */
    @TableField("digital_avatar_common_image")
    private String digitalAvatarCommonImage;

    /**
     * 是否删除
     */
    @TableField("status")
    private Integer status;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
