package com.youying.common.core.common;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023-06-28
 */
@Data
public class RepertoireTicketRes {
    private Long id;

    /**
     * 商品ID
     */
    private String skuId;

    /**
     * 空投ID
     */
    private String dropId;

    /**
     * 剧场ID
     */
    private Long theaterId;

    /**
     * 剧目ID
     */
    private Long repertoireId;

    /**
     * 封面版式正面
     */
    private String coverFront;

    /**
     * 封面版式反面
     */
    private String coverReverse;

    /**
     * 叠加处理图片
     */
    private String overlayImage;

    /**
     * 普通电子票图片
     */
    private String commonImage;

    /**
     * 发行方
     */
    private String issuerName;

    /**
     * 发放数量
     */
    private Integer issuedQuantity;

    /**
     * 发放时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 剧目封面图URL
     */
    private String repertoireCoverPicture;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
