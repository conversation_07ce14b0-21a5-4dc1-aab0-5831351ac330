package com.youying.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 用户藏品佩戴表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-25
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_user_adorn_collection")
public class UserAdornCollection extends Model<UserAdornCollection> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 徽章类型（1：电子票、2：数字头像、3：纪念徽章、4：等级勋章）
     */
    @TableField("badge_type")
    private Integer badgeType;

    /**
     * 用户数字藏品领取ID
     */
    @TableField("user_receiving_records_id")
    private Long userReceivingRecordsId;

    /**
     * 升级状态（0：待升级，1：已升级）
     */
    @TableField("upgrade_status")
    private Integer upgradeStatus;

    /**
     * 佩戴图像
     */
    @TableField("adorn_image")
    private String adornImage;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
