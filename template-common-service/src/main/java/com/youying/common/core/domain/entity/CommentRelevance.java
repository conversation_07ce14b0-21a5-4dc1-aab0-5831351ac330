package com.youying.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 用户评论关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-08
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_comment_relevance")
public class CommentRelevance extends Model<CommentRelevance> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 剧场评论ID
     */
    @TableField("comment_theater_id")
    private Long commentTheaterId;

    /**
     * 剧目评论ID
     */
    @TableField("comment_repertoire_id")
    private Long commentRepertoireId;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
