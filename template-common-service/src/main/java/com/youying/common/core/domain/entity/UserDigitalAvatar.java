package com.youying.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 用户电子头像表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-29
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_user_digital_avatar")
public class UserDigitalAvatar extends Model<UserDigitalAvatar> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 藏品组合ID
     */
    @TableField("portfolio_id")
    private Long portfolioId;

    /**
     * 藏品组合ID
     */
    @TableField("portfolio_info_id")
    private Long portfolioInfoId;

    /**
     * 电子头像ID
     */
    @TableField("digital_avatar_id")
    private Long digitalAvatarId;

    /**
     * 领取用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 用户领取ID
     */
    @TableField("user_receiving_records_id")
    private Long userReceivingRecordsId;

    /**
     * 订单ID
     */
    @TableField("order_id")
    private Long orderId;

    /**
     * 领取状态
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 合成图片
     */
    @TableField("image")
    private String image;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "UserDigitalAvatar{" +
                "id=" + id +
                ", portfolioId=" + portfolioId +
                ", userId=" + userId +
                ", userReceivingRecordsId=" + userReceivingRecordsId +
                ", orderId=" + orderId +
                ", status=" + status +
                ", image='" + image + '\'' +
                '}';
    }
}
