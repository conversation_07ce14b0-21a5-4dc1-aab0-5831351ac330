package com.youying.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 用户推送藏品表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-22
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_user_push_collection")
public class UserPushCollection extends Model<UserPushCollection> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 剧场ID
     */
    @TableField("theater_id")
    private Long theaterId;

    /**
     * 剧目ID
     */
    @TableField("repertoire_id")
    private Long repertoireId;

    /**
     * 等级勋章ID
     */
    @TableField("rank_medal_id")
    private Long rankMedalId;

    /**
     * 等级勋章详情ID
     */
    @TableField("rank_medal_info_id")
    private Long rankMedalInfoId;

    /**
     * 纪念徽章ID
     */
    @TableField("souvenir_badge_id")
    private Long souvenirBadgeId;

    /**
     * 领取状态
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 最大领取数(只针对纪念徽章)
     */
    @TableField("max_num")
    private Integer maxNum;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
