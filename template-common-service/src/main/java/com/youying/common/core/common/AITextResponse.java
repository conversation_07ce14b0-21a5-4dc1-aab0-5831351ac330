package com.youying.common.core.common;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-05-30
 */
@Data
public class AITextResponse implements Serializable {
    /**
     * 编号
     */
    private List<String> noList;

    /**
     * 编号
     */
    private String no;

    /**
     * 时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String dateTime;

    /**
     * 时间
     */
    private String time;

    /**
     * 时间
     */
    private String date = "";

    /**
     * 排
     */
    private String row = "";

    /**
     * 区域
     */
    private String area = "";

    /**
     * 座位
     */
    private String seat = "";

    /**
     * 地址
     */
    private String address = "";

    /**
     * 票价
     */
    private String price;

    /**
     * 剧目
     */
    private String repertoire;

    /**
     * 剧目短名称
     */
    private String shortRepertoireName;

    /**
     * 剧场
     */
    private String theater;

    /**
     * 剧目
     */
    private Long repertoireId;

    /**
     * 剧场
     */
    private Long theaterId;

    /**
     * 场次关联ID
     */
    private Long repertoireInfoId;

    /**
     * 场次关联详情ID
     */
    private Long repertoireInfoDetailId;

    /**
     * 电子票ID
     */
    private Long repertoireTicketId;

    /**
     * 数字头像ID
     */
    private Long digitalAvatarId;

    /**
     * 票序号
     */
    private String serialNumber;

    /**
     * 藏品组合ID
     */
    private Long portfolioId;

    /**
     * 藏品组合ID
     */
    private Long portfolioInfoId;

    /**
     * 封面版式正面
     */
    private String coverFront;

    /**
     * 封面版式反面
     */
    private String coverReverse;

    /**
     * 普通电子票图片
     */
    private String commonImage;

    /**
     * 普通数字头像
     */
    private String digitalAvatarCommonImage;

    /**
     * 发行方
     */
    private String issuerName;

    /**
     * 剧目封面图片
     */
    private String repertoireCoverPicture;

    /**
     * 数字头像
     */
    private DigitalAvatarBlockchainResponse digitalAvatarBlockchain;

    /**
     * 电子票
     */
    private List<RepertoireTicketRes> repertoireTicketList = new ArrayList<RepertoireTicketRes>();

    /**
     * 数字头像
     */
    private List<String> digitalAvatarUrlList = new ArrayList<String>();

    private List<String> timeList = new ArrayList<>();

    private String fileUrl;

    /**
     * 结构化票面文字
     */
    private String body;

    private String text;

    /**
     * 票面文字
     */
    private String textStr;

    private String requestId;

    /**
     * 电子票中间是否合成剧目名称
     */
    private Boolean ifCompositeRepertoireName;
}
