package com.youying.common.core.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-05-30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaiDuAIRequest implements Serializable {
    /**
     * 图像数据，base64编码后进行urlencode，要求base64编码和urlencode后大小不超过4M，
     * 最短边至少15px，最长边最大4096px，支持jpg/jpeg/png/bmp格式
     * 优先级：image > url > pdf_file，当image字段存在时，url、pdf_file字段失效
     */
    private String image;

    /**
     * 图片完整url，url长度不超过1024字节，url对应的图片base64编码后大小不超过4M，
     * 最短边至少15px，最长边最大4096px，支持jpg/jpeg/png/bmp格式
     * 优先级：image > url > pdf_file，当image字段存在时，url
     */
    private String url;
}
