package com.youying.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 剧目剧场评论
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-06
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_comment")
public class Comment extends Model<Comment> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商家ID
     */
    @TableField(value = "merchant_id", fill = FieldFill.INSERT)
    private Long merchantId;

    /**
     * 剧场ID (0为暂无)
     */
    @TableField("theater_id")
    private Long theaterId;

    /**
     * 剧目ID (0为暂无)
     */
    @TableField("repertoire_id")
    private Long repertoireId;

    /**
     * 剧目场次ID
     */
    @TableField("repertoire_info_detail_id")
    private Long repertoireInfoDetailId;

    /**
     * 商家用户ID
     */
    @TableField("user_merchant_id")
    private Long userMerchantId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 回复人ID
     */
    @TableField("reply_id")
    private Long replyId;

    /**
     * 评论ID （父级）
     */
    @TableField("parent_id")
    private Long parentId;

    /**
     * 评论ID （父级）
     */
    @TableField("comment_parent_id")
    private Long commentParentId;

    /**
     * 内容
     */
    @TableField("content")
    private String content;

    /**
     * 剧场评论
     */
    @TableField("theater_content")
    private String theaterContent;

    /**
     * 评论时间
     */
    @TableField("comment_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date commentTime;

    /**
     * 评分
     */
    @TableField("grade")
    private Integer grade;

    /**
     * 仅自己可见
     */
    @TableField("visible")
    private Integer visible;

    /**
     * 置顶
     */
    @TableField("top")
    private Integer top;

    /**
     * 查看状态
     */
    @TableField("look_flag")
    private Integer lookFlag;

    /**
     * IP地址
     */
    @TableField("ip_address")
    private String ipAddress;

    /**
     * 状态
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 剧目商家回复状态
     */
    @TableField("`repertoire_reply_status`")
    private Integer repertoireReplyStatus;

    /**
     * 剧场商家回复状态
     */
    @TableField("`theater_reply_status`")
    private Integer theaterReplyStatus;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
