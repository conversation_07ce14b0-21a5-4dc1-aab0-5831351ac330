package com.youying.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 剧目剧场关联编号表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_theater_repertoire_no")
public class TheaterRepertoireNo extends Model<TheaterRepertoireNo> {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 编号
     */
    @TableField("`no`")
    private String no;

    /**
     * 剧场ID
     */
    @TableField("theater_id")
    private Long theaterId;

    /**
     * 剧目ID
     */
    @TableField("repertoire_id")
    private Long repertoireId;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
