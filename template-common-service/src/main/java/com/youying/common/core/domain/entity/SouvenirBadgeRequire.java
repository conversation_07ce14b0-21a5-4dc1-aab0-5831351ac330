package com.youying.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 纪念徽章领取规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-22
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_souvenir_badge_require")
public class SouvenirBadgeRequire extends Model<SouvenirBadgeRequire> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商家ID
     */
    @TableField("merchant_id")
    private Long merchantId;

    /**
     * 剧场ID
     */
    @TableField("theater_id")
    private Long theaterId;

    /**
     * 剧目ID (0为暂无)
     */
    @TableField("repertoire_id")
    private Long repertoireId;

    /**
     * 纪念徽章ID
     */
    @TableField("souvenir_badge_id")
    private Long souvenirBadgeId;

    /**
     * 等级勋章（大于等于设置等级）
     */
    @TableField("rank_medal_info_id")
    private Long rankMedalInfoId;

    /**
     * 观影次数
     */
    @TableField("look_number")
    private Integer lookNumber;

    /**
     * 扫描电子票中含有设置的剧场信息次数（剧目ID）电子票扫描场次
     */
    @TableField("repertoire_info_detail_id")
    private Long repertoireInfoDetailId;

    /**
     * 观看剧场演出（剧目剧场ID）观看某场次
     */
    @TableField("repertoire_info_id")
    private Long repertoireInfoId;

    /**
     * 观看时间段
     */
    @TableField("start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 观看时间段
     */
    @TableField("end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 时间段内观看次数
     */
    @TableField("time_look_number")
    private Integer timeLookNumber;

    /**
     * 指定名单发放（id拼接）
     */
    @TableField("white_list")
    private String whiteList;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
