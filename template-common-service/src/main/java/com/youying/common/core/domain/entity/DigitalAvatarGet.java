package com.youying.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-30
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_digital_avatar_get")
public class DigitalAvatarGet extends Model<DigitalAvatarGet> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数字头像ID
     */
    @TableField("digital_avatar_id")
    private Long digitalAvatarId;

    /**
     * 扫描票据编号
     */
    @TableField("`no`")
    private String no;

    /**
     * 图片路径
     */
    @TableField("image")
    private String image;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 用户扫码ID
     */
    @TableField("user_look_id")
    private Long userLookId;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
