package com.youying.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 用户设置表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-06
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_user_setting")
public class UserSetting extends Model<UserSetting> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 评论通知
     */
    @TableField("comment_notify")
    private Integer commentNotify;

    /**
     * 点赞通知
     */
    @TableField("well_notify")
    private Integer wellNotify;

    /**
     * 提问通知
     */
    @TableField("issue_notify")
    private Integer issueNotify;

    /**
     * 取群发消息提示
     */
    @TableField("group_message_notify")
    private Integer groupMessageNotify;

    /**
     * 消息提示
     */
    @TableField("message_notify")
    private Integer messageNotify;

    /**
     * 欢迎语提示
     */
    @TableField("welcome_notify")
    private Integer welcomeNotify;

    /**
     * 关注账号更新
     */
    @TableField("like_notify")
    private Integer likeNotify;

    /**
     * 电子票夹
     */
    @TableField("electronic_ticket")
    private Integer electronicTicket;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
