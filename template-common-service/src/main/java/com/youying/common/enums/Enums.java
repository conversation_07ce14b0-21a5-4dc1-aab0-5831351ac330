package com.youying.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2023/5/12
 */
@SuppressWarnings("all")
public class Enums {
    /**
     * 状态
     */
    @Getter
    @AllArgsConstructor
    public enum StatusFlag {
        OK(1, "正常"),
        PROHIBITION(0, "禁用");

        private final Integer code;
        private final String value;

        public static Integer toggleStatus(Integer status) {
            if (OK.getCode().equals(status)) {
                return PROHIBITION.getCode();
            }
            return OK.getCode();
        }
    }

    /**
     * 删除状态
     */
    @Getter
    @AllArgsConstructor
    public enum DeleteFlag {
        OK(1, "正常"),
        DELETE(0, "删除");

        private final Integer code;
        private final String value;
    }

    /**
     * 审核状态
     */
    @Getter
    @AllArgsConstructor
    public enum AuditFlag {
        WAIT(0, "待处理"),
        REJECTED(1, "禁用"),
        PASS(2, "通过");

        private final Integer code;
        private final String value;
    }

    /**
     * 企业类型
     */
    @Getter
    @AllArgsConstructor
    public enum MerchantCategoryFlag {
        REPERTOIRE(1, "剧目"),
        THEATER(2, "剧场");

        private final Integer code;
        private final String value;
    }

    /**
     * 推荐状态
     */
    @Getter
    @AllArgsConstructor
    public enum RecommendFlag {
        RECOMMEND(1, "推荐"),
        DEFAULT(0, "默认");

        private final Integer code;
        private final String value;
    }

    /**
     * 关联状态
     */
    @Getter
    @AllArgsConstructor
    public enum RelevanceFlag {
        WAIT(0, "待确认"),
        PASS(1, "已确认"),
        CANCEL(2, "已取消");

        private final Integer code;
        private final String value;
    }

    /**
     * 性别状态
     */
    @Getter
    @AllArgsConstructor
    public enum SexFlag {
        UNKNOWN(0, "未知"),
        WOMAN(1, "女"),
        MAN(2, "男");

        private final Integer code;
        private final String value;
    }

    /**
     * 数字藏品类型
     */
    @Getter
    @AllArgsConstructor
    public enum BadgeTypeFlag {
        ELECTRONIC_TICKET(1, "电子票"),
        DIGITAL_AVATAR(2, "数字头像"),
        SOUVENIR_BADGE(3, "纪念徽章"),
        RANK_MEDAL(4, "等级勋章");

        private final Integer code;
        private final String value;
    }

    /**
     * 端口
     */
    @Getter
    @AllArgsConstructor
    public enum PortFlag {
        USER(2, "用户"),
        MERCHANT(1, "商家");

        private final Integer code;
        private final String value;
    }

    /**
     * 用户交互标志
     */
    @Getter
    @AllArgsConstructor
    public enum UserInteractionFlag {
        COMMENT(1, "评论"),
        COMMENT_KUDOS(2, "评论点赞"),
        ISSUE(3, "提问"),
        DYNAMIC_KUDOS(4, "动态点赞"),
        COMMENT_SHARE(5, "评论转发"),
        COMMENT_FOLLOW(6, "评论关注");

        private final Integer code;
        private final String value;
    }

    /**
     * 用户查看状态
     */
    @Getter
    @AllArgsConstructor
    public enum LookFlag {
        DEFAULT(0, "默认"),
        PASS(1, "已看");

        private final Integer code;
        private final String value;
    }

    /**
     * 支付状态
     */
    @Getter
    @AllArgsConstructor
    public enum PayStatusFlag {
        WITH(0, "待支付"),
        OK(1, "已支付"),
        CANCEL(2, "已取消"),
        REFUND(3, "已退款"),
        ;

        private final Integer code;
        private final String value;
    }

    /**
     * 支付查询类型
     */
    @Getter
    @AllArgsConstructor
    public enum OrderQueryType {
        TRANSACTION_ID(1, "微信支付回调流水号"),
        ORDER_NO(2, "商家支付编号");

        private final Integer code;
        private final String value;
    }

    /**
     * 支付查询类型
     */
    @Getter
    @AllArgsConstructor
    public enum FreeFlag {
        FREE(1, "免费"),
        CHARGING(0, "收费");

        private final Integer code;
        private final String value;
    }

    /**
     * 纸质票默认类型
     */
    @Getter
    @AllArgsConstructor
    public enum DefaultFlag {
        DEFAULT(1, "默认"),
        SPECIAL(0, "特殊");

        private final Integer code;
        private final String value;
    }

    /**
     * 数据类型
     */
    @Getter
    @AllArgsConstructor
    public enum DataFlag {
        USER(1, "用户"),
        ADMIN(0, "系统");

        private final Integer code;
        private final String value;
    }
}
