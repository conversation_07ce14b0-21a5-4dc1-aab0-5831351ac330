package com.youying.common.enums;

/**
 * 票类型枚举
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
public enum TicketTypeEnum {

    /**
     * 普通票 - 对应具体剧目和剧场，需要审核
     */
    NORMAL(1, "普通票"),

    /**
     * 升级票 - 对应具体剧目和剧场，需要审核
     */
    UPGRADE(2, "升级票"),

    /**
     * 随机票 - 不对应任何剧目和剧场，不需要审核
     */
    RANDOM(3, "随机票");

    private final Integer code;
    private final String description;

    TicketTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举
     */
    public static TicketTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TicketTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 判断是否为随机票
     */
    public static boolean isRandomTicket(Integer ticketType) {
        return RANDOM.getCode().equals(ticketType);
    }

    /**
     * 判断是否需要审核
     */
    public static boolean needsAudit(Integer ticketType) {
        return !isRandomTicket(ticketType);
    }
}
