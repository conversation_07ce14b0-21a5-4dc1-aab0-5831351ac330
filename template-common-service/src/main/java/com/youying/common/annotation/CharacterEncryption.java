package com.youying.common.annotation;

import com.youying.common.enums.EncryptionMode;
import com.youying.common.enums.EncryptionType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 字符串加密解密 加密字段返回格式，
 * 存在加密字段类型 true 字段加密+分段加密，以$分割
 *
 * <AUTHOR>
 * @Date 2023/4/12
 */

@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface CharacterEncryption {
    /**
     * 加密模式
     *
     * @return
     */
    EncryptionMode encryptionMode() default EncryptionMode.ASC;

    /**
     * 加密字段类型
     * <p>
     * 加密数字拼接
     *
     * @return
     */
    EncryptionType encryptionType();
}
