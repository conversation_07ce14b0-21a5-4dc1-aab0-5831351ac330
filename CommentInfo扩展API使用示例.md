# CommentInfo扩展API使用示例

## 概述

CommentInfo的addOrUpdate方法已扩展支持转发和关注功能。现在支持以下操作类型：

- `0`: 踩
- `1`: 赞  
- `2`: 转发
- `3`: 关注

## 核心逻辑

**重要特性：如果已经有了就代表本次请求是删除，没有表示新增**

这意味着：
- 第一次调用某个type：新增该类型的互动记录
- 第二次调用相同type：删除该类型的互动记录（取消操作）

## API调用示例

### 1. 点赞操作

#### 第一次点赞（新增）
```bash
curl -X POST http://localhost:8112/commentInfo/addOrUpdate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "commentId": 123,
    "userId": 456,
    "type": 1
  }'
```

#### 第二次点赞（取消）
```bash
curl -X POST http://localhost:8112/commentInfo/addOrUpdate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "commentId": 123,
    "userId": 456,
    "type": 1
  }'
```

### 2. 踩操作

#### 第一次踩（新增）
```bash
curl -X POST http://localhost:8112/commentInfo/addOrUpdate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "commentId": 123,
    "userId": 456,
    "type": 0
  }'
```

#### 第二次踩（取消）
```bash
curl -X POST http://localhost:8112/commentInfo/addOrUpdate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "commentId": 123,
    "userId": 456,
    "type": 0
  }'
```

### 3. 转发操作（新功能）

#### 第一次转发（新增）
```bash
curl -X POST http://localhost:8112/commentInfo/addOrUpdate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "commentId": 123,
    "userId": 456,
    "type": 2
  }'
```

#### 第二次转发（取消）
```bash
curl -X POST http://localhost:8112/commentInfo/addOrUpdate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "commentId": 123,
    "userId": 456,
    "type": 2
  }'
```

### 4. 关注操作（新功能）

#### 第一次关注（新增）
```bash
curl -X POST http://localhost:8112/commentInfo/addOrUpdate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "commentId": 123,
    "userId": 456,
    "type": 3
  }'
```

#### 第二次关注（取消）
```bash
curl -X POST http://localhost:8112/commentInfo/addOrUpdate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "commentId": 123,
    "userId": 456,
    "type": 3
  }'
```

### 5. 删除所有互动记录

```bash
curl -X POST http://localhost:8112/commentInfo/addOrUpdate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "commentId": 123,
    "userId": 456,
    "type": null
  }'
```

## 前端JavaScript示例

### 点赞/取消点赞
```javascript
async function toggleLike(commentId) {
  try {
    const response = await fetch('/commentInfo/addOrUpdate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        commentId: commentId,
        type: 1  // 点赞
      })
    });
    
    const result = await response.json();
    if (result.code === 200) {
      console.log('点赞操作成功');
      // 更新UI状态
      updateLikeButton(commentId);
    }
  } catch (error) {
    console.error('点赞操作失败:', error);
  }
}
```

### 转发/取消转发
```javascript
async function toggleShare(commentId) {
  try {
    const response = await fetch('/commentInfo/addOrUpdate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        commentId: commentId,
        type: 2  // 转发
      })
    });
    
    const result = await response.json();
    if (result.code === 200) {
      console.log('转发操作成功');
      // 更新UI状态
      updateShareButton(commentId);
    }
  } catch (error) {
    console.error('转发操作失败:', error);
  }
}
```

### 关注/取消关注
```javascript
async function toggleFollow(commentId) {
  try {
    const response = await fetch('/commentInfo/addOrUpdate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        commentId: commentId,
        type: 3  // 关注
      })
    });
    
    const result = await response.json();
    if (result.code === 200) {
      console.log('关注操作成功');
      // 更新UI状态
      updateFollowButton(commentId);
    }
  } catch (error) {
    console.error('关注操作失败:', error);
  }
}
```

## 业务逻辑说明

### 1. 独立性
每种类型的操作都是独立的：
- 用户可以同时点赞、转发、关注同一条评论
- 取消其中一种操作不会影响其他操作

### 2. 通知机制
- **点赞、转发、关注**：会向评论作者发送通知
- **踩**：不会发送通知
- **自己操作自己的评论**：不会发送通知

### 3. 数据一致性
- 每个用户对每条评论的每种操作类型只能有一条记录
- 重复操作会自动切换状态（新增/删除）

### 4. 权限控制
- 需要用户登录才能进行操作
- 系统会自动获取当前登录用户的ID

## 错误处理

### 常见错误码
- `200`: 操作成功
- `401`: 未登录或token无效
- `403`: 权限不足
- `404`: 评论不存在
- `500`: 服务器内部错误

### 错误响应示例
```json
{
  "code": 404,
  "msg": "评论不存在",
  "data": null
}
```

## 数据库变化

### t_comment_info表
- 现在支持type值：0(踩)、1(赞)、2(转发)、3(关注)
- 每个用户对每条评论的每种类型只能有一条记录

### t_user_interaction表
- 新增转发通知记录（type=5）
- 新增关注通知记录（type=6）

## 测试建议

1. **功能测试**：验证每种操作的新增/删除逻辑
2. **独立性测试**：验证不同操作类型的独立性
3. **通知测试**：验证通知发送的正确性
4. **权限测试**：验证用户权限控制
5. **并发测试**：验证高并发情况下的数据一致性

## 注意事项

1. **前端状态管理**：前端需要正确维护按钮状态，避免用户困惑
2. **防重复提交**：建议前端添加防重复提交机制
3. **网络异常处理**：需要处理网络异常情况下的状态恢复
4. **用户体验**：操作应该有明确的视觉反馈
