package com.youying.framework.web.service;

import com.youying.common.constant.CacheConstants;
import com.youying.common.constant.Constants;
import com.youying.common.core.redis.RedisCache;
import com.youying.common.exception.user.UserPasswordNotMatchException;
import com.youying.common.exception.user.UserPasswordRetryLimitExceedException;
import com.youying.common.utils.MessageUtils;
import com.youying.framework.manager.AsyncManager;
import com.youying.framework.manager.factory.AsyncFactory;
import com.youying.framework.security.context.AuthenticationContextHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 登录密码方法
 *
 * <AUTHOR>
 */
@Component
public class SysPasswordService {
    @Autowired
    private RedisCache redisCache;

    @Value(value = "${user.password.maxRetryCount}")
    private int maxRetryCount;

    @Value(value = "${user.password.lockTime}")
    private int lockTime;

    /**
     * 登录账户密码错误次数缓存键名
     *
     * @param username 用户名
     * @return 缓存键key
     */
    private String getCacheKey(String username) {
        return CacheConstants.PWD_ERR_CNT_KEY + username;
    }

    /**
     * 密码错误，记录错误登录次数
     */
    public void loginExceptionRetryCountAdd() {
        String username = getLoginUserName();
        Integer retryCount = getLoginRetryCount(username);
        retryCount = retryCount + 1;
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL,
                MessageUtils.message("user.password.retry.limit.count", retryCount)));
        redisCache.setCacheObject(getCacheKey(username), retryCount, lockTime, TimeUnit.MINUTES);
        throw new UserPasswordNotMatchException();
    }

    /**
     * 检测错误密码登录是否超过阈值
     *
     * @param username
     */
    public void validate(String username) {
        int retryCount = getLoginRetryCount(username);
        if (retryCount >= Integer.valueOf(maxRetryCount).intValue()) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL,
                    MessageUtils.message("user.password.retry.limit.exceed", maxRetryCount, lockTime)));
            throw new UserPasswordRetryLimitExceedException(maxRetryCount, lockTime);
        }
    }

    /**
     * 获取登录重计次数
     *
     * @return
     */
    public Integer getLoginRetryCount(String username) {
        Integer retryCount = redisCache.getCacheObject(getCacheKey(username));
        if (retryCount == null) {
            retryCount = 0;
        }
        return retryCount;
    }

    /**
     * 获取登录重计次数
     *
     * @return
     */
    public String getLoginUserName() {
        Authentication usernamePasswordAuthenticationToken = AuthenticationContextHolder.getContext();
        return usernamePasswordAuthenticationToken.getName();
    }

    /**
     * 登录重计次数清零
     *
     * @param loginName
     */
    public void clearLoginRecordCache(String loginName) {
        if (redisCache.hasKey(getCacheKey(loginName))) {
            redisCache.deleteObject(getCacheKey(loginName));
        }
    }
}
