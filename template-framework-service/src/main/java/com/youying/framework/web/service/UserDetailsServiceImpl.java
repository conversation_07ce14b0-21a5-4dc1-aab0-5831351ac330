package com.youying.framework.web.service;

import com.youying.common.core.domain.entity.User;
import com.youying.common.core.domain.model.LoginUser;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.common.enums.UserStatus;
import com.youying.common.exception.ServiceException;
import com.youying.common.utils.StringUtils;
import com.youying.system.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

/**
 * 用户验证处理
 *
 * <AUTHOR>
 */
@Service
public class UserDetailsServiceImpl implements UserDetailsService {
    private static final Logger log = LoggerFactory.getLogger(UserDetailsServiceImpl.class);

    @Autowired
    private UserService userService;

    @Override
    public UserDetails loadUserByUsername(String phone) throws UsernameNotFoundException {
        User user = userService.findUserByPhone(phone);
        if (StringUtils.isNull(user)) {
            log.info("登录用户：{} 不存在.", user.getName());
            throw new ServiceException("登录用户：" + user.getName() + " 不存在");
        } else if (UserStatus.DELETED.getCode().equals(user.getDeleted())) {
            log.info("登录用户：{} 已被删除.", user.getName());
            throw new ServiceException("对不起，您的账号：" + user.getName() + " 已被删除");
        } else if (StatusFlag.PROHIBITION.getCode().equals(user.getStatus())) {
            log.info("登录用户：{} 已被停用.", user.getName());
            throw new ServiceException("对不起，您的账号：" + user.getName() + " 已停用");
        }
        return createLoginUser(user);
    }

    public UserDetails createLoginUser(User user) {
        return new LoginUser(user.getId(), user.getName(), user);
    }
}
