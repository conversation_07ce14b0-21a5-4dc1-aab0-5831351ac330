package com.youying.framework.imgagediscern;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.ocr.v20181119.OcrClient;
import com.tencentcloudapi.ocr.v20181119.models.GroupInfo;
import com.tencentcloudapi.ocr.v20181119.models.ItemInfo;
import com.tencentcloudapi.ocr.v20181119.models.LineInfo;
import com.tencentcloudapi.ocr.v20181119.models.SmartStructuralOCRV2Request;
import com.tencentcloudapi.ocr.v20181119.models.SmartStructuralOCRV2Response;
import com.youying.common.core.common.AITextResponse;
import com.youying.common.core.domain.entity.ScanningInfo;
import com.youying.common.exception.ServiceException;
import com.youying.common.utils.StringUtils;
import com.youying.common.utils.spring.SpringUtils;
import com.youying.system.service.ScanningInfoService;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2023-10-25
 */
@Slf4j
@Component
public class TencentAi implements DiscernStrategy {

    private static final String SECRET_ID = "AKIDymZRQKYt9C6qT5iOxJRi2GThqsmaJ1hJ";
    private static final String SECRET_KEY = "gHk5oXBWvGOMF0JPURBpU4RbdXKwY9nN";
    List<ScanningInfo> imageRuleList = new ArrayList<>();
    private AITextResponse aiText = new AITextResponse();
    private Map<String, ScanningInfo> scanningInfoMap = new HashMap<String, ScanningInfo>();

    @Override
    public AITextResponse recognition(String ticketText) {
        return null;
    }

    /**
     * @param imageBase64Str
     * @return
     */
    @Override
    public AITextResponse imageRecognition(String imageBase64Str) {
        return null;
    }

    /**
     * 腾讯图片智能化识别
     *
     * @param imageBase64Str
     * @param scanningId
     * @return
     */
    @Override
    public AITextResponse imageRecognition(String imageBase64Str, Long scanningId) {
        try {
            Credential cred = new Credential(SECRET_ID, SECRET_KEY);
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("ocr.tencentcloudapi.com");
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            OcrClient client = new OcrClient(cred, "ap-shanghai", clientProfile);
            SmartStructuralOCRV2Request req = new SmartStructuralOCRV2Request();

            getImageRule(scanningId);
            String[] ruleStrList = imageRuleList.stream().map(ScanningInfo::getValue).toArray(String[]::new);

            req.setImageBase64(imageBase64Str);
            req.setItemNames(ruleStrList);
            req.setReturnFullText(true);

            SmartStructuralOCRV2Response resp = client.SmartStructuralOCRV2(req);
            GroupInfo[] respDateList = resp.getStructuralList();
            String text = JSON.toJSONString(resp.getWordList());

            aiText.setTextStr(getTextStr(text));
            aiText.setText(text);
            aiText.setBody(JSON.toJSONString(resp.getStructuralList()));
            aiText.setRequestId(resp.getRequestId());
            return getTencentAiResponse(respDateList);
        } catch (TencentCloudSDKException e) {
            log.error("{} --> 腾讯智能化识别异常：{}", DateUtil.now(), e.getMessage());
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 获取腾讯识别内容
     *
     * @param respDateList
     * @return
     */
    private AITextResponse getTencentAiResponse(GroupInfo[] respDateList) {
        if (respDateList == null || respDateList.length == 0) {
            return aiText;
        }

        for (GroupInfo info : respDateList) {
            if (info.getGroups() != null) {
                for (LineInfo lineInfo : info.getGroups()) {
                    if (lineInfo.getLines() != null) {
                        for (ItemInfo itemInfo : lineInfo.getLines()) {
                            String autoName = itemInfo.getKey().getAutoName();
                            String autoContent = itemInfo.getValue().getAutoContent();
                            extractInformation(autoName, autoContent);
                        }
                    }
                }
            }
        }
        // 后处理人工智能文本
        postProcessAiText();
        return aiText;
    }

    /**
     * 根据规则设置对应值
     *
     * @param autoName
     * @param autoContent
     */
    private void extractInformation(String autoName, String autoContent) {
        ScanningInfo scanningInfo = scanningInfoMap.get(autoName);
        if (scanningInfo != null) {
            switch (scanningInfo.getType()) {
                case 1:
                    aiText.setDateTime(replaceTime(autoContent));
                    aiText.getTimeList().add(replaceTime(autoContent));
                    break;
                case 2:
                    aiText.setDate(replaceTime(autoContent));
                    aiText.getTimeList().add(replaceTime(autoContent));
                    break;
                case 3:
                    aiText.setTime(replaceTime(autoContent));
                    aiText.getTimeList().add(replaceTime(autoContent));
                    break;
                case 4:
                    if (aiText.getAddress().contains(autoContent)) {
                        break;
                    }
                    aiText.setArea(aiText.getArea() + autoContent);
                    aiText.setAddress(aiText.getAddress() + aiText.getArea());
                    break;
                case 5:
                    if (aiText.getAddress().contains(autoContent)) {
                        break;
                    }
                    aiText.setSeat(aiText.getSeat() + autoContent);
                    aiText.setAddress(aiText.getAddress() + aiText.getSeat());
                    break;
                case 6:
                    if (aiText.getAddress().contains(autoContent)) {
                        break;
                    }
                    aiText.setRow(aiText.getRow() + autoContent);
                    aiText.setAddress(aiText.getAddress() + aiText.getRow());
                    break;
                case 7:
                    aiText.setPrice(replacePrice(autoContent));
                    break;
                default:
                    log.error("{} 图片识别规则设置错误，无此类型 --> ", DateUtil.now());
                    throw new ServiceException("图片识别规则设置错误，无此类型");
            }
        }

    }

    /**
     * 获取规则
     *
     * @param scanningId
     * @return
     */
    private void getImageRule(Long scanningId) {
        ScanningInfoService scanningInfoService = SpringUtils.getBean("scanningInfoServiceImpl");
        imageRuleList = scanningInfoService.list(new LambdaQueryWrapper<ScanningInfo>()
                .eq(ScanningInfo::getScanningId, scanningId));
        if (CollectionUtils.isEmpty(imageRuleList)) {
            log.error("{} 腾讯图片识别规则不存在 --> ", DateUtil.now());
            throw new ServiceException("腾讯图片识别规则不存在");
        }
        scanningInfoMap = imageRuleList.stream().collect(Collectors.toMap(ScanningInfo::getValue, Function.identity()));
    }

    /**
     * 优化返回结果
     */
    public void postProcessAiText() {
        ensureSeatAndRowFormat();
        combineAreaWithSeatRow();
        extractDateTimeFromTimeList();
    }

    private void ensureSeatAndRowFormat() {
        if (StringUtils.isNotBlank(aiText.getSeat()) && !aiText.getAddress().contains("座")
                && !aiText.getAddress().contains("号")) {
            aiText.setSeat(aiText.getSeat() + "座");
        }
        if (StringUtils.isNotBlank(aiText.getRow()) && !aiText.getAddress().contains("排")) {
            aiText.setRow(aiText.getRow() + "排");
        }
    }

    private void combineAreaWithSeatRow() {
        if (StringUtils.isNotBlank(aiText.getArea())) {
            aiText.setSeat(aiText.getArea() + aiText.getRow() + aiText.getSeat());
        }
    }

    private void extractDateTimeFromTimeList1() {
        int flag = 0;
        for (String time : aiText.getTimeList()) {
            if (time.length() == 4) {
                aiText.setTime(time);
                flag++;
            }
            if (time.length() > 4 && time.length() <= 8) {
                aiText.setDate(time);
                flag++;
            }
        }
        if (flag == 2) {
            aiText.getTimeList().add(aiText.getDate() + aiText.getTime());
        }
        if (aiText.getTime() != null && aiText.getDate() != null) {
            aiText.getTimeList().add(aiText.getDate() + aiText.getTime());
        }
    }

    private void extractDateTimeFromTimeList() {
        List<String> timeList = new ArrayList<>();
        List<String> timeLists = aiText.getTimeList();
        for (String timeStr : timeLists) {
            for (String timeStrY : timeLists) {
                if ((timeStr + timeStrY).length() >= 6 && (timeStr + timeStrY).length() <= 14) {
                    timeList.add(timeStr + timeStrY);
                    timeList.add(timeStrY + timeStr);
                }
                timeList.add(timeStr);
            }
        }
        aiText.setTimeList(timeList);
    }

    /**
     * 正则表达式只保留数字
     *
     * @param value
     * @return
     */
    private String replaceTime(String value) {
        return value.replaceAll("[^0-9]", "");
    }

    /**
     * 正则表达式去除人民币符号
     *
     * @param value
     * @return
     */
    private String replacePrice(String value) {
        return value.replaceAll("¥|￥", "");
    }

    public String getTextStr(String textStr) {
        // 正则表达式匹配'detectedText:"任意不包含双引号的字符序列"'
        Pattern pattern = Pattern.compile("\"detectedText\":\"(.*?)\"");
        Matcher matcher = pattern.matcher(textStr);
        StringBuilder str = new StringBuilder();
        while (matcher.find()) {
            str.append(matcher.group(1));
        }
        return str.toString();
    }
}
