package com.youying.framework.imgagediscern;

import com.youying.common.core.common.AITextResponse;

/**
 * <AUTHOR>
 * @since 2023-10-25
 */
public interface DiscernStrategy {

    /**
     * 票面识别
     * 
     * @param ticketText
     * @return
     */
    AITextResponse recognition(String ticketText);

    /**
     * 图像识别
     *
     * @param imageBase64Str
     * @return
     */
    AITextResponse imageRecognition(String imageBase64Str);

    /**
     * 图像识别
     *
     * @param imageBase64Str
     * @param scanningId
     * @return
     */
    AITextResponse imageRecognition(String imageBase64Str, Long scanningId);
}
