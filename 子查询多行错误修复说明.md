# 子查询多行错误修复说明

## 问题描述

在实现评论详情接口时遇到了SQL错误：

```
Subquery returns more than 1 row
```

这个错误发生在以下子查询中：
```sql
( SELECT `type` FROM t_comment_info WHERE comment_id = c.id AND user_id = ? ) AS kudosStatus
```

## 错误原因

随着CommentInfo功能的扩展，现在支持多种操作类型：
- `0`: 踩
- `1`: 赞
- `2`: 转发
- `3`: 关注

当一个用户对同一条评论进行了多种操作（比如既点赞又转发），`t_comment_info`表中会有多条记录，导致子查询返回多行数据，而SQL期望只返回一行。

## 修复方案

### 1. 使用GROUP_CONCAT聚合函数

**修复前：**
```sql
( SELECT `type` FROM t_comment_info WHERE comment_id = c.id AND user_id = #{userId} ) AS kudosStatus
```

**修复后：**
```sql
( SELECT GROUP_CONCAT(`type`) FROM t_comment_info WHERE comment_id = c.id AND user_id = #{userId} ) AS kudosStatus
```

这样会返回逗号分隔的字符串，如："1,2,3"

### 2. 添加独立的布尔字段

为了更好的前端使用体验，我们添加了独立的布尔字段：

```sql
( SELECT COUNT(1) > 0 FROM t_comment_info WHERE comment_id = c.id AND user_id = #{userId} AND `type` = 1 ) AS isLiked,
( SELECT COUNT(1) > 0 FROM t_comment_info WHERE comment_id = c.id AND user_id = #{userId} AND `type` = 0 ) AS isDisliked,
( SELECT COUNT(1) > 0 FROM t_comment_info WHERE comment_id = c.id AND user_id = #{userId} AND `type` = 2 ) AS isShared,
( SELECT COUNT(1) > 0 FROM t_comment_info WHERE comment_id = c.id AND user_id = #{userId} AND `type` = 3 ) AS isFollowed
```

## 修复的SQL查询

### 评论详情查询修复
```sql
SELECT
    c.id,
    c.user_id,
    u.`name` AS userName,
    u.avatar AS userAvatar,
    c.ip_address,
    t.address AS location,
    r.`name` AS repertoireName,
    t.`name` AS theaterName,
    CONCAT(DATE_FORMAT(rid.start_time, '%Y-%m-%d %H:%i'), '-', DATE_FORMAT(rid.end_time, '%H:%i')) AS sessionName,
    COALESCE(r.cover_picture, MAX(urr.image), MAX(urr.file_url)) AS commentImage,
    r.cover_picture AS repertoireCoverPicture,
    t.cover_picture AS theaterCoverPicture,
    ( CASE '0' WHEN c.deleted THEN '' WHEN c.`status` THEN '' ELSE c.content END ) AS content,
    ( CASE '0' WHEN c.deleted THEN '' WHEN c.`status` THEN '' ELSE c.theater_content END ) AS theaterContent,
    c.comment_time AS createTime,
    ( SELECT COUNT(1) FROM t_comment_info WHERE comment_id = c.id AND `type` = 1 ) AS likeCount,
    ( SELECT COUNT(1) FROM t_comment_info WHERE comment_id = c.id AND `type` = 2 ) AS shareCount,
    ( SELECT COUNT(1) FROM t_comment_info WHERE comment_id = c.id AND `type` = 3 ) AS followCount,
    COUNT( DISTINCT c1.id ) AS replyCount,
    ( SELECT GROUP_CONCAT(`type`) FROM t_comment_info WHERE comment_id = c.id AND user_id = #{userId} ) AS kudosStatus,
    ( SELECT COUNT(1) > 0 FROM t_comment_info WHERE comment_id = c.id AND user_id = #{userId} AND `type` = 1 ) AS isLiked,
    ( SELECT COUNT(1) > 0 FROM t_comment_info WHERE comment_id = c.id AND user_id = #{userId} AND `type` = 0 ) AS isDisliked,
    ( SELECT COUNT(1) > 0 FROM t_comment_info WHERE comment_id = c.id AND user_id = #{userId} AND `type` = 2 ) AS isShared,
    ( SELECT COUNT(1) > 0 FROM t_comment_info WHERE comment_id = c.id AND user_id = #{userId} AND `type` = 3 ) AS isFollowed,
    c.visible,
    c.deleted,
    c.`status`,
    c.top,
    c.theater_id,
    c.repertoire_id,
    c.repertoire_info_detail_id,
    #{userId} AS replyId
FROM
    t_comment AS c
    LEFT JOIN t_user AS u ON u.id = c.user_id
    LEFT JOIN t_repertoire AS r ON r.id = c.repertoire_id
    LEFT JOIN t_theater AS t ON t.id = c.theater_id
    LEFT JOIN t_repertoire_info_detail AS rid ON rid.id = c.repertoire_info_detail_id
    LEFT JOIN t_user_receiving_records AS urr ON urr.comment_id = c.id
    LEFT JOIN t_comment AS c1 ON c1.parent_id = c.id AND c1.deleted = 1
WHERE
    c.id = #{commentId}
    AND c.deleted = 1
GROUP BY
    c.id, c.user_id, u.`name`, u.avatar, c.ip_address, t.address,
    r.`name`, t.`name`, rid.start_time, rid.end_time, r.cover_picture, t.cover_picture,
    c.content, c.theater_content, c.comment_time, c.visible, c.deleted,
    c.`status`, c.top, c.theater_id, c.repertoire_id, c.repertoire_info_detail_id
```

### 回复评论查询修复
```sql
SELECT
    c.id,
    c.parent_id,
    c.comment_parent_id,
    c.user_id,
    u.`name` AS userName,
    u.avatar AS userAvatar,
    c.reply_id AS replyUserId,
    ru.`name` AS replyName,
    ru.avatar AS replyAvatar,
    c.ip_address,
    t.address AS location,
    ( CASE '0' WHEN c.deleted THEN '' WHEN c.`status` THEN '' ELSE c.content END ) AS content,
    c.comment_time AS createTime,
    ( SELECT COUNT(1) FROM t_comment_info WHERE comment_id = c.id AND `type` = 1 ) AS likeCount,
    ( SELECT COUNT(1) FROM t_comment_info WHERE comment_id = c.id AND `type` = 2 ) AS shareCount,
    ( SELECT COUNT(1) FROM t_comment_info WHERE comment_id = c.id AND `type` = 3 ) AS followCount,
    ( SELECT GROUP_CONCAT(`type`) FROM t_comment_info WHERE comment_id = c.id AND user_id = #{replyId} ) AS kudosStatus,
    ( SELECT COUNT(1) > 0 FROM t_comment_info WHERE comment_id = c.id AND user_id = #{replyId} AND `type` = 1 ) AS isLiked,
    ( SELECT COUNT(1) > 0 FROM t_comment_info WHERE comment_id = c.id AND user_id = #{replyId} AND `type` = 0 ) AS isDisliked,
    ( SELECT COUNT(1) > 0 FROM t_comment_info WHERE comment_id = c.id AND user_id = #{replyId} AND `type` = 2 ) AS isShared,
    ( SELECT COUNT(1) > 0 FROM t_comment_info WHERE comment_id = c.id AND user_id = #{replyId} AND `type` = 3 ) AS isFollowed,
    c.visible,
    c.deleted,
    c.`status`
FROM
    t_comment AS c
    LEFT JOIN t_user AS u ON u.id = c.user_id
    LEFT JOIN t_user AS ru ON ru.id = c.reply_id
    LEFT JOIN t_theater AS t ON t.id = c.theater_id
WHERE
    c.parent_id = #{id}
    AND c.deleted = 1
ORDER BY
    c.comment_time ASC
LIMIT 50
```

## 数据结构变化

### CommentResponse类扩展
```java
/**
 * 用户点赞详情，0为踩 1为赞 null为未操作
 * 现在支持多种操作状态的组合，格式为逗号分隔的字符串，如："1,2,3"
 */
private String kudosStatus;

/**
 * 用户是否点赞（true/false）
 */
private Boolean isLiked;

/**
 * 用户是否踩（true/false）
 */
private Boolean isDisliked;

/**
 * 用户是否转发（true/false）
 */
private Boolean isShared;

/**
 * 用户是否关注（true/false）
 */
private Boolean isFollowed;
```

## 前端使用建议

### 1. 使用布尔字段
推荐前端使用新的布尔字段，更直观易用：
```javascript
if (comment.isLiked) {
    // 显示已点赞状态
}
if (comment.isShared) {
    // 显示已转发状态
}
```

### 2. 解析kudosStatus字段
如果需要获取所有操作类型：
```javascript
const operations = comment.kudosStatus ? comment.kudosStatus.split(',').map(Number) : [];
const hasLike = operations.includes(1);
const hasShare = operations.includes(2);
const hasFollow = operations.includes(3);
```

## 性能考虑

### 1. 子查询优化
- 每个布尔字段都是独立的子查询
- 建议为`t_comment_info`表添加复合索引：`(comment_id, user_id, type)`

### 2. 索引建议
```sql
CREATE INDEX idx_comment_info_composite ON t_comment_info(comment_id, user_id, type);
```

## 测试验证

### 1. 单一操作测试
- 用户只点赞：`kudosStatus = "1"`, `isLiked = true`, 其他为false
- 用户只转发：`kudosStatus = "2"`, `isShared = true`, 其他为false

### 2. 多重操作测试
- 用户点赞+转发：`kudosStatus = "1,2"`, `isLiked = true`, `isShared = true`
- 用户所有操作：`kudosStatus = "0,1,2,3"`, 所有布尔字段为true

### 3. 边界情况测试
- 无操作：`kudosStatus = null`, 所有布尔字段为false
- 数据库中有脏数据的情况

## 总结

通过使用GROUP_CONCAT函数和添加独立的布尔字段，我们成功解决了子查询返回多行的问题，同时：

✅ **兼容性**：保持了原有的kudosStatus字段（格式有变化）
✅ **易用性**：新增布尔字段让前端使用更方便
✅ **完整性**：支持所有操作类型的状态查询
✅ **性能**：通过合理的索引可以保证查询性能

修复后的SQL查询现在可以正确处理用户的多种操作状态，不会再出现"Subquery returns more than 1 row"错误。
